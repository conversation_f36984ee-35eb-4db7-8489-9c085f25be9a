[21-May-2025 07:00:36 UTC] Removed updated post ID 629 from betterdocs_ai_chatbot_synced_posts
[21-May-2025 07:00:36 UTC] Added updated post ID 629 to saved_docs_post_ids for later syncing
[21-May-2025 07:01:04 UTC] Removed updated post ID 622 from betterdocs_ai_chatbot_synced_posts
[21-May-2025 07:01:04 UTC] Added updated post ID 622 to saved_docs_post_ids for later syncing
[21-May-2025 07:01:28 UTC] Removed updated post ID 619 from betterdocs_ai_chatbot_synced_posts
[21-May-2025 07:01:28 UTC] Added updated post ID 619 to saved_docs_post_ids for later syncing
[21-May-2025 07:02:24 UTC] Removed updated post ID 610 from betterdocs_ai_chatbot_synced_posts
[21-May-2025 07:02:24 UTC] Added updated post ID 610 to saved_docs_post_ids for later syncing
[21-May-2025 07:04:30 UTC] Added updated post ID 1622 to saved_docs_post_ids for later syncing
[21-May-2025 07:06:17 UTC] Skipping deletion of existing site collection
[21-May-2025 07:06:18 UTC] Sync New Docs: Called send_data with delete_existing=false
[21-May-2025 07:06:19 UTC] Found 5 posts to sync.
[21-May-2025 07:06:24 UTC] Successfully synced 5 posts. Total synced posts: 63
[21-May-2025 07:06:24 UTC] Removed 5 successfully synced posts from saved_docs_post_ids
[21-May-2025 07:06:24 UTC] Removed 3 successfully synced posts from betterdocs_ai_chatbot_error_posts
[21-May-2025 07:20:52 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/betterdocs/wp-admin/admin-header.php on line 36
[21-May-2025 07:21:09 UTC] Settings changed: embedding model changed: yes
[21-May-2025 07:21:12 UTC] Successfully deleted existing site collection
[21-May-2025 07:21:13 UTC] Successfully created site collection
[21-May-2025 07:21:28 UTC] Successfully synced 20 posts. Total synced posts: 63
[21-May-2025 07:21:40 UTC] Successfully synced 20 posts. Total synced posts: 63
[21-May-2025 07:21:52 UTC] Successfully synced 20 posts. Total synced posts: 63
[21-May-2025 07:21:55 UTC] Successfully synced 3 posts. Total synced posts: 63
[21-May-2025 07:30:25 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:30:58 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:30:58 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:30:58 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:30:59 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:30:59 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:30:59 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:31:01 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:31:01 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:31:01 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:31:05 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:31:05 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:31:05 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:31:24 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:31:59 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 07:32:15 UTC] PHP Parse error:  syntax error, unexpected variable "$create_response", expecting "function" or "const" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/AIChatbot.php on line 390
[21-May-2025 12:08:57 UTC] Automatic updates starting...
[21-May-2025 12:09:01 UTC]   Automatic plugin updates starting...
[21-May-2025 12:09:01 UTC]   Automatic plugin updates complete.
[21-May-2025 12:09:01 UTC]   Automatic theme updates starting...
[21-May-2025 12:09:01 UTC]   Automatic theme updates complete.
[21-May-2025 12:09:01 UTC] Automatic updates complete.
[21-May-2025 12:43:16 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function DI\autowire() in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/config.php:16
Stack trace:
#0 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php(210): require_once()
#1 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(324): WPDeveloper\BetterDocsChatbot\Plugin->container_config(Array)
#2 /Users/<USER>/Sites/betterdocs/wp-includes/plugin.php(205): WP_Hook->apply_filters(Array, Array)
#3 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Plugin.php(205): apply_filters('betterdocs_cont...', Array)
#4 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Plugin.php(140): WPDeveloper\BetterDocs\Plugin->setup_container()
#5 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Plugin.php(300): WPDeveloper\BetterDocs\Plugin->__construct()
#6 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/betterdocs.php(41): WPDeveloper\BetterDocs\Plugin::get_instance()
#7 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/betterdocs.php(54): betterdocs()
#8 /Users/<USER>/Sites/betterdocs/wp-settings.php(526): include_once('/Users/<USER>/Si...')
#9 phar:///usr/local/bin/wp/vendor/wp-cli/wp-cli/php/WP_CLI/Runner.php(1375): require('/Users/<USER>/Si...')
#10 phar:///usr/local/bin/wp/vendor/wp-cli/wp-cli/php/WP_CLI/Runner.php(1294): WP_CLI\Runner->load_wordpress()
#11 phar:///usr/local/bin/wp/vendor/wp-cli/wp-cli/php/WP_CLI/Bootstrap/LaunchRunner.php(28): WP_CLI\Runner->start()
#12 phar:///usr/local/bin/wp/vendor/wp-cli/wp-cli/php/bootstrap.php(83): WP_CLI\Bootstrap\LaunchRunner->process(Object(WP_CLI\Bootstrap\BootstrapState))
#13 phar:///usr/local/bin/wp/vendor/wp-cli/wp-cli/php/wp-cli.php(32): WP_CLI\bootstrap()
#14 phar:///usr/local/bin/wp/php/boot-phar.php(20): include('phar:///usr/loc...')
#15 /usr/local/bin/wp(4): include('phar:///usr/loc...')
#16 {main}
  thrown in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/config.php on line 16
[22-May-2025 01:54:59 UTC] Automatic updates starting...
[22-May-2025 02:11:18 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/betterdocs/wp-includes/functions.php on line 6114
[22-May-2025 02:11:21 UTC]   Automatic plugin updates starting...
[22-May-2025 02:11:21 UTC]   Automatic plugin updates complete.
[22-May-2025 02:11:21 UTC]   Automatic theme updates starting...
[22-May-2025 02:11:21 UTC]   Automatic theme updates complete.
[22-May-2025 02:11:21 UTC] Automatic updates complete.
[22-May-2025 02:24:48 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/betterdocs/wp-includes/functions.php on line 6114
[22-May-2025 02:40:54 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/betterdocs/wp-includes/functions.php on line 6114
[22-May-2025 02:57:12 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/betterdocs/wp-includes/functions.php on line 6114
[22-May-2025 06:49:02 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/query-monitor/collectors/php_errors.php on line 364
[22-May-2025 06:49:02 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/query-monitor/collectors/php_errors.php on line 364
[22-May-2025 06:49:02 UTC] PHP Parse error:  syntax error, unexpected token ":", expecting "," or ";" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Shortcodes/ChatbotPreview.php on line 52
[22-May-2025 06:49:06 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/query-monitor/collectors/php_errors.php on line 364
[22-May-2025 06:49:06 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/query-monitor/collectors/php_errors.php on line 364
[22-May-2025 06:49:06 UTC] PHP Parse error:  syntax error, unexpected token ":", expecting "," or ";" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Shortcodes/ChatbotPreview.php on line 52
[22-May-2025 06:57:01 UTC] ChatbotPreview render method called
[22-May-2025 06:57:01 UTC] Attributes: Array
(
    [title] => We typically reply in a few minutes
    [subtitle] => We help your business grow by connecting you to your customers.
    [welcome_message] => Hii, welcome to BetterDocs 👋 You’re speaking with AI Agent - I’m here to answer your questions & help you out.
    [class] => 
)

[22-May-2025 06:57:01 UTC] Views object found in betterdocs_chatbot()
[22-May-2025 06:57:01 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 06:57:01 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 06:57:01 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 06:57:01 UTC] File exists: Yes
[22-May-2025 07:00:52 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 07:00:52 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 07:00:52 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 07:00:52 UTC] File exists: Yes
[22-May-2025 07:01:08 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 07:01:08 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 07:01:08 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 07:01:08 UTC] File exists: Yes
[22-May-2025 07:05:25 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method WPDeveloper\BetterDocsChatbot\Shortcodes\ChatbotPreview::get_name() in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/ShortcodeFactory.php:42
Stack trace:
#0 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php(332): WPDeveloper\BetterDocsChatbot\Core\ShortcodeFactory->init()
#1 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(324): WPDeveloper\BetterDocsChatbot\Plugin->load_core_services('')
#2 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#3 /Users/<USER>/Sites/betterdocs/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#4 /Users/<USER>/Sites/betterdocs/wp-settings.php(704): do_action('init')
#5 /Users/<USER>/Sites/betterdocs/wp-config.php(99): require_once('/Users/<USER>/Si...')
#6 /Users/<USER>/Sites/betterdocs/wp-load.php(50): require_once('/Users/<USER>/Si...')
#7 /Users/<USER>/Sites/betterdocs/wp-blog-header.php(13): require_once('/Users/<USER>/Si...')
#8 /Users/<USER>/Sites/betterdocs/index.php(17): require('/Users/<USER>/Si...')
#9 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>/Si...')
#10 {main}
  thrown in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/ShortcodeFactory.php on line 42
[22-May-2025 07:05:26 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method WPDeveloper\BetterDocsChatbot\Shortcodes\ChatbotPreview::get_name() in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/ShortcodeFactory.php:42
Stack trace:
#0 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php(332): WPDeveloper\BetterDocsChatbot\Core\ShortcodeFactory->init()
#1 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(324): WPDeveloper\BetterDocsChatbot\Plugin->load_core_services('')
#2 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#3 /Users/<USER>/Sites/betterdocs/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#4 /Users/<USER>/Sites/betterdocs/wp-settings.php(704): do_action('init')
#5 /Users/<USER>/Sites/betterdocs/wp-config.php(99): require_once('/Users/<USER>/Si...')
#6 /Users/<USER>/Sites/betterdocs/wp-load.php(50): require_once('/Users/<USER>/Si...')
#7 /Users/<USER>/Sites/betterdocs/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>/Si...')
#8 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>/Si...')
#9 {main}
  thrown in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/ShortcodeFactory.php on line 42
[22-May-2025 07:05:37 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method WPDeveloper\BetterDocsChatbot\Shortcodes\ChatbotPreview::get_name() in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/ShortcodeFactory.php:42
Stack trace:
#0 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php(332): WPDeveloper\BetterDocsChatbot\Core\ShortcodeFactory->init()
#1 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(324): WPDeveloper\BetterDocsChatbot\Plugin->load_core_services('')
#2 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#3 /Users/<USER>/Sites/betterdocs/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#4 /Users/<USER>/Sites/betterdocs/wp-settings.php(704): do_action('init')
#5 /Users/<USER>/Sites/betterdocs/wp-config.php(99): require_once('/Users/<USER>/Si...')
#6 /Users/<USER>/Sites/betterdocs/wp-load.php(50): require_once('/Users/<USER>/Si...')
#7 /Users/<USER>/Sites/betterdocs/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>/Si...')
#8 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>/Si...')
#9 {main}
  thrown in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/ShortcodeFactory.php on line 42
[22-May-2025 07:07:12 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 07:07:12 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 07:07:12 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 07:07:12 UTC] File exists: Yes
[22-May-2025 07:09:45 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 07:09:45 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 07:09:45 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 07:09:45 UTC] File exists: Yes
[22-May-2025 07:12:45 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 07:12:45 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 07:12:45 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 07:12:45 UTC] File exists: Yes
[22-May-2025 07:15:16 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 07:15:16 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 07:15:16 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 07:15:16 UTC] File exists: Yes
[22-May-2025 07:17:41 UTC] PHP Fatal error:  Uncaught WPDeveloper\BetterDocs\Dependencies\DI\NotFoundException: No entry or class found for 'WPDeveloper\BetterDocsChatbot\Core\DirectChatbotPreview' in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Dependencies/DI/Container.php:135
Stack trace:
#0 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/ShortcodeFactory.php(42): WPDeveloper\BetterDocs\Dependencies\DI\Container->get('WPDeveloper\\Bet...')
#1 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php(332): WPDeveloper\BetterDocsChatbot\Core\ShortcodeFactory->init()
#2 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(324): WPDeveloper\BetterDocsChatbot\Plugin->load_core_services('')
#3 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#4 /Users/<USER>/Sites/betterdocs/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#5 /Users/<USER>/Sites/betterdocs/wp-settings.php(704): do_action('init')
#6 /Users/<USER>/Sites/betterdocs/wp-config.php(99): require_once('/Users/<USER>/Si...')
#7 /Users/<USER>/Sites/betterdocs/wp-load.php(50): require_once('/Users/<USER>/Si...')
#8 /Users/<USER>/Sites/betterdocs/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>/Si...')
#9 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>/Si...')
#10 {main}
  thrown in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Dependencies/DI/Container.php on line 135
[22-May-2025 07:17:41 UTC] PHP Fatal error:  Uncaught WPDeveloper\BetterDocs\Dependencies\DI\NotFoundException: No entry or class found for 'WPDeveloper\BetterDocsChatbot\Core\DirectChatbotPreview' in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Dependencies/DI/Container.php:135
Stack trace:
#0 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/ShortcodeFactory.php(42): WPDeveloper\BetterDocs\Dependencies\DI\Container->get('WPDeveloper\\Bet...')
#1 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php(332): WPDeveloper\BetterDocsChatbot\Core\ShortcodeFactory->init()
#2 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(324): WPDeveloper\BetterDocsChatbot\Plugin->load_core_services('')
#3 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#4 /Users/<USER>/Sites/betterdocs/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#5 /Users/<USER>/Sites/betterdocs/wp-settings.php(704): do_action('init')
#6 /Users/<USER>/Sites/betterdocs/wp-config.php(99): require_once('/Users/<USER>/Si...')
#7 /Users/<USER>/Sites/betterdocs/wp-load.php(50): require_once('/Users/<USER>/Si...')
#8 /Users/<USER>/Sites/betterdocs/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>/Si...')
#9 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>/Si...')
#10 {main}
  thrown in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Dependencies/DI/Container.php on line 135
[22-May-2025 07:17:43 UTC] PHP Fatal error:  Uncaught WPDeveloper\BetterDocs\Dependencies\DI\NotFoundException: No entry or class found for 'WPDeveloper\BetterDocsChatbot\Core\DirectChatbotPreview' in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Dependencies/DI/Container.php:135
Stack trace:
#0 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Core/ShortcodeFactory.php(42): WPDeveloper\BetterDocs\Dependencies\DI\Container->get('WPDeveloper\\Bet...')
#1 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php(332): WPDeveloper\BetterDocsChatbot\Core\ShortcodeFactory->init()
#2 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(324): WPDeveloper\BetterDocsChatbot\Plugin->load_core_services('')
#3 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#4 /Users/<USER>/Sites/betterdocs/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#5 /Users/<USER>/Sites/betterdocs/wp-settings.php(704): do_action('init')
#6 /Users/<USER>/Sites/betterdocs/wp-config.php(99): require_once('/Users/<USER>/Si...')
#7 /Users/<USER>/Sites/betterdocs/wp-load.php(50): require_once('/Users/<USER>/Si...')
#8 /Users/<USER>/Sites/betterdocs/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>/Si...')
#9 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>/Si...')
#10 {main}
  thrown in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Dependencies/DI/Container.php on line 135
[22-May-2025 07:20:32 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 07:20:32 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 07:20:32 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 07:20:32 UTC] File exists: Yes
[22-May-2025 07:21:39 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 07:21:39 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 07:21:39 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 07:21:39 UTC] File exists: Yes
[22-May-2025 07:22:54 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 07:22:54 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 07:22:54 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 07:22:54 UTC] File exists: Yes
[22-May-2025 09:04:52 UTC] PHP Fatal error:  Uncaught ArgumentCountError: Too few arguments to function WPDeveloper\BetterDocs\Utils\Enqueue::enqueue(), 1 passed in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php on line 105 and at least 2 expected in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Utils/Enqueue.php:15
Stack trace:
#0 /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php(105): WPDeveloper\BetterDocs\Utils\Enqueue->enqueue('betterdocs-ai-c...')
#1 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(324): WPDeveloper\BetterDocsChatbot\Plugin->public_enqueue_scripts('')
#2 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#3 /Users/<USER>/Sites/betterdocs/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#4 /Users/<USER>/Sites/betterdocs/wp-includes/script-loader.php(2272): do_action('wp_enqueue_scri...')
#5 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')
#6 /Users/<USER>/Sites/betterdocs/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#7 /Users/<USER>/Sites/betterdocs/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#8 /Users/<USER>/Sites/betterdocs/wp-includes/general-template.php(3064): do_action('wp_head')
#9 /Users/<USER>/Sites/betterdocs/wp-content/themes/hello-elementor/header.php(24): wp_head()
#10 /Users/<USER>/Sites/betterdocs/wp-includes/template.php(810): require_once('/Users/<USER>/Si...')
#11 /Users/<USER>/Sites/betterdocs/wp-includes/template.php(745): load_template('/Users/<USER>/Si...', true, Array)
#12 /Users/<USER>/Sites/betterdocs/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)
#13 /Users/<USER>/Sites/betterdocs/wp-content/themes/hello-elementor/index.php(15): get_header()
#14 /Users/<USER>/Sites/betterdocs/wp-includes/template-loader.php(106): include('/Users/<USER>/Si...')
#15 /Users/<USER>/Sites/betterdocs/wp-blog-header.php(19): require_once('/Users/<USER>/Si...')
#16 /Users/<USER>/Sites/betterdocs/index.php(17): require('/Users/<USER>/Si...')
#17 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>/Si...')
#18 {main}
  thrown in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs/includes/Utils/Enqueue.php on line 15
[22-May-2025 09:16:57 UTC] PHP Parse error:  Unclosed '{' on line 311 does not match ')' in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Plugin.php on line 336
[22-May-2025 09:18:54 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:18:54 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:18:54 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:18:54 UTC] File exists: Yes
[22-May-2025 09:21:29 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:21:29 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:21:29 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:21:29 UTC] File exists: Yes
[22-May-2025 09:22:43 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/query-monitor/collectors/php_errors.php on line 364
[22-May-2025 09:22:43 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/query-monitor/collectors/php_errors.php on line 364
[22-May-2025 09:22:43 UTC] PHP Parse error:  Unclosed '{' on line 40 in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Shortcodes/ChatbotPreview.php on line 76
[22-May-2025 09:22:56 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:22:56 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:22:56 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:22:56 UTC] File exists: Yes
[22-May-2025 09:40:24 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:40:24 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:40:24 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:40:24 UTC] File exists: Yes
[22-May-2025 09:40:57 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:40:57 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:40:57 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:40:57 UTC] File exists: Yes
[22-May-2025 09:45:41 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:45:41 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:45:41 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:45:41 UTC] File exists: Yes
[22-May-2025 09:45:44 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:45:44 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:45:44 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:45:44 UTC] File exists: Yes
[22-May-2025 09:46:00 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:46:00 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:46:00 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:46:00 UTC] File exists: Yes
[22-May-2025 09:47:13 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:47:13 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:47:13 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:47:13 UTC] File exists: Yes
[22-May-2025 09:50:05 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:50:05 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:50:05 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:50:05 UTC] File exists: Yes
[22-May-2025 09:50:07 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:50:07 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:50:07 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:50:07 UTC] File exists: Yes
[22-May-2025 09:56:49 UTC] Views path method called with name: shortcodes/chatbot-preview
[22-May-2025 09:56:49 UTC] Views path: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/
[22-May-2025 09:56:49 UTC] Filename: /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/views/shortcodes/chatbot-preview.php
[22-May-2025 09:56:49 UTC] File exists: Yes
[22-May-2025 10:44:52 UTC] PHP Notice:  Function WP_Block_Type_Registry::register was called <strong>incorrectly</strong>. Block type names must not contain uppercase characters. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 5.0.0.) in /Users/<USER>/Sites/betterdocs/wp-includes/functions.php on line 6114
[22-May-2025 12:02:26 UTC] Automatic updates starting...
[22-May-2025 12:02:29 UTC]   Automatic plugin updates starting...
[22-May-2025 12:02:29 UTC]   Automatic plugin updates complete.
[22-May-2025 12:02:29 UTC]   Automatic theme updates starting...
[22-May-2025 12:02:29 UTC]   Automatic theme updates complete.
[22-May-2025 12:02:29 UTC] Automatic updates complete.
[25-May-2025 03:37:51 UTC] Automatic updates starting...
[25-May-2025 03:37:55 UTC]   Automatic plugin updates starting...
[25-May-2025 03:37:55 UTC]   Automatic plugin updates complete.
[25-May-2025 03:37:55 UTC]   Automatic theme updates starting...
[25-May-2025 03:37:55 UTC]   Automatic theme updates complete.
[25-May-2025 03:37:55 UTC] Automatic updates complete.
[25-May-2025 04:30:26 UTC] PHP Warning:  Undefined array key "primary_color" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Editors/Elementor/Widget/ChatbotPreview.php on line 155
[25-May-2025 04:30:59 UTC] PHP Warning:  Undefined array key "primary_color" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Editors/Elementor/Widget/ChatbotPreview.php on line 155
[25-May-2025 04:42:17 UTC] PHP Warning:  foreach() argument must be of type array|object, string given in /Users/<USER>/Sites/betterdocs/wp-content/plugins/elementor/includes/base/element-base.php on line 137
[25-May-2025 04:42:42 UTC] PHP Warning:  foreach() argument must be of type array|object, string given in /Users/<USER>/Sites/betterdocs/wp-content/plugins/elementor/includes/base/element-base.php on line 137
[25-May-2025 04:46:12 UTC] PHP Warning:  foreach() argument must be of type array|object, string given in /Users/<USER>/Sites/betterdocs/wp-content/plugins/elementor/includes/base/element-base.php on line 137
[25-May-2025 06:50:57 UTC] PHP Warning:  foreach() argument must be of type array|object, string given in /Users/<USER>/Sites/betterdocs/wp-content/plugins/elementor/includes/base/element-base.php on line 137
[25-May-2025 06:58:09 UTC] PHP Warning:  Undefined array key "primary_color" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Editors/Elementor/Widget/ChatbotPreview.php on line 162
[25-May-2025 06:58:09 UTC] PHP Warning:  foreach() argument must be of type array|object, string given in /Users/<USER>/Sites/betterdocs/wp-content/plugins/elementor/includes/base/element-base.php on line 137
[25-May-2025 06:59:09 UTC] PHP Warning:  Undefined array key "primary_color" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Editors/Elementor/Widget/ChatbotPreview.php on line 162
[25-May-2025 06:59:09 UTC] PHP Warning:  foreach() argument must be of type array|object, string given in /Users/<USER>/Sites/betterdocs/wp-content/plugins/elementor/includes/base/element-base.php on line 137
[25-May-2025 06:59:12 UTC] PHP Warning:  Undefined array key "primary_color" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Editors/Elementor/Widget/ChatbotPreview.php on line 162
[25-May-2025 06:59:12 UTC] PHP Warning:  foreach() argument must be of type array|object, string given in /Users/<USER>/Sites/betterdocs/wp-content/plugins/elementor/includes/base/element-base.php on line 137
[25-May-2025 07:29:32 UTC] PHP Warning:  Undefined array key "primary_color" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Editors/Elementor/Widget/ChatbotPreview.php on line 144
[25-May-2025 07:30:52 UTC] PHP Warning:  Undefined array key "primary_color" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Editors/Elementor/Widget/ChatbotPreview.php on line 144
[25-May-2025 09:34:34 UTC] PHP Warning:  Undefined array key "primary_color" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Editors/Elementor/Widget/ChatbotPreview.php on line 180
[25-May-2025 09:40:50 UTC] PHP Warning:  Undefined array key "primary_color" in /Users/<USER>/Sites/betterdocs/wp-content/plugins/betterdocs-ai-chatbot/includes/Editors/Elementor/Widget/ChatbotPreview.php on line 139
[25-May-2025 10:27:36 UTC] Settings changed: embedding model changed: yes
[25-May-2025 12:02:24 UTC] Automatic updates starting...
[25-May-2025 12:02:27 UTC]   Automatic plugin updates starting...
[25-May-2025 12:02:27 UTC]   Automatic plugin updates complete.
[25-May-2025 12:02:28 UTC]   Automatic theme updates starting...
[25-May-2025 12:02:28 UTC]   Automatic theme updates complete.
[25-May-2025 12:02:28 UTC] Automatic updates complete.
[25-May-2025 12:32:34 UTC] Settings changed: embedding model changed: yes
