<?php
/**
 * Plugin Name: Feedlane Debug
 * Description: Debug helper for Feedlane plugin
 * Version: 1.0.0
 */

// Add debug menu
add_action('admin_menu', function() {
    add_menu_page(
        'Feedlane Debug',
        'Feedlane Debug',
        'manage_options',
        'feedlane-debug',
        'feedlane_debug_page',
        'dashicons-admin-tools',
        99
    );
});

function feedlane_debug_page() {
    ?>
    <div class="wrap">
        <h1>Feedlane Debug Information</h1>
        
        <h2>Plugin Status</h2>
        <table class="widefat striped">
            <tr>
                <td><strong>Plugin Active:</strong></td>
                <td><?php echo is_plugin_active('feedlane/feedlane.php') ? '✅ YES' : '❌ NO'; ?></td>
            </tr>
            <tr>
                <td><strong>Main Class Exists:</strong></td>
                <td><?php echo class_exists('WPDeveloper\Feedlane\Plugin') ? '✅ YES' : '❌ NO'; ?></td>
            </tr>
            <tr>
                <td><strong>Plugin Instance:</strong></td>
                <td><?php echo function_exists('feedlane') ? '✅ YES' : '❌ NO'; ?></td>
            </tr>
            <tr>
                <td><strong>Autoloader:</strong></td>
                <td><?php echo file_exists(WP_PLUGIN_DIR . '/feedlane/vendor/autoload.php') ? '✅ EXISTS' : '❌ MISSING'; ?></td>
            </tr>
        </table>
        
        <h2>Post Types</h2>
        <table class="widefat striped">
            <?php
            $post_types = ['feedlane_posts', 'feedlane_ideas'];
            foreach ($post_types as $post_type) {
                echo '<tr>';
                echo '<td><strong>' . $post_type . ':</strong></td>';
                echo '<td>' . (post_type_exists($post_type) ? '✅ REGISTERED' : '❌ NOT FOUND') . '</td>';
                echo '</tr>';
            }
            ?>
        </table>
        
        <h2>Taxonomies</h2>
        <table class="widefat striped">
            <?php
            $taxonomies = ['idea_category', 'roadmap_status'];
            foreach ($taxonomies as $taxonomy) {
                echo '<tr>';
                echo '<td><strong>' . $taxonomy . ':</strong></td>';
                echo '<td>' . (taxonomy_exists($taxonomy) ? '✅ REGISTERED' : '❌ NOT FOUND') . '</td>';
                echo '</tr>';
            }
            ?>
        </table>
        
        <h2>Admin Menus</h2>
        <table class="widefat striped">
            <?php
            global $menu, $submenu;
            $feedlane_found = false;
            
            if (isset($menu)) {
                foreach ($menu as $menu_item) {
                    if (isset($menu_item[2]) && strpos($menu_item[2], 'feedlane') !== false) {
                        $feedlane_found = true;
                        echo '<tr>';
                        echo '<td><strong>Menu:</strong></td>';
                        echo '<td>✅ ' . $menu_item[0] . ' (' . $menu_item[2] . ')</td>';
                        echo '</tr>';
                    }
                }
            }
            
            if (!$feedlane_found) {
                echo '<tr><td colspan="2">❌ No Feedlane menus found</td></tr>';
            }
            
            if (isset($submenu['feedlane'])) {
                foreach ($submenu['feedlane'] as $submenu_item) {
                    echo '<tr>';
                    echo '<td><strong>Submenu:</strong></td>';
                    echo '<td>✅ ' . $submenu_item[0] . ' (' . $submenu_item[2] . ')</td>';
                    echo '</tr>';
                }
            }
            ?>
        </table>
        
        <h2>Hooks Debug</h2>
        <table class="widefat striped">
            <tr>
                <td><strong>Current Hook:</strong></td>
                <td><?php echo current_action() ?: 'None'; ?></td>
            </tr>
            <tr>
                <td><strong>Init Hook Fired:</strong></td>
                <td><?php echo did_action('init') ? '✅ YES (' . did_action('init') . ' times)' : '❌ NO'; ?></td>
            </tr>
            <tr>
                <td><strong>Admin Menu Hook Fired:</strong></td>
                <td><?php echo did_action('admin_menu') ? '✅ YES (' . did_action('admin_menu') . ' times)' : '❌ NO'; ?></td>
            </tr>
        </table>
        
        <h2>Assets Check</h2>
        <table class="widefat striped">
            <?php
            $assets = [
                'js/feedlane-sidebar.min.js',
                'css/feedlane-sidebar.min.css',
                'js/feedlane-admin.min.js',
                'css/feedlane-admin.min.css',
            ];
            
            $assets_dir = WP_PLUGIN_DIR . '/feedlane/assets/';
            foreach ($assets as $asset) {
                $file_path = $assets_dir . $asset;
                echo '<tr>';
                echo '<td><strong>' . $asset . ':</strong></td>';
                echo '<td>' . (file_exists($file_path) ? '✅ EXISTS (' . round(filesize($file_path)/1024, 1) . 'KB)' : '❌ NOT FOUND') . '</td>';
                echo '</tr>';
            }
            ?>
        </table>
        
        <h2>Actions</h2>
        <p>
            <a href="<?php echo admin_url('plugins.php'); ?>" class="button">Go to Plugins</a>
            <a href="<?php echo admin_url('admin.php?page=feedlane'); ?>" class="button button-primary">Try Feedlane Dashboard</a>
            <button onclick="location.reload()" class="button">Refresh Debug Info</button>
        </p>
        
        <h2>Manual Tests</h2>
        <p>
            <a href="<?php echo admin_url('admin.php?page=feedlane-debug&action=test_activation'); ?>" class="button">Test Plugin Activation</a>
            <a href="<?php echo admin_url('admin.php?page=feedlane-debug&action=test_classes'); ?>" class="button">Test Classes</a>
        </p>
        
        <?php
        if (isset($_GET['action'])) {
            echo '<h2>Test Results</h2>';
            echo '<div style="background: #f1f1f1; padding: 10px; margin: 10px 0;">';
            
            switch ($_GET['action']) {
                case 'test_activation':
                    echo '<h3>Testing Plugin Activation</h3>';
                    if (!is_plugin_active('feedlane/feedlane.php')) {
                        $result = activate_plugin('feedlane/feedlane.php');
                        if (is_wp_error($result)) {
                            echo '❌ Error: ' . $result->get_error_message();
                        } else {
                            echo '✅ Plugin activated successfully!';
                        }
                    } else {
                        echo '✅ Plugin is already active';
                    }
                    break;
                    
                case 'test_classes':
                    echo '<h3>Testing Classes</h3>';
                    $classes = [
                        'WPDeveloper\Feedlane\Plugin',
                        'WPDeveloper\Feedlane\Core\Admin',
                        'WPDeveloper\Feedlane\Core\PostTypes',
                    ];
                    
                    foreach ($classes as $class) {
                        echo $class . ': ' . (class_exists($class) ? '✅ EXISTS' : '❌ NOT FOUND') . '<br>';
                    }
                    break;
            }
            
            echo '</div>';
        }
        ?>
    </div>
    <?php
}
?>
