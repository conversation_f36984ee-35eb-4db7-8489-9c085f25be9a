const path = require("path");
const defaultConfig = require("@wordpress/scripts/config/webpack.config");
const MiniCSSExtractPlugin = require("mini-css-extract-plugin");
const RemoveEmptyScriptsPlugin = require("webpack-remove-empty-scripts");
const CopyPlugin = require("copy-webpack-plugin");

const isProduction = process.env.NODE_ENV === "production";

const plugins = defaultConfig.plugins.filter(
    (plugin) =>
        plugin.constructor.name !== "MiniCssExtractPlugin" &&
        plugin.constructor.name !== "CleanWebpackPlugin"
);

const config = {
    ...defaultConfig,
    entry: {
        ['js/betterdocs.chatbot-history.min'] : './react-src/index.js',
        ['js/betterdocs.chatbot-preview.min'] : './react-src/ai-chatbot-preview/Preview.js',
        ['css/betterdocs.chatbot-history.min']: "./react-src/ai-chatbot-history/scss/ai-chatbot.scss",
        ['css/betterdocs.chatbot-preview.min']: "./react-src/ai-chatbot-preview/scss/chatbot.scss",
    },
    module: {
        ...defaultConfig.module,
        rules: [...defaultConfig.module.rules],
    },
    output: {
        path: path.join(__dirname, "assets/"),
        filename: "[name].js",
    },
    plugins: [
        ...plugins,
        new RemoveEmptyScriptsPlugin(),
        new MiniCSSExtractPlugin({
            filename: ({chunk}) =>{
                return `${chunk.name.replace("/js/", "/css/")}.css`;
            }
        })
    ],
};

module.exports = config;
