<?php

namespace WPDeveloper\BetterDocsChatbot\REST;

use WPD<PERSON>loper\BetterDocsChatbot\Core\AIChatbot;
use WPDeveloper\BetterDocsChatbot\Utils\Helper;
use WPDeveloper\BetterDocs\Core\BaseAPI;
use Exception;
use WP_Error;
use WP_REST_Request;
use WP_REST_Response;
use WP_REST_Server;

/**
 * @property string $namespace
 */
#[\AllowDynamicProperties]
class ChatBot<PERSON><PERSON> extends BaseAPI {
	protected $version = 'v1';
    protected $namespace = 'betterdocs-pro';

    public function permission_check(): bool {
		return current_user_can( 'edit_docs_settings' );
	}

    public function register() {
        // Disable nonce verification for our endpoints
        add_filter('rest_authentication_errors', function($result) {
            // Only bypass for our specific endpoints
            if (!empty($_SERVER['REQUEST_URI'])) {
                $request_uri = $_SERVER['REQUEST_URI'];
                $our_endpoints = [
                    'betterdocs-pro/v1/save-conversation',
                    'betterdocs-pro/v1/query-post',
                    'betterdocs-pro/v1/get-current-user-conversation',
                    'betterdocs-pro/v1/update-online-status'
                    // 'get-chats' is intentionally excluded to require authentication
                ];

                foreach ($our_endpoints as $endpoint) {
                    if (strpos($request_uri, $endpoint) !== false) {
                        // This is one of our endpoints, bypass authentication
                        return true;
                    }
                }
            }

            // For all other endpoints, use default authentication
            return $result;
        }, 20); // Higher priority to override default

        // Add CORS headers for REST API requests
        add_action('rest_api_init', function() {
            remove_filter('rest_pre_serve_request', 'rest_send_cors_headers');
            add_filter('rest_pre_serve_request', function($served, $result) {
                // Allow requests from any origin
                header('Access-Control-Allow-Origin: *');
                header('Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE');
                header('Access-Control-Allow-Credentials: true');
                header('Access-Control-Allow-Headers: Authorization, X-WP-Nonce, Content-Type, Accept, Origin');

                // Handle preflight requests
                if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
                    header('Access-Control-Max-Age: 86400'); // 24 hours
                    exit;
                }

                return $served;
            }, 10, 2);
        });

        register_rest_route(
            $this->namespace . '/' . $this->version,
            '/get-chats',
            [
                'methods'             => WP_REST_Server::ALLMETHODS,
                'callback'            => [$this, 'get_chats'],
                'permission_callback' => [$this, 'permission_check']
            ]
        );

        register_rest_route(
            $this->namespace . '/' . $this->version,
            '/get-current-user-conversation',
            [
                'methods'             => WP_REST_Server::ALLMETHODS,
                'callback'            => [$this, 'get_current_user_conversation'],
                'permission_callback' => '__return_true'
            ]
        );

        // Add the rest of your routes similarly
        register_rest_route(
            $this->namespace . '/' . $this->version,
            '/save-conversation',
            [
                'methods'             => WP_REST_Server::ALLMETHODS,
                'callback'            => [$this, 'save_conversation_data'],
                'permission_callback' => '__return_true'
            ]
        );

        register_rest_route(
            $this->namespace . '/' . $this->version,
            '/query-post',
            [
                'methods'             => WP_REST_Server::ALLMETHODS,
                'callback'            => [$this, 'query_post_to_server'],
                'permission_callback' => '__return_true'
            ]
        );

        register_rest_route(
            $this->namespace . '/' . $this->version,
            '/update-online-status',
            [
                'methods'             => WP_REST_Server::ALLMETHODS,
                'callback'            => [$this, 'update_online_status'],
                'permission_callback' => '__return_true'
            ]
        );

        register_rest_route(
            $this->namespace . '/' . $this->version,
            '/get-online-status',
            [
                'methods'             => WP_REST_Server::ALLMETHODS,
                'callback'            => [$this, 'get_online_status'],
                'permission_callback' => [$this, 'permission_check']
            ]
        );
    }


	public function get_chats(WP_REST_Request $request)
	{
		global $wpdb;
		$filter_term = sanitize_text_field($request->get_param('filter_term'));
		$offset = intval($request->get_param('offset'));
		$limit = intval($request->get_param('limit'));

		$conversations_table = $wpdb->prefix . 'betterdocs_ai_chatbot_conversations';
		$sessions_table = $wpdb->prefix . 'betterdocs_ai_chatbot_sessions';
		$users_table = $wpdb->prefix . 'betterdocs_ai_chatbot_users';

		// Base query
		$query = "
            SELECT
                c.id AS conversation_id,
                c.session_id,
                c.conversation,
                c.created_at AS conversation_created_at,
                s.user_id,
                s.device_info,
                s.main_information,
                s.created_at AS session_created_at,
                u.email AS user_email,
                u.profile_info
            FROM $conversations_table c
            INNER JOIN $sessions_table s ON c.session_id = s.id
            INNER JOIN $users_table u ON s.user_id = u.id
        ";

		// Filter by term
		if (!empty($filter_term)) {
			$query .= $wpdb->prepare("
            WHERE
                LOWER(u.email) LIKE %s OR
                LOWER(s.main_information) LIKE %s OR
                LOWER(c.conversation) LIKE %s
        ", '%' . $wpdb->esc_like($filter_term) . '%', '%' . $wpdb->esc_like($filter_term) . '%', '%' . $wpdb->esc_like($filter_term) . '%');
		}

		// Apply pagination only if filter term is not empty
		if (empty($filter_term)) {
			$query .= " ORDER BY c.created_at DESC LIMIT %d OFFSET %d";
			$query = $wpdb->prepare($query, $limit, $offset);
		} else {
			$query .= " ORDER BY c.created_at DESC";
		}

		$chats = $wpdb->get_results($query, ARRAY_A);

		if (empty($chats)) {
			return new WP_REST_Response([], 200);
		}

		foreach ($chats as &$chat) {
			$chat['conversation'] = json_decode($chat['conversation'], true);
			$chat['device_info'] = json_decode($chat['device_info'], true);
			$chat['main_information'] = json_decode($chat['main_information'], true);
			$chat['profile_info'] = json_decode($chat['profile_info'], true);

			// Highlight the filter term in conversation texts, if provided
			if ($filter_term && isset($chat['conversation'])) {
				$filter_term_lower = strtolower($filter_term);

				foreach ($chat['conversation'] as &$conversation) {
					if (isset($conversation['text'])) {
						// Highlight without escaping HTML characters
						$conversation['text'] = str_ireplace(
							$filter_term,
							'<span class="highlight">' . $filter_term . '</span>',
							$conversation['text']
						);
					}
				}
			}
		}

		return new WP_REST_Response($chats, 200);
	}

	public function get_current_user_conversation(WP_REST_Request $request)
	{
		$user_email = sanitize_email($request->get_param('user_email'));
		$filter_term = sanitize_text_field($request->get_param('filter_term'));
		$session_id = sanitize_text_field($request->get_param('session_id')); // For session-specific data

		global $wpdb;
		$users_table = "{$wpdb->prefix}betterdocs_ai_chatbot_users";
		$sessions_table = "{$wpdb->prefix}betterdocs_ai_chatbot_sessions";
		$conversations_table = "{$wpdb->prefix}betterdocs_ai_chatbot_conversations";

		$data = [];

		if ($session_id) {
			// Fetch conversation data by session ID
			$conversation_data = $wpdb->get_row($wpdb->prepare(
				"SELECT c.conversation, s.device_info, s.main_information
             FROM $conversations_table c
             INNER JOIN $sessions_table s ON c.session_id = s.id
             WHERE c.session_id = %s",
				$session_id
			), ARRAY_A);

			if ($conversation_data) {
				$data = [
					'device_information' => json_decode($conversation_data['device_info'], true),
					'main_information' => json_decode($conversation_data['main_information'], true),
					'conversation' => json_decode($conversation_data['conversation'], true),
				];
			} else {
				return new WP_REST_Response('No conversations found for this session ID.', 404);
			}
		} else {
			return new WP_REST_Response('Session ID or email is required.', 400);
		}

		// Highlight the filter term in conversation texts, if provided
		if ($filter_term && isset($data['conversation'])) {
			$filter_term_lower = strtolower($filter_term);

			foreach ($data['conversation'] as &$conversation) {
				if (isset($conversation['text'])) {
					// Highlight without escaping HTML characters
					$conversation['text'] = str_ireplace(
						$filter_term,
						'<span class="highlight">' . $filter_term . '</span>',
						$conversation['text']
					);
				}
			}
		}

		return new WP_REST_Response($data, 200);
	}

    public function save_conversation_data(WP_REST_Request $request)
    {
        global $wpdb;

        $params = $request->get_json_params();

        // Validate input structure
        if (!isset($params['conversation'])) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Missing conversation data.'
            ], 400);
        }

        $conversation_data = $params['conversation'];

        // Strict structure validation
        if (!is_array($conversation_data) ||
            !isset($conversation_data['email']) ||
            !isset($conversation_data['conversation'])) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Invalid conversation data structure.'
            ], 400);
        }

        // Validate session ID format
        $session_id = $params['session_id'] ?? $_COOKIE['chatbot_session_id'] ?? '';

        if ($session_id && !$this->validate_session_id($session_id)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Invalid session format'
            ], 400);
        }

        // Generate secure session ID if needed
        if (!$session_id) {
            $session_id = $this->generate_session_id();
            $this->set_secure_cookie($session_id);
        }

        // Sanitize inputs
        $email = sanitize_email($conversation_data['email']);
        if (!is_email($email)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Invalid email address'
            ], 400);
        }

        // Sanitize nested data
        $main_information = $this->sanitize_nested_array(
            $conversation_data['main_information'] ?? [],
            ['location', 'country', 'time', 'timezone', 'language'],
            100 // max length per field
        );

        $device_information = $this->sanitize_nested_array(
            $conversation_data['device_information'] ?? [],
            ['IP_address', 'browser'],
            100
        );

        $isBlocked = (bool)($conversation_data['isBlocked'] ?? false);

        // Database setup
        $users_table = $wpdb->prefix . 'betterdocs_ai_chatbot_users';
        $sessions_table = $wpdb->prefix . 'betterdocs_ai_chatbot_sessions';
        $conversations_table = $wpdb->prefix . 'betterdocs_ai_chatbot_conversations';

        try {
            $wpdb->query('START TRANSACTION');

            // User handling with prepared statements
            $user_id = $wpdb->get_var(
                $wpdb->prepare("SELECT id FROM $users_table WHERE email = %s", $email)
            );

            if (!$user_id) {
                $wpdb->insert($users_table, [
                    'email' => $email,
                    'profile_info' => json_encode($this->get_gravatar_data($email)),
                    'is_blocked' => $isBlocked,
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql'),
                ], ['%s', '%s', '%d', '%s', '%s']);

                $user_id = $wpdb->insert_id;
            } else {
                $wpdb->update($users_table, [
                    'profile_info' => json_encode($this->get_gravatar_data($email)),
                    'is_blocked' => $isBlocked,
                    'updated_at' => current_time('mysql'),
                ], ['id' => $user_id], ['%s', '%d', '%s'], ['%d']);
            }

            // Session handling
            if (!$wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $sessions_table WHERE id = %s", $session_id))
            ) {
                $wpdb->insert($sessions_table, [
                    'id' => $session_id,
                    'user_id' => $user_id,
                    'device_info' => json_encode($device_information),
                    'main_information' => json_encode($main_information),
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql'),
                ], ['%s', '%d', '%s', '%s', '%s', '%s']);
            } else {
                // Update session information
                $wpdb->update($sessions_table, [
                    'device_info' => json_encode($device_information),
                    'main_information' => json_encode($main_information),
                    'updated_at' => current_time('mysql'),
                ], ['id' => $session_id], ['%s', '%s', '%s'], ['%s']);
            }

            // IMPROVED CONVERSATION HANDLING: Properly merge and deduplicate messages
            $existing_conversation_json = $wpdb->get_var($wpdb->prepare(
                "SELECT conversation FROM $conversations_table WHERE session_id = %s", $session_id
            ));

            if ($existing_conversation_json) {
                // Get existing conversation and decode it
                $existing_conversation = json_decode($existing_conversation_json, true);
                if (!is_array($existing_conversation)) {
                    $existing_conversation = [];
                }

                // Create a map of existing messages with a more robust unique identifier
                // We'll use a combination of text content, message type, and position in the conversation
                $existing_message_map = [];
                foreach ($existing_conversation as $index => $msg) {
                    if (!isset($msg['text']) || !isset($msg['type'])) {
                        continue; // Skip invalid messages
                    }

                    // Create a unique key using text, type and a hash of nearby messages for context
                    // This helps distinguish between identical messages that appear in different parts of the conversation
                    $context_hash = '';
                    if (isset($existing_conversation[$index-1]['text'])) {
                        $context_hash .= substr(md5($existing_conversation[$index-1]['text']), 0, 8);
                    }
                    if (isset($existing_conversation[$index+1]['text'])) {
                        $context_hash .= substr(md5($existing_conversation[$index+1]['text']), 0, 8);
                    }

                    $key = md5($msg['text'] . $msg['type'] . $context_hash);
                    $existing_message_map[$key] = $msg;

                    // Also store by content alone as a fallback
                    $content_key = md5($msg['text']);
                    if (!isset($existing_message_map[$content_key])) {
                        $existing_message_map[$content_key] = $msg;
                    }
                }

                // Process new messages - maintain original timestamps for existing messages
                $sanitized_conversation = [];
                $processed_keys = []; // Track which messages we've already processed

                foreach ($conversation_data['conversation'] as $index => $new_msg) {
                    // First sanitize with wp_kses_post to allow HTML, then convert to Markdown
                    $text = $this->convert_html_to_markdown(wp_kses_post($new_msg['text'] ?? ''));
                    $type = in_array($new_msg['type'] ?? '', ['sent', 'received', 'failed'])
                        ? $new_msg['type']
                        : 'unknown';

                    if (empty($text)) {
                        continue; // Skip empty messages
                    }

                    // Create context hash similar to above
                    $context_hash = '';
                    if (isset($conversation_data['conversation'][$index-1]['text'])) {
                        $context_hash .= substr(md5($conversation_data['conversation'][$index-1]['text']), 0, 8);
                    }
                    if (isset($conversation_data['conversation'][$index+1]['text'])) {
                        $context_hash .= substr(md5($conversation_data['conversation'][$index+1]['text']), 0, 8);
                    }

                    // Generate a unique key for this message that includes timestamp if available
                    // This ensures that identical messages sent at different times are treated as distinct
                    $timestamp_component = !empty($new_msg['timestamp']) ? $new_msg['timestamp'] : microtime(true);
                    $msg_key = md5($text . $type . $context_hash . $timestamp_component);
                    $content_key = md5($text . $timestamp_component);

                    // Check if we've already processed this exact message in this loop
                    if (isset($processed_keys[$msg_key])) {
                        continue; // Skip only if the exact same message with same timestamp was processed
                    }

                    // For repeated messages, we want to treat them as new messages
                    // with their own timestamps, so we'll only check the database for exact matches
                    // including the timestamp component

                    // This is a new message or a repeated message with a new timestamp
                    // Use the timestamp from the current data or generate current time if no timestamp provided
                    $timestamp = !empty($new_msg['timestamp'])
                        ? sanitize_text_field($new_msg['timestamp'])
                        : current_time('mysql');

                    $sanitized_conversation[] = [
                        'text' => $text,
                        'type' => $type,
                        'timestamp' => $timestamp
                    ];
                    $processed_keys[$msg_key] = true;
                }

                // Update the conversation with properly preserved timestamps
                $wpdb->update($conversations_table, [
                    'conversation' => json_encode($sanitized_conversation),
                    'updated_at' => current_time('mysql')
                ], ['session_id' => $session_id], ['%s', '%s'], ['%s']);

            } else {
                // No existing conversation, process new conversation with preserved timestamps
                $new_conversation = [];
                $processed_keys = []; // Track which messages we've already processed

                foreach ($conversation_data['conversation'] as $entry) {
                    // First sanitize with wp_kses_post to allow HTML, then convert to Markdown
                    $text = $this->convert_html_to_markdown(wp_kses_post($entry['text'] ?? ''));
                    $type = in_array($entry['type'] ?? '', ['sent', 'received', 'failed']) ? $entry['type'] : 'unknown';

                    if (empty($text)) {
                        continue; // Skip empty messages
                    }

                    // Create a key for deduplication that includes timestamp
                    // This ensures that identical messages sent at different times are treated as distinct
                    $timestamp_component = !empty($entry['timestamp']) ? $entry['timestamp'] : microtime(true);
                    $key = md5($text . $type . $timestamp_component);

                    // Skip if we've already processed this exact message with the same timestamp
                    if (isset($processed_keys[$key])) {
                        continue;
                    }

                    $new_conversation[] = [
                        'text' => $text,
                        'type' => $type,
                        'timestamp' => !empty($entry['timestamp']) ? sanitize_text_field($entry['timestamp']) : current_time('mysql')
                    ];

                    $processed_keys[$key] = true;
                }

                $wpdb->insert($conversations_table, [
                    'session_id' => $session_id,
                    'conversation' => json_encode($new_conversation),
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ], ['%s', '%s', '%s', '%s']);
            }

            $wpdb->query('COMMIT');

            return new WP_REST_Response([
                'success' => true,
                'message' => 'Chat data saved successfully!',
                'session_id' => $session_id
            ], 200);

        } catch (\Exception $e) {
            $wpdb->query('ROLLBACK');
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Failed to save conversation data'
            ], 500);
        }
    }

    // Helper method to sanitize nested arrays
    private function sanitize_nested_array(array $data, array $allowed_keys, int $max_length): array
    {
        $sanitized = [];
        foreach ($allowed_keys as $key) {
            $value = $data[$key] ?? '';
            $sanitized[$key] = substr(sanitize_text_field($value), 0, $max_length);
        }
        return $sanitized;
    }

    public function get_gravatar_data($email)
    {
        // Generate email hash and Gravatar URL
        $email_hash = hash('sha256', strtolower(trim($email)));
        $gravatar_url = 'https://www.gravatar.com/' . $email_hash . '.json';

        // Extract the username from the email (before @)
        $username = strstr($email, '@', true);
        $first_char = $username[0];

        // Make the request to Gravatar
        $response = wp_remote_get($gravatar_url);

        if (is_wp_error($response)) {
            // Handle error
            return new \WP_Error('gravatar_error', __('Error fetching data: ') . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // If Gravatar returns no data, return default data
        if (empty($data) || !isset($data['entry'])) {
            // Default data structure using the email username
            return [
                "entry" => [
                    [
                        "hash" => $email_hash,
                        "requestHash" => $email_hash,
                        "profileUrl" => 'https://gravatar.com/default_profile',
                        "preferredUsername" => $username,
                        "thumbnailUrl" => 'https://placehold.co/40x40/ebe9fe/542cbc?text=' . $first_char . '&font=roboto',
                        "photos" => [
                            [
                                "value" => 'https://placehold.co/40x40/ebe9fe/542cbc?text=' . $first_char . '&font=roboto',
                                "type" => "thumbnail"
                            ]
                        ],
                        "displayName" => $username
                    ]
                ]
            ];
        }

        return $data; // Return the decoded data
    }

    public function query_post_to_server(WP_REST_Request $request) {
        try {
            // Validate request structure
            $params = $request->get_json_params();
            if (!is_array($params)) {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => __('Invalid request format', 'betterdocs-ai-chatbot')
                ], 400);
            }

            // Enhanced input validation
            $query = isset($params['query']) ? sanitize_text_field($params['query']) : '';
            $email = isset($params['email']) ? sanitize_email($params['email']) : '';
            $session_id = $params['session_id'] ?? $_COOKIE['chatbot_session_id'] ?? '';

            // Strict validation
            if (empty($query) || strlen($query) > 1000) {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => __('Invalid query length', 'betterdocs-ai-chatbot')
                ], 400);
            }

            if (!is_email($email)) {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => __('Invalid email address', 'betterdocs-ai-chatbot')
                ], 400);
            }

            // Configuration validation
            $config_check = $this->validate_chatbot_config();
            if ($config_check !== true) {
                return $config_check;
            }

            // Get language parameter if provided (check both URL and JSON body)
            $lang = isset($params['lang']) ? sanitize_text_field($params['lang']) : sanitize_text_field($request->get_param('lang'));

            // Prepare API request with validation
            $api_data = [
                "query" => $query,
                "domain" => AIChatbot::get_domain(),
                "conversation" => $this->sanitize_conversation_data(
                    Helper::get_conversation_tokens_data($session_id)['conversation'] ?? []
                ),
                "previous_context_index" => $this->sanitize_index_data(
                    Helper::get_conversation_tokens_data($session_id)['previous_context_index'] ?? []
                ),
                "openai_data" => [
                    "openai_api_key" => AIChatbot::get_openai_api_key(),
                    "openai_chat_model" => AIChatbot::get_chat_model(),
                    "openai_embedding_model" => AIChatbot::get_embed_model()
                ]
            ];

            // Add language parameter if it exists
            if (!empty($lang)) {
                $api_data["lang"] = $lang;
            }

            // Make secure API request
            $response = $this->make_secure_api_request($api_data);
            if (is_wp_error($response)) {
                throw new \Exception($response->get_error_message());
            }

            // Handle database operations
            $result = $this->handle_database_operations($session_id, $email, $response);
            if (!empty($result['error'])) {
                throw new \Exception($result['error']);
            }

            return new WP_REST_Response([
                'success' => true,
                'message' => __('Request successful', 'betterdocs-ai-chatbot'),
                'data' => $this->sanitize_response_data($result['data'])
            ], 200);

        } catch (\Exception $e) {
            return new WP_REST_Response([
                'success' => false,
                'message' => __('We’re currently experiencing a temporary issue with our AI Chatbot system. Please try again shortly. If the problem persists, feel free to contact our support team.', 'betterdocs-ai-chatbot'),
                'data' => [
                    'conversation' => [
                        $this->create_error_message($e->getMessage())
                    ]
                ]
            ], 500);
        }
    }

    // Helper methods
    private function validate_session_id($session_id) {
        return preg_match('/^session_[a-f0-9]{14}\.[a-f0-9]{8}$/', $session_id);
    }

    // This function is used in line 262 when generating a new session ID
    private function generate_session_id() {
        $prefix = 'session_';
        $hex = uniqid($prefix, true); // Original PHP format
        return str_replace(' ', '.', $hex); // Handle PHP's output formatting
    }

    private function validate_chatbot_config() {
        // Check license status first
        $chatbot_license = get_option('betterdocs_chatbot_software__license_status');
        $pro_license = get_option('betterdocs_pro_software__license_status');

        if ($chatbot_license !== 'valid' || $pro_license !== 'valid') {
            return new WP_REST_Response([
                'success' => false,
                'message' => __('BetterDocs AI Chatbot is currently unavailable. Please contact the site administrator for activation.', 'betterdocs-ai-chatbot'),
                'data' => [
                    'conversation' => [
                        [
                            'content' => __('BetterDocs AI Chatbot is currently unavailable. Please contact the site administrator for activation.', 'betterdocs-ai-chatbot'),
                            'role' => 'assistant',
                            'timestamp' => current_time('mysql')
                        ]
                    ]
                ]
            ], 403);
        }

        $required = [
            'domain' => AIChatbot::get_domain(),
            'api_key' => AIChatbot::get_openai_api_key(),
            'chat_model' => AIChatbot::get_chat_model(),
            'embed_model' => AIChatbot::get_embed_model()
        ];

        $missing = array_keys(array_filter($required, function($v) { return empty($v); }));
        if (!empty($missing)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => sprintf(__('Missing configuration: %s', 'betterdocs-ai-chatbot'),
                    implode(', ', $missing))
            ], 500);
        }

        if (!function_exists('betterdocs') || !betterdocs()->settings->get('enable_ai_chatbot', false)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => __('BetterDocs AI Chatbot is currently unavailable. Please contact the site administrator for activation.', 'betterdocs-ai-chatbot')
            ], 403);
        }

        return true;
    }

    private function make_secure_api_request($data) {
        $url = AIChatbot::$api_url . '/v1/query-post';

        // Add language parameter to URL if it exists
        if (!empty($data['lang'])) {
            $url .= '?lang=' . urlencode($data['lang']);
            // Remove lang from data since it's now in the URL
            unset($data['lang']);
        }

        $args = [
            'method' => 'POST',
            'timeout' => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT,
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . get_option('ai_chatbot_request_bareer_token')
            ],
            'body' => wp_json_encode($data),
            'data_format' => 'body',
            'sslverify' => true,
            'redirection' => 0
        ];

        $response = wp_remote_post($url, $args);

        if (is_wp_error($response)) {
            return new WP_Error('api_error', __('Service unavailable', 'betterdocs-ai-chatbot'));
        }

        return $response;
    }

    private function handle_database_operations($session_id, $email, $response) {
        global $wpdb;

        // Note: $email parameter is used in the parent method but not directly in this method
        // It's kept for future use and API consistency

        $decoded_body = json_decode(wp_remote_retrieve_body($response), true);
        $status_code = wp_remote_retrieve_response_code($response);

        if ($status_code !== 200 || !is_array($decoded_body)) {
            throw new \Exception(__('Invalid API response', 'betterdocs-ai-chatbot'));
        }

        // Prepare conversation data
        $conversations_table = $wpdb->prefix . 'betterdocs_ai_chatbot_conversations';
        $sanitized_entries = $this->sanitize_conversation_entries($decoded_body['conversation']);

        $wpdb->query('START TRANSACTION');

        try {
            // Upsert conversation data
            if ($wpdb->get_var($wpdb->prepare(
                "SELECT session_id FROM $conversations_table WHERE session_id = %s", $session_id))
            ) {
                $result = $wpdb->update(
                    $conversations_table,
                    [
                        'conversation' => json_encode($sanitized_entries),
                        'updated_at' => current_time('mysql')
                    ],
                    ['session_id' => $session_id],
                    ['%s', '%s'],
                    ['%s']
                );
            } else {
                $result = $wpdb->insert(
                    $conversations_table,
                    [
                        'session_id' => $session_id,
                        'conversation' => json_encode($sanitized_entries),
                        'created_at' => current_time('mysql'),
                        'updated_at' => current_time('mysql')
                    ],
                    ['%s', '%s', '%s', '%s']
                );
            }

            if (false === $result) {
                throw new \Exception(__('Database operation failed', 'betterdocs-ai-chatbot'));
            }

            $wpdb->query('COMMIT');

            // Set secure cookie
            if (!isset($_COOKIE['chatbot_session_id'])) {
                $this->set_secure_cookie($session_id);
            }

            return [
                'data' => [
                    'conversation' => $sanitized_entries,
                    'context' => $this->sanitize_index_data($decoded_body['context'] ?? []),
                    'previous_context_index' => $this->sanitize_index_data($decoded_body['previous_context_index'] ?? []),
                    'session_id' => $session_id
                ]
            ];

        } catch (\Exception $e) {
            $wpdb->query('ROLLBACK');
            return ['error' => $e->getMessage()];
        }
    }

    private function sanitize_conversation_entries($entries) {
        return array_map(function($entry) {
            return [
                'id' => md5(uniqid(mt_rand(), true)),
                'text' => sanitize_text_field($entry['content'] ?? ''),
                'type' => in_array($entry['role'], ['user', 'assistant']) ? $entry['role'] : 'unknown',
                'timestamp' => sanitize_text_field($entry['timestamp'] ?? current_time('mysql'))
            ];
        }, $entries);
    }

    private function set_secure_cookie($session_id) {
        $cookie_options = [
            'expires' => time() + (30 * 24 * 60 * 60),
            'path' => '/',
            'secure' => true,
            'httponly' => true,
            'samesite' => 'Lax'
        ];
        setcookie('chatbot_session_id', $session_id, $cookie_options);
    }

    private function sanitize_index_data(array $index_data): array {
        $sanitized = [];
        $max_entries = 100; // Prevent DoS with large arrays

        if (!is_array($index_data)) {
            return [];
        }

        foreach (array_slice($index_data, 0, $max_entries) as $entry) {
            if (is_array($entry)) {
                // Sanitize nested array elements
                $clean_entry = [];
                foreach ($entry as $key => $value) {
                    $clean_key = sanitize_key($key);
                    $clean_value = is_scalar($value) ? sanitize_text_field(strval($value)) : '';
                    $clean_entry[$clean_key] = $clean_value;
                }
                $sanitized[] = $clean_entry;
            } elseif (is_scalar($entry)) {
                // Sanitize simple values
                $sanitized[] = sanitize_text_field(strval($entry));
            }
        }

        return $sanitized;
    }

    private function sanitize_conversation_data(array $conversation): array {
        $sanitized = [];

        foreach ($conversation as $message) {
            if (!is_array($message)) continue;

            // Handle both field naming conventions
            $content = $message['content'] ?? $message['text'] ?? '';
            $raw_role = $message['role'] ?? $message['type'] ?? '';

            // Map legacy types to roles
            switch (strtolower($raw_role)) {
                case 'sent':
                case 'user':
                    $role = 'user';
                    break;
                case 'received':
                case 'assistant':
                    $role = 'assistant';
                    break;
                default:
                    $role = 'unknown';
            }

            $sanitized[] = [
                'role' => $role,
                'content' => sanitize_textarea_field($content)
                // Explicitly exclude timestamp
            ];
        }

        return $sanitized;
    }

    private function sanitize_response_data(array $data): array {
        return [
            'conversation' => array_map([$this, 'sanitize_conversation_entry'], $data['conversation'] ?? []),
            'context' => $this->sanitize_index_data($data['context'] ?? []),
            'previous_context_index' => $this->sanitize_index_data($data['previous_context_index'] ?? []),
            'session_id' => $data['session_id'] ?? '',
        ];
    }

    private function create_error_message(string $message): array {
        return [
            'content' => sanitize_text_field($message),
            'role' => 'assistant',
            'timestamp' => sanitize_text_field(current_time('mysql')),
            'is_error' => true
        ];
    }

    private function sanitize_conversation_entry(array $entry): array {
        return [
            'id' => sanitize_key($entry['id'] ?? ''),
            'text' => wp_kses_post($entry['text'] ?? ''), // Allow limited HTML if needed
            'type' => in_array($entry['type'] ?? '', ['sent', 'received'])
                    ? $entry['type']
                    : 'unknown',
            'timestamp' => sanitize_text_field($entry['timestamp'] ?? '')
        ];
    }

    /**
     * Convert HTML links to Markdown format
     *
     * @param string $text The text containing HTML links
     * @return string The text with HTML links converted to Markdown
     */
    private function convert_html_to_markdown($text) {
        // Strip paragraph tags but keep their content
        $text = preg_replace('/<p>(.*?)<\/p>/is', '$1', $text);

        // Convert <a href="...">...</a> to [...](...) format
        $text = preg_replace('/<a\s+href=["\'](.*?)["\'].*?>(.*?)<\/a>/is', '[$2]($1)', $text);

        // Convert HTML entities to their actual characters
        $text = html_entity_decode($text);

        // Remove any remaining HTML tags
        $text = strip_tags($text);

        return $text;
    }

	public function update_online_status(WP_REST_Request $request)
	{
		global $wpdb;

		$params = $request->get_json_params();
		$user_email = isset($params['user_email']) ? sanitize_email($params['user_email']) : null;
		$session_id = sanitize_text_field($request->get_param('session_id'));

		if (empty($user_email) || !is_email($user_email)) {
			return new WP_REST_Response([
				'message' => 'Invalid or missing email address.',
				'status' => 400,
			], 400);
		}

		$online_status_table = "{$wpdb->prefix}betterdocs_ai_chatbot_online_status";

		// Update or insert the online status
		$wpdb->replace(
			$online_status_table,
			[
				'user_email' => $user_email,
				'session_id' => $session_id,
				'last_active' => current_time('mysql'),
			],
			['%s', '%s', '%s']
		);

		return new WP_REST_Response([
			'message' => 'Status updated successfully.',
			'status' => 200,
		], 200);
	}

	public function get_online_status(WP_REST_Request $request)
	{
		global $wpdb;

		$user_email = sanitize_email($request->get_param('user_email'));
		$passed_time = (int) $request->get_param('passed_time'); // Time in minutes

		if (empty($user_email)) {
			return new WP_REST_Response('Email is required', 400);
		}

		$online_status_table = "{$wpdb->prefix}betterdocs_ai_chatbot_online_status";

		// Check the user's last active time
		$last_active = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT last_active FROM $online_status_table WHERE user_email = %s",
				$user_email
			)
		);

		if ($last_active && (strtotime($last_active) >= (current_time('timestamp') - ($passed_time * 60)))) {
			return new WP_REST_Response(true, 200); // User is online
		}

		return new WP_REST_Response(false, 200); // User is offline
	}



	protected function args($args = [])
	{
		return wp_parse_args($args, [
			'license_key' => [
				'required'          => true,
				'validate_callback' => function ($param) {
					return is_string($param) && !empty($param);
				}
			]
		]);
	}
}
