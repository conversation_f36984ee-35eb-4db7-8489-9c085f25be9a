<?php

namespace WPDeveloper\BetterDocsChatbot;

use Exception;
use WPDeveloper\BetterDocs\Dependencies\DI\DependencyException;
use WPDeveloper\BetterDocs\Dependencies\DI\NotFoundException;
use WPDeveloper\BetterDocs\Core\BaseAPI;
use WPDeveloper\BetterDocsChatbot\Core\AIChatbot;
use WPDeveloper\BetterDocsChatbot\Core\AIChatbotMenu;
use WPDeveloper\BetterDocsChatbot\Core\ChatbotNotice;
use WPDeveloper\BetterDocsChatbot\Database\DBSchema;
use WPDeveloper\BetterDocsChatbot\Dependencies\WPDeveloper\Licensing\LicenseManager;
use WPDeveloper\BetterDocsChatbot\Dependencies\WPDeveloper\WPBGProcess\AIChatbotBackgroundProcessing;
use WPDeveloper\BetterDocsChatbot\Utils\Enqueue;
use function is_plugin_active;


final class Plugin {
	private static $_instance = null;
	private        $licenseManager;

	private $version    = '1.2.0';
	private $db_version = '1.0.0';

	public $container = null;
	public $assets    = null;
	public $ai_chatbot;
	public $chatbot;


	/**
	 * Create a plugin instance.
	 *
	 * @return ?Plugin
	 *
	 * @suppress PHP0441
	 * @since 2.5.0
	 */
	public static function get_instance() {
		if ( Plugin::$_instance == null ) {
			Plugin::$_instance = new self();

			do_action( 'betterdocs_chatbot_loaded' );
		}

		return Plugin::$_instance;
	}

	/**
	 * @throws Exception
	 */
	public function __construct() {
		if ( defined( 'BETTERDOCS_CHATBOT_VERSION' ) ) {
			$this->version = BETTERDOCS_CHATBOT_VERSION;
		}

		$this->define_constants();

		// Register the activation hook here
		register_activation_hook( BETTERDOCS_CHATBOT_FILE, [ $this, 'on_plugin_activation' ] );

		if ( ! $this->is_plugin_active( 'betterdocs-pro/betterdocs-pro.php' ) ) {
			add_action( 'admin_notices', [ $this, 'pro_is_required' ] );

			return;
		}

		add_action( 'betterdocs_init_before', [ $this, 'before_init' ] );
		add_action( 'betterdocs_loaded', [ $this, 'initialize' ] );
		add_action( 'admin_init', [ $this, 'admin_init' ] );

		/**
		 * Initialize API
		 */
		add_action( 'rest_api_init', [ $this, 'api_initialization' ] );

		add_action( 'wp_enqueue_scripts', [ $this, 'public_enqueue_scripts' ] );
		add_action( 'admin_enqueue_scripts', [ $this, 'admin_enqueue_scripts' ] );

		$this->license();
	}

	public function admin_enqueue_scripts() {
		betterdocs_chatbot()->assets->enqueue( 'betterdocs-ai-chatbot-history', 'js/betterdocs.chatbot-history.min.js' );
		betterdocs_chatbot()->assets->localize(
			'betterdocs-ai-chatbot-history',
			'betterdocsAIChatbot',
			[
				'rest_url' => esc_url_raw( rest_url() ),
				'nonce'    => wp_create_nonce( 'wp_rest' ),
				'title' => betterdocs()->settings->get('ai_chatbot_title'),
				'subtitle' => betterdocs()->settings->get('ai_chatbot_subtitle'),
				'welcome_message' => betterdocs()->settings->get('ai_chatbot_welcome_message'),
				'locale' => get_locale() // Add the WordPress locale
			]
		);
		betterdocs_chatbot()->assets->enqueue( 'betterdocs-ai-chatbot-history', 'css/betterdocs.chatbot-history.min.css' );
		betterdocs_chatbot()->assets->enqueue( 'betterdocs-ai-chatbot-preview', 'css/betterdocs.chatbot-preview.min.css' );
	}

	public function public_enqueue_scripts() {
		betterdocs_chatbot()->assets->enqueue( 'betterdocs-ai-chatbot-preview', 'js/betterdocs.chatbot-preview.min.js' );
		betterdocs_chatbot()->assets->localize(
			'betterdocs-ai-chatbot-preview',
			'betterdocsAIChatbot',
			[
				'rest_url' => esc_url_raw( rest_url() ),
				'nonce'    => wp_create_nonce( 'wp_rest' ),
				'title' => betterdocs()->settings->get('ai_chatbot_title'),
				'subtitle' => betterdocs()->settings->get('ai_chatbot_subtitle'),
				'welcome_message' => betterdocs()->settings->get('ai_chatbot_welcome_message'),
				'locale' => get_locale() // Add the WordPress locale
			]
		);
		betterdocs_chatbot()->assets->enqueue( 'betterdocs-ai-chatbot-preview', 'css/betterdocs.chatbot-preview.min.css' );
	}

	public function on_plugin_activation() {
		DBSchema::betterdocs_ai_chatbot_create_tables();
	}

	/**
	 * Get all the API initialized.
	 * @return void
	 */
	public function api_initialization() {
		$_api_classes = scandir( __DIR__ . DIRECTORY_SEPARATOR . 'REST' );

		if ( ! empty( $_api_classes ) && is_array( $_api_classes ) ) {
			foreach ( $_api_classes as $class ) {
				if ( $class == '.' || $class == '..' || strpos( $class, '.' ) === 0 ) {
					continue;
				}

				$classname  = basename( $class, '.php' );
				$classname  = '\\' . __NAMESPACE__ . "\\REST\\$classname";
				$_api_class = $this->container->get( $classname );

				if ( $_api_class instanceof BaseAPI ) {
					$_api_class->register();
				}
			}
		}
	}


	public function pro_is_required() {
		if ( file_exists( WP_PLUGIN_DIR . '/betterdocs-pro/betterdocs-pro.php' ) ) {
			$activation_link = wp_nonce_url( admin_url( 'plugins.php?action=activate&plugin=betterdocs-pro/betterdocs-pro.php' ), 'activate-plugin_betterdocs-pro/betterdocs-pro.php' );
			echo '<div class="notice notice-error is-dismissible"><p>BetterDocs AI Chatbot Addon requires the BetterDocs PRO plugin to be active. <a href="' . esc_url( $activation_link ) . '">Activate BetterDocs PRO</a>.</p></div>';
		} else {
			echo '<div class="notice notice-error is-dismissible"><p>BetterDocs AI Chatbot Addon requires the BetterDocs PRO plugin. <a target="_blank" href="' . esc_url( 'https://betterdocs.co/#pricing' ) . '">Get BetterDocs PRO</a>.</p></div>';
		}
	}

	public function required_free_version_notice() {
		$message = sprintf( __( 'Please update your %1$s BetterDocs%2$s plugin to version %1$s%3$s%2$s or higher to ensure full functionality of the %1$sAI Chatbot Addon%2$s.', 'betterdocs-ai-chatbot' ), '<strong>', '</strong>', BETTERDOCS_REQUIRED_VERSION_FOR_AI_CHATBOT );
		echo '<div class="notice notice-error is-dismissible"><p>' . $message . '</p></div>';
	}

	public function required_pro_version_notice() {
		$message = sprintf( __( 'Please update your %1$sBetterDocs Pro%2$s plugin to version %1$s%3$s%2$s or higher to ensure full functionality of the %1$sAI Chatbot Addon.%2$s', 'betterdocs-ai-chatbot' ), '<strong>', '</strong>', BETTERDOCS_PRO_REQUIRED_VERSION_FOR_AI_CHATBOT );
		echo '<div class="notice notice-error is-dismissible"><p>' . $message . '</p></div>';
	}


	/**
	 * Summary of define_constants
	 * @return void
	 */
	private function define_constants() {
		$this->define( 'BETTERDOCS_CHATBOT_DB_VERSION', $this->db_version );
		$this->define( 'BETTERDOCS_CHATBOT_ABSPATH', dirname( BETTERDOCS_CHATBOT_FILE ) . '/' );
		$this->define( 'BETTERDOCS_CHATBOT_ABSURL', plugin_dir_url( BETTERDOCS_CHATBOT_FILE ) );
		$this->define( 'BETTERDOCS_CHATBOT_PLUGIN_BASENAME', plugin_basename( BETTERDOCS_CHATBOT_FILE ) );
		$this->define( 'BETTERDOCS_CHATBOT_PLUGIN_BASENAME', plugin_basename( BETTERDOCS_CHATBOT_FILE ) );


		$this->define( 'BETTERDOCS_CHATBOT_VERSION', $this->version );

		$this->define( 'BETTERDOCS_CHATBOT_STORE_URL', 'https://api.wpdeveloper.com' );
		$this->define( 'BETTERDOCS_CHATBOT_SL_ITEM_ID', 1882016 );
		$this->define( 'BETTERDOCS_CHATBOT_SL_ITEM_SLUG', 'betterdocs-ai-chatbot' );
		$this->define( 'BETTERDOCS_CHATBOT_SL_ITEM_NAME', 'BetterDocs AI Chatbot' );
		$this->define( 'BETTERDOCS_CHATBOT_SL_DB_PREFIX', 'betterdocs_chatbot_software_' );
		$this->define( 'BETTERDOCS_REQUIRED_VERSION_FOR_AI_CHATBOT', '3.8.8' );
		$this->define( 'BETTERDOCS_PRO_REQUIRED_VERSION_FOR_AI_CHATBOT', '3.5.0' );
	}


	/**
	 * Define constant if not already set.
	 *
	 * @param string      $name Constant name.
	 * @param string|bool $value Constant value.
	 */
	private function define( string $name, $value ) {
		if ( ! defined( $name ) ) {
			define( $name, $value );
		}
	}

	public function before_init() {
		add_filter( 'betterdocs_container_config', [ $this, 'container_config' ] );
	}

	public function container_config( $configs ) {
		$config_array = require_once BETTERDOCS_CHATBOT_ABSPATH . 'includes/config.php';

		if ( is_array( $config_array ) ) {
			$configs = array_merge( $configs, $config_array );
		}

		return $configs;
	}

	public function is_plugin_active( $pluginame ) {
		if ( ! function_exists( 'is_plugin_active' ) ) {
			include_once ABSPATH . 'wp-admin/includes/plugin.php';
		}

		return is_plugin_active( $pluginame );
	}

	/**
	 * @throws DependencyException
	 * @throws NotFoundException
	 */
	public function initialize() {
		add_action( 'init', [ $this, 'init' ], 2 );
		add_action( 'init', [$this, 'load_core_services'], 4);
	}

	public function get_posts_count( $post_types ) {
		global $wpdb;

		if ( empty( $post_types ) ) {
			return 0;
		}

		// Ensure $post_types is an array
		$post_types = (array) $post_types;

		// Prepare post types for SQL query
		$placeholders = implode( ', ', array_fill( 0, count( $post_types ), '%s' ) );
		$query = $wpdb->prepare(
			"SELECT COUNT(*) as post_count
			 FROM {$wpdb->posts}
			 WHERE post_type IN ($placeholders)
			 AND post_status = 'publish'",
			...$post_types
		);

		$posts_count = $wpdb->get_var( $query );

		// Use saved post IDs count if 'enable_sync_embed_docs' is enabled
		$post_ids = get_option( 'saved_docs_post_ids', [] );
		if ( get_option( 'enable_sync_embed_docs' ) ) {
			return count( $post_ids );
		}

		return (int) $posts_count;
	}

	public function get_docs_count( $post_type ) {
		global $wpdb;

		$query = $wpdb->prepare( "SELECT COUNT(*) as post_count
            FROM {$wpdb->posts}
            WHERE post_type = %s AND post_status = 'publish'", $post_type );

		$docs_count = $wpdb->get_var( $query );

		$post_ids = get_option( 'saved_docs_post_ids', [] ); // Get the option, defaulting to an empty array
		if ( get_option( 'enable_sync_embed_docs' ) ) {
			return count( $post_ids );
		}

		return $docs_count;
	}

	public function admin_init() {
		if ( defined( 'DOING_AJAX' ) && DOING_AJAX || ! is_admin() ) {
			return;
		}

		// Check if AI chatbot is enabled
		if ( AIChatbot::is_ai_chatbot_enabled() && get_option( 'background_process_status' ) == 1 ) {
			$process = new AIChatbotBackgroundProcessing();

			// Get allowed post types from settings
			$post_types = AIChatbot::get_posttype();
			// Get post count dynamically
			$posts_count = $this->get_posts_count( $post_types ) / BETTERDOCS_DOCS_PER_PAGE;

			for ( $index = 0; $index < $posts_count; $index++ ) {
				$process->push_to_queue( $index );
				$process->save()->dispatch();
			}

			update_option( 'background_process_status', 0 );
		}
	}

	public function init() {
		betterdocs()->load_plugin_textdomain( 'betterdocs-ai-chatbot', BETTERDOCS_CHATBOT_FILE );
	}

	public function load_core_services() {

		if ( version_compare( BETTERDOCS_VERSION, BETTERDOCS_REQUIRED_VERSION_FOR_AI_CHATBOT, '<' ) ) {
			add_action( 'admin_notices', [ $this, 'required_free_version_notice' ] );
		}
		if ( version_compare( BETTERDOCS_PRO_VERSION, BETTERDOCS_PRO_REQUIRED_VERSION_FOR_AI_CHATBOT, '<' ) ) {
			add_action( 'admin_notices', [ $this, 'required_pro_version_notice' ] );
		}

		$this->container = betterdocs()->container;
		$this->assets    = $this->container->get( Enqueue::class );
		$this->container->get( DBSchema::class );
		$this->ai_chatbot = $this->container->get( AIChatbotMenu::class );
		$this->container->get( ChatbotNotice::class );
		$this->chatbot = new AIChatbotBackgroundProcessing();
	}

	/**
	 * @throws Exception
	 */
	public function license() {
		add_action( 'init', function () {
			if ( ! did_action( 'betterdocs_loaded' ) ) {
				return;
			}

			$this->licenseManager = LicenseManager::get_instance( [
				'plugin_file' => BETTERDOCS_CHATBOT_FILE,
				'version'     => $this->version,
				'item_id'     => BETTERDOCS_CHATBOT_SL_ITEM_ID,
				'item_name'   => BETTERDOCS_CHATBOT_SL_ITEM_NAME,
				'item_slug'   => BETTERDOCS_CHATBOT_SL_ITEM_SLUG,
				'storeURL'    => BETTERDOCS_CHATBOT_STORE_URL,
				'textdomain'  => 'betterdocs-ai-chatbot',
				'db_prefix'   => BETTERDOCS_CHATBOT_SL_DB_PREFIX,
				'page_slug'   => 'betterdocs-settings&tab=tab-license',

				'scripts_handle' => 'betterdocs-pro-settings',
				'screen_id'      => [
					"betterdocs_page_betterdocs-settings",
					"toplevel_page_betterdocs-dashboard",
					"betterdocs_page_betterdocs-admin",
					"betterdocs_page_betterdocs-analytics",
					"betterdocs_page_betterdocs-faq",
					"betterdocs_page_betterdocs-ai-chatbot"
				],
				'api'      => 'rest',
				'dev_mode' => true,
				'rest'     => [
					'namespace'  => 'betterdocs-chatbot',
					'permission' => 'delete_users'
				]
			] );
		}, 3 );

		add_action( 'in_admin_header', function () {
			global $current_screen;

			$screens = [
				'toplevel_page_betterdocs-dashboard',
				'betterdocs_page_betterdocs-admin',
				'betterdocs_page_betterdocs-settings',
				'betterdocs_page_betterdocs-faq',
				'betterdocs_page_betterdocs-analytics',
				'betterdocs_page_betterdocs-glossaries',
				'edit-doc_category',
				'edit-doc_tag'
			];

			if ( ! in_array( $current_screen->id, $screens ) ) {
				return;
			}

			add_action( 'admin_notices', [ $this->licenseManager, 'admin_notices' ] );
		}, 11 );
	}

	/**
	 * @return LicenseManager|null
	 */
	public function get_license_manager() {
		return $this->licenseManager;
	}
}
