import React, {Suspense} from "react";
import { __ } from "@wordpress/i18n";

import {addAction, addFilter} from "@wordpress/hooks";

import AIChatbot from "./ai-chatbot-history/components/AIChatbot";
import {licenseMaanager} from "./admin/settings";
import './ai-chatbot-history';
import './ai-chatbot-preview/Preview';
import {licenseInactiveAlert} from "./utils/chatbotProAlert";


licenseMaanager();

const ComponentLoader = ({Component, fallback}) => {
    return (
        <Suspense fallback={fallback ?? "Loading..."}>
            <Component/>
        </Suspense>
    );
};

addFilter("betterdocs_routes", "betterdocs/ai-chatbot", (routes) => {

    routes.push({
        path: "betterdocs-ai-chatbot",
        element: <ComponentLoader Component={AIChatbot}/>,
        exact: true,
    });
    return routes;
});


addFilter("betterdocs_admin_header_title", "betterdocsChatbot", (content, page) => {
    switch (page) {
        case "betterdocs-ai-chatbot":
            return __("AI Chatbot Conversation History", 'betterdocs-ai-chatbot');
    }
    return content;
});


addFilter(
    "quickBuilder_fieldValue",
    "betterdocsChatbot",
    function (value, field) {
        if ('enable_ai_chatbot' === field && value && (betterdocs.PRO_LICENSE !== 'valid' || betterdocs.CHATBOT_LICENSE !== 'valid')) {
            return false;
        }

        return value;
    }
)


addAction(
    "quickBuilder_setFieldValue",
    "betterdocsChatbot",
    function (field, value, validProps) {
        if ('enable_ai_chatbot' === field && value && (betterdocs.PRO_LICENSE !== 'valid' || betterdocs.CHATBOT_LICENSE !== 'valid')) {
            licenseInactiveAlert().fire();
            return;
        }
    }
)
