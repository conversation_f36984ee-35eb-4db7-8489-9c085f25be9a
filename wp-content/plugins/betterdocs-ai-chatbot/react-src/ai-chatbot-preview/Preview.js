import { addFilter } from "@wordpress/hooks";
import { __ } from "@wordpress/i18n";
import Chatbot from "./Chatbot";
import ChatbotActiveIcon from "./components/icons/ChatbotActiveIcon.js";
import ChatbotTabIcon from "./components/icons/ChatbotTabIcon.js";

addFilter('tab_chatbot_preview', 'btterdocs/chattab-preview', (tabs, activeTabClass) => {
    

    tabs.push({
        id: "chatbot",
        class: "betterdocs-ia-chatbot",
        type: "tab",
        title: __("Chatbot", "betterdocs-ai-chatbot"),
        default: false,
        icon: (
            activeTabClass == 'chatbot' ? (
                <ChatbotActiveIcon />
            ) : (
                <ChatbotTabIcon />
            )
        ),
        component: Chatbot,
        showTab: true,
        showTabInComponent: true,
    });
    return tabs;
});
