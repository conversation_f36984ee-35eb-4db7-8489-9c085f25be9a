import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import DOMPurify from 'dompurify';
import { __ } from '@wordpress/i18n';

import ChatbotIcon from './components/icons/ChatbotIcon';
import ChatbotUser from './components/icons/ChatbotUser';
import { getBrowserInfo, getCookie, parseToHtml, setCookie, sanitizeInput, initSession, generateSessionId } from '../utils/helper';
import Seen from './components/icons/Seen';
import EmailArrow from './components/icons/EmailArrow';
import Close from './components/icons/Close';
import Send from './components/icons/Send';
import useOnlineStatus from './components/hooks/useOnlineStatus';
import { getConversation, getCurrentUserConversation, updateOnlineStatus, handleSaveConversationData } from '../utils/fetch';
import TimeAgoDisplay from './TimeAgoDisplay';

import Disable from './components/icons/Disable';
import GenericLoader from './components/GenericLoader';
import BackIcon from './components/icons/BackIcon';
import { marked } from 'marked';
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";


const Chatbot = () => {
    const [sessionId, setSessionId] = useState(null);
    const [message, setMessage] = useState('');
    const [messages, setMessages] = useState([]);
    const [isNewMesage, setNewMessage] = useState(false);
    const [isTyping, setIsTyping] = useState(false);
    const [typedMessage, setTypedMessage] = useState(''); // For typewriting effect
    const [showEmailField, setShowEmailField] = useState(true);
    const [onChangeEmail, setOnChangeEmail] = useState("");
    const [email, setEmail] = useState(getCookie('userEmail') || "");
    const [initialBotResponse] = useState(betterdocsAIChatbot?.welcome_message);
    const [typingStartTime, setTypingStartTime] = useState(null);
    const [isShowThankYouMessage, setThankYouMessage] = useState(false);
    const [isShowEmailValidateMessage, setEmailValidateMessage] = useState(false);
    const [validateAlert, setValidationAlert] = useState(null);
    const [isEmailFieldFocused, setIsEmailFieldFocused] = useState(false);
    const [loading, setLoading] = useState(false);

    const chatBodyRef = useRef(null);
    const emailFieldRef = useRef(null);

    const [error, setError] = useState(null);
    const [location, setLocation] = useState({ city: '', country: '' });

    const [status, setStatus] = useState('offline');

    const timeStampFormat = new Date().toISOString().slice(0, 19).replace('T', ' ');
    const getMySQLTimestamp = () => {
        const now = new Date();
        // Format as YYYY-MM-DD HH:MM:SS (MySQL format)
        return now.toISOString().slice(0, 19).replace('T', ' ');
    };

    // Initialize session only once when component mounts
    useEffect(() => {
        // Get or create a session ID and store it in state
        const existingSession = initSession();
        setSessionId(existingSession);
    }, []);



    useOnlineStatus();

    useEffect(() => {
        const fetchIpInfo = async () => {
            try {
                const response = await fetch('https://ipinfo.io/json');
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const data = await response?.json();
                setLocation(data);
            } catch (err) {
                setError(err.message);
            }
        };

        fetchIpInfo();
    }, []);

    const handleMessageChange = (event) => {
        const rawValue = event.target.value;
        const sanitizedValue = sanitizeInput.sanitize('text', rawValue);
        setMessage(sanitizedValue);
    };

    const handleSendClick = async () => {
        if (message.trim() === '') return;

        const tempMessage = message?.trim(); // Capture and trim the current message
        const sentMessageTimestamp = getMySQLTimestamp(); // Generate unique timestamp for sent message

        // Add the sent message to the state immediately with its own timestamp
        setMessages((prevMessages) => [
            ...prevMessages,
            {
                text: tempMessage,
                type: (navigator?.onLine) ? 'sent' : 'failed',
                timestamp: sentMessageTimestamp // Use the unique timestamp
            },
        ]);

        // Clear the input field immediately after adding the message
        setMessage('');
        setTypedMessage('');

        // Ensure the chat body is scrolled to the bottom after adding the new message
        if (chatBodyRef.current) {
            setTimeout(() => {
                chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
            }, 50); // Small delay to ensure state update is processed before scrolling
        }

        if (!navigator.onLine) {
            return;
        }

        setIsTyping(true);

        try {
            // Get the site language from WordPress settings
            // betterdocsAIChatbot.locale contains the WordPress site language (e.g., 'en_US', 'es_ES', 'fr_FR')
            const wpLocale = betterdocsAIChatbot?.locale || 'en_US';

            // Extract the language code (e.g., 'en' from 'en_US', 'es' from 'es_ES')
            const langCode = wpLocale.split('_')[0];

            const response = await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/query-post`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    query: tempMessage,
                    email: email,
                    session_id: sessionId,
                    lang: langCode // Add the language parameter from WordPress locale
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'API request failed');
            }

            const data = await response.json();
            // Check if the response is successful
            if (data?.success) {
                // Safely extract the conversation array
                const conversation = data?.data?.conversation || [];

                // Get the last message from the conversation array
                const lastMessage = conversation[conversation.length - 1];

                // Extract text from the last message
                const responseMessage = lastMessage?.text || 'No response content available';

                // Start typewriting effect with the response message
                setNewMessage(true);
                startTypewriting(responseMessage);
            } else {
                // Handle errors by throwing the message or data
                throw new Error(data.message || 'Request failed');
            }

        } catch (error) {
            setIsTyping(false);
            setMessages((prevMessages) => [
                ...prevMessages,
                {
                    text: error.message,
                    type: 'received',
                    timestamp: getMySQLTimestamp() // Use a new timestamp for error messages
                },
            ]);
        }
    };


    const startTypewriting = (responseMessage) => {
        let index = 0;
        let plainTextMessage = ''; // Temporary storage for plain text before applying HTML
        setTypedMessage(''); // Clear previous typed message

        // Generate unique timestamp for this specific bot response
        const responseTimestamp = getMySQLTimestamp();

        setIsTyping(true);
        setTypingStartTime(responseTimestamp); // Set the typing start time in state

        const typeWriter = () => {
            if (index < responseMessage?.length) {
                plainTextMessage += responseMessage?.charAt(index);
                setTypedMessage(plainTextMessage);
                index++;
                setTimeout(typeWriter, 10); // Adjust typing speed (in ms)
            } else {
                // Once typing is complete, apply formatting
                const formattedMessage = parseToHtml(plainTextMessage).__html;
                setTypedMessage(formattedMessage);
                setIsTyping(false);
                setMessages((prevMessages) => [
                    ...prevMessages,
                    {
                        text: formattedMessage,
                        type: 'received',
                        timestamp: responseTimestamp // Use the timestamp we generated for this response
                    },
                ]);
            }
        };

        typeWriter();
    };

    const ensureUniqueTimestamps = (messages) => {
        const seenTimestamps = new Set();
        let lastTimestamp = null;

        return messages.map(msg => {
            // If message has no timestamp, generate one
            if (!msg.timestamp) {
                const newTimestamp = getMySQLTimestamp();
                return {...msg, timestamp: newTimestamp};
            }

            // If this timestamp has been seen before, make it unique
            if (seenTimestamps.has(msg.timestamp) || msg.timestamp === lastTimestamp) {
                // Add milliseconds to make it unique
                // Handle both ISO and MySQL format timestamps
                let date;
                if (msg.timestamp.includes('T') && msg.timestamp.includes('Z')) {
                    // ISO format
                    date = new Date(msg.timestamp);
                } else {
                    // MySQL format
                    date = new Date(msg.timestamp.replace(' ', 'T') + 'Z');
                }
                date.setMilliseconds(date.getMilliseconds() + 1);
                const uniqueTimestamp = date.toISOString().slice(0, 19).replace('T', ' ');
                lastTimestamp = uniqueTimestamp;
                seenTimestamps.add(uniqueTimestamp);
                return {...msg, timestamp: uniqueTimestamp};
            }

            // Otherwise use the original timestamp
            lastTimestamp = msg.timestamp;
            seenTimestamps.add(msg.timestamp);
            return msg;
        });
    };

    const handleKeyPress = (event) => {
        if (!isTyping && event?.key === 'Enter' && !event?.shiftKey) {
            event.preventDefault();
            handleSendClick();
        }
    };

    const handleEmailChange = (e) => {
        setOnChangeEmail(e?.target?.value);
    };

    const handleEmailSet = async () => {
        if (onChangeEmail?.trim() === '' || !validateEmail(onChangeEmail?.trim())) {
            setEmailValidateMessage(true);
            setTimeout(() => setEmailValidateMessage(false), 3000);
            return;
        }

        // Use consistent cookie settings
        setCookie('userEmail', onChangeEmail, {
            expiresDays: 30,
            path: '/',
            secure: window.location.protocol === 'https:',
            sameSite: 'Lax'
        });

        setCookie('showEmailField', 'false', {
            expiresDays: 30,
            path: '/',
            secure: window.location.protocol === 'https:',
            sameSite: 'Lax'
        });

        // Update state after cookies are set
        setEmail(onChangeEmail);

        setThankYouMessage(true);
        setTimeout(() => setThankYouMessage(false), 3000);
        setShowEmailField(false);
    };

    // Load chat history when email or sessionId changes
    useEffect(() => {
        const getChatHistory = async () => {
            // Only proceed if we have both email and sessionId
            if (!email || !sessionId) {
                return;
            }

            setLoading(true);
            try {
                // Use the sessionId from state - don't reinitialize
                const conversation = await getCurrentUserConversation(email, sessionId);

                if (conversation?.conversation) {
                    // Update conversation with session context
                    setMessages(conversation.conversation);

                    // Update online status with current session
                    updateOnlineStatus(email, sessionId);
                }
            } catch (error) {
                setMessages([{
                    text: initialBotResponse,
                    type: 'received',
                    timestamp: getMySQLTimestamp() // Use MySQL format timestamp
                }]);
            } finally {
                setLoading(false);
            }
        };

        if (email) {
            getChatHistory();
        }
    }, [email, sessionId, initialBotResponse]);


    useEffect(() => {
        const initialTimestamp = getMySQLTimestamp();
        const savedConversation = email ? getCookie(email) : null;

        if (email && savedConversation) {
            const parsedConversation = JSON.parse(savedConversation);
            // Ensure unique timestamps for all messages
            setMessages(ensureUniqueTimestamps(parsedConversation?.conversation || []));
        } else if (!email) {
            const initialMessage = {
                text: initialBotResponse,
                type: 'received',
                timestamp: initialTimestamp,
            };
            setMessages([initialMessage]); // Set the initial message
        }
    }, [email]);

    // Use a ref to track the last save time
    const lastSaveTimeRef = useRef(0);
    // Use a ref to track if a save is pending
    const saveTimeoutRef = useRef(null);
    // Minimum time between saves in milliseconds (e.g., 2000ms = 2 seconds)
    const SAVE_DEBOUNCE_TIME = 2000;

    useEffect(() => {
        // Clear any existing timeout when component unmounts
        return () => {
            if (saveTimeoutRef.current) {
                clearTimeout(saveTimeoutRef.current);
            }
        };
    }, []);

    useEffect(() => {
        // Only proceed if we have the necessary data
        if (!email || !messages || messages.length === 0 || !sessionId) {
            return;
        }

        // Clear any existing timeout
        if (saveTimeoutRef.current) {
            clearTimeout(saveTimeoutRef.current);
        }

        // Set a new timeout to save the conversation
        saveTimeoutRef.current = setTimeout(() => {
            // Check if enough time has passed since the last save
            const now = Date.now();
            if (now - lastSaveTimeRef.current < SAVE_DEBOUNCE_TIME) {
                return; // Skip this save if it's too soon after the last one
            }

            // Update the last save time
            lastSaveTimeRef.current = now;

            // Ensure each message in the array has a unique timestamp
            const uniqueTimestampMessages = ensureUniqueTimestamps(messages);

            // Construct the conversation structure with the unique timestamp messages
            const conversationData = {
                email: email,
                main_information: {
                    location: `${location?.city}, ${location?.country}`,
                    country: location?.country,
                    time: getMySQLTimestamp(),
                    timezone: location?.timezone,
                    language: navigator?.language,
                },
                device_information: {
                    IP_address: location?.ip,
                    browser: getBrowserInfo()
                },
                isBlocked: false,
                conversation: uniqueTimestampMessages.map(msg => ({
                    text: msg?.text,
                    type: msg?.type,
                    timestamp: msg?.timestamp || getMySQLTimestamp() // Use original timestamp or generate new one
                }))
            };

            // Save data to the WordPress database with preserved timestamps
            handleSaveConversationData(email, conversationData, sessionId)
            .then(response => {
                if (response?.success) {
                    updateOnlineStatus(email, sessionId);
                }
            })
            .catch(error => {
                console.error("Error saving conversation:", error);
            });
        }, 500); // Short delay to debounce rapid changes
    }, [location, messages, email, sessionId]);

    // Focus management using useEffect
    useEffect(() => {
        if (emailFieldRef?.current) {
            const handleFocus = () => setIsEmailFieldFocused(true);
            const handleBlur = () => setIsEmailFieldFocused(false);

            const emailInput = emailFieldRef?.current;

            emailInput.addEventListener('focus', handleFocus);
            emailInput.addEventListener('blur', handleBlur);

            // Cleanup the event listeners on unmount
            return () => {
                emailInput.removeEventListener('focus', handleFocus);
                emailInput.removeEventListener('blur', handleBlur);
            };
        }
    }, []);


    useEffect(() => {

        // Scroll to the bottom of the chat body whenever messages change
        if (chatBodyRef.current) {
            chatBodyRef.current.scrollTop = chatBodyRef?.current?.scrollHeight;
        }
    }, [messages, isTyping, typedMessage, isShowEmailValidateMessage, isShowThankYouMessage]);

    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!email) {
            setValidationAlert('Email is required');
            return false;
        } else if (!emailRegex.test(email)) {
            setValidationAlert('Please enter a valid email');
            return false;
        }
        return true;
    };

    // Back button functionality removed as it's not being used

    return (
        <div className="chat-container">
            <div className="betterdocs-chatbot-header betterdocs-ia-common-header">
                {/* <button type="button" className="back-button" onClick={handleBack}>
                    <BackIcon />
                </button> */}
                <h2>{__("Chatbot", "betterdocs-ai-chatbot")}</h2>
            </div>

            <div className='chat-content-wrapper'>
                <div className="chat-body" ref={chatBodyRef}>
                    <div className="top-content">
                        <div className="chat-icon">
                            <ChatbotIcon />
                        </div>
                        {betterdocsAIChatbot?.title && (
                            <h3 className="heading-title">
                                {betterdocsAIChatbot.title}
                            </h3>
                        )}
                        {betterdocsAIChatbot?.subtitle && (
                            <p className="chat-description">
                                {betterdocsAIChatbot.subtitle}
                            </p>
                        )}
                    </div>

                    {
                        loading && (
                            <GenericLoader />
                        )
                    }
                    {!loading && messages && messages?.map((msg, index) => (
                        <div key={index} className={`message ${msg?.type}`}>
                            {msg?.type === 'sent' || msg?.type === 'failed' ? (
                                <div className='query'>
                                    {msg?.text}
                                    {
                                        (msg?.type == 'failed') && (
                                            <span className="status">
                                                {__("Sending Failed", "betterdocs-ai-chatbot")}
                                            </span>
                                        )
                                    }
                                </div>
                            ) : msg?.type === 'received' ? (
                                <div className="message-content">
                                    <div className="avatar">
                                        <ChatbotUser />
                                    </div>
                                    <div className='message-text'>
                                        <div className="text" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(marked(msg?.text)) }}></div>
                                        <span className="message-received-time">
                                            {msg.timestamp && (
                                                <TimeAgoDisplay
                                                    timestamp={msg?.timestamp}
                                                    timezone={location?.timezone}
                                                    isNewMesage={isNewMesage}
                                                    setNewMessage={setNewMessage}
                                                />
                                            )}
                                        </span>
                                    </div>
                                </div>
                            ) : (
                                <></>
                            )}
                        </div>
                    ))}



                    {isTyping && typedMessage && (
                        <div className="message typing received message-active">
                            <div className="message-content">
                                <div className="avatar">
                                    <ChatbotUser />
                                </div>
                                <div className='message-text'>
                                    <div className="text" dangerouslySetInnerHTML={{ __html: DOMPurify?.sanitize(typedMessage) }}></div>
                                    <span className="message-received-time">
                                        <TimeAgoDisplay
                                            timestamp={typingStartTime || timeStampFormat}
                                            timezone={location?.timezone}
                                            isNewMesage={isNewMesage}
                                            setNewMessage={setNewMessage}
                                        />
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}

                    {(isTyping && !typedMessage) && (
                        <div className="message typing received message-active">
                            <div className="message-content">
                                <div className="avatar">
                                    <ChatbotUser />
                                </div>
                                <div className="text thinking-dots">{__("Thinking", "betterdocs-ai-chatbot")}<span className="dots"></span></div>
                            </div>
                        </div>
                    )}

                    {showEmailField && getCookie('showEmailField') !== 'false' && (
                        <div className={`message received email-field-wrapper ${isEmailFieldFocused ? 'focused' : ''}`}>
                            <div className="message-content">
                                <div className="avatar">
                                    <ChatbotUser />
                                </div>
                                <div className="text">{__("Enter email to keep conversation alive.", "betterdocs-ai-chatbot")}</div>
                            </div>
                            <div className="email-field-container">
                                <div className="email-field">
                                    <input
                                        ref={emailFieldRef}
                                        type="email"
                                        id="email"
                                        value={onChangeEmail}
                                        onChange={handleEmailChange}
                                        onKeyDown={(e) => {
                                            if (e?.key === 'Enter') {
                                                handleEmailSet();
                                            }
                                        }}
                                        placeholder={__("Enter your email", "betterdocs-ai-chatbot")}
                                        required
                                    />
                                    <div className='email-icon' onClick={handleEmailSet}>
                                        <EmailArrow />
                                    </div>
                                </div>

                                {isShowEmailValidateMessage && (
                                    <div className='error-message-container'>
                                        {validateAlert}
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>

                {isShowThankYouMessage && (
                    <div className='thankyou-message-container'>
                        {__("Thanks! We should reply in a moment.", "betterdocs-ai-chatbot")}
                    </div>
                )}


                <div className="chat-footer">
                    <div className={`message-input${showEmailField && getCookie('showEmailField') !== 'false' ? ' disabled' : ''}`} >
                        <input
                            placeholder={__("Type a message...", "betterdocs-ai-chatbot")}
                            value={message}
                            onChange={handleMessageChange}
                            onKeyDown={handleKeyPress}
                            rows="2"
                        />

                        <button
                            onClick={handleSendClick}
                            style={isTyping ? { pointerEvents: 'none', opacity: '.6' } : {}}
                            disabled={isTyping}
                        >
                            <Send />
                        </button>

                    </div>
                </div>
            </div>
        </div>
    );
};

export default Chatbot;
