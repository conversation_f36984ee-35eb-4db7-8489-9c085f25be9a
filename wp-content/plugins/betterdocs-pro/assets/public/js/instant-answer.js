(()=>{var e={100:(e,t,n)=>{"use strict";n.d(t,{dd:()=>o,eJ:()=>u,wm:()=>l});var r=n(814),a=n(543),i=n(932);function s(e){return Math.min(1e3*Math.pow(2,e),3e4)}function o(e){return"function"==typeof(null==e?void 0:e.cancel)}var c=function(e){this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent};function l(e){return e instanceof c}var u=function(e){var t,n,l,u,h=this,d=!1;this.abort=e.abort,this.cancel=function(e){return null==t?void 0:t(e)},this.cancelRetry=function(){d=!0},this.continueRetry=function(){d=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise((function(e,t){l=e,u=t}));var f=function(t){h.isResolved||(h.isResolved=!0,null==e.onSuccess||e.onSuccess(t),null==n||n(),l(t))},m=function(t){h.isResolved||(h.isResolved=!0,null==e.onError||e.onError(t),null==n||n(),u(t))};!function l(){if(!h.isResolved){var u;try{u=e.fn()}catch(e){u=Promise.reject(e)}t=function(e){if(!h.isResolved&&(m(new c(e)),null==h.abort||h.abort(),o(u)))try{u.cancel()}catch(e){}},h.isTransportCancelable=o(u),Promise.resolve(u).then(f).catch((function(t){var o,c;if(!h.isResolved){var u=null!=(o=e.retry)?o:3,f=null!=(c=e.retryDelay)?c:s,p="function"==typeof f?f(h.failureCount,t):f,v=!0===u||"number"==typeof u&&h.failureCount<u||"function"==typeof u&&u(h.failureCount,t);!d&&v?(h.failureCount++,null==e.onFail||e.onFail(h.failureCount,t),(0,i.yy)(p).then((function(){if(!r.m.isFocused()||!a.t.isOnline())return new Promise((function(t){n=t,h.isPaused=!0,null==e.onPause||e.onPause()})).then((function(){n=void 0,h.isPaused=!1,null==e.onContinue||e.onContinue()}))})).then((function(){d?m(t):l()}))):m(t)}}))}}()}},185:(e,t,n)=>{"use strict";n.d(t,{j:()=>a});var r=n(932),a=new(function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(e){e()},this.batchNotifyFn=function(e){e()}}var t=e.prototype;return t.batch=function(e){var t;this.transactions++;try{t=e()}finally{this.transactions--,this.transactions||this.flush()}return t},t.schedule=function(e){var t=this;this.transactions?this.queue.push(e):(0,r.G6)((function(){t.notifyFn(e)}))},t.batchCalls=function(e){var t=this;return function(){for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];t.schedule((function(){e.apply(void 0,r)}))}},t.flush=function(){var e=this,t=this.queue;this.queue=[],t.length&&(0,r.G6)((function(){e.batchNotifyFn((function(){t.forEach((function(t){e.notifyFn(t)}))}))}))},t.setNotifyFunction=function(e){this.notifyFn=e},t.setBatchNotifyFunction=function(e){this.batchNotifyFn=e},e}())},295:(e,t,n)=>{"use strict";function r(e,t){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,t)}function a(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,r(e,t)}n.d(t,{A:()=>a})},329:(e,t,n)=>{"use strict";n.d(t,{B:()=>i,t:()=>a});var r=console;function a(){return r}function i(e){r=e}},352:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});var r=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(e){var t=this,n=e||function(){};return this.listeners.push(n),this.onSubscribe(),function(){t.listeners=t.listeners.filter((function(e){return e!==n})),t.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}()},425:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(null,arguments)}n.d(t,{A:()=>r})},543:(e,t,n)=>{"use strict";n.d(t,{t:()=>s});var r=n(295),a=n(352),i=n(932),s=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!i.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},t}(0,r.A)(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"==typeof e?n.setOnline(e):n.onOnline()}))},n.setOnline=function(e){this.online=e,e&&this.onOnline()},n.onOnline=function(){this.listeners.forEach((function(e){e()}))},n.isOnline=function(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},t}(a.Q))},576:(e,t,n)=>{"use strict";var r=n(795);t.H=r.createRoot,r.hydrateRoot},592:(e,t,n)=>{"use strict";n.d(t,{QueryClientProvider:()=>m,useQuery:()=>M});var r=n(185),a=n(795),i=n.n(a)().unstable_batchedUpdates;r.j.setBatchNotifyFunction(i);var s=n(329),o=console;(0,s.B)(o);var c=n(609),l=n.n(c),u=l().createContext(void 0),h=l().createContext(!1);function d(e){return e&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=u),window.ReactQueryClientContext):u}var f=function(){var e=l().useContext(d(l().useContext(h)));if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},m=function(e){var t=e.client,n=e.contextSharing,r=void 0!==n&&n,a=e.children;l().useEffect((function(){return t.mount(),function(){t.unmount()}}),[t]);var i=d(r);return l().createElement(h.Provider,{value:r},l().createElement(i.Provider,{value:t},a))},p=n(425),v=n(295),y=n(932),g=n(814),C=n(352),b=n(100),E=function(e){function t(t,n){var r;return(r=e.call(this)||this).client=t,r.options=n,r.trackedProps=[],r.selectError=null,r.bindMethods(),r.setOptions(n),r}(0,v.A)(t,e);var n=t.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){1===this.listeners.length&&(this.currentQuery.addObserver(this),w(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return _(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return _(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(e,t){var n=this.options,r=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();var a=this.hasListeners();a&&S(this.currentQuery,r,this.options,n)&&this.executeFetch(),this.updateResult(t),!a||this.currentQuery===r&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();var i=this.computeRefetchInterval();!a||this.currentQuery===r&&this.options.enabled===n.enabled&&i===this.currentRefetchInterval||this.updateRefetchInterval(i)},n.getOptimisticResult=function(e){var t=this.client.defaultQueryObserverOptions(e),n=this.client.getQueryCache().build(this.client,t);return this.createResult(n,t)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(e,t){var n=this,r={},a=function(e){n.trackedProps.includes(e)||n.trackedProps.push(e)};return Object.keys(e).forEach((function(t){Object.defineProperty(r,t,{configurable:!1,enumerable:!0,get:function(){return a(t),e[t]}})})),(t.useErrorBoundary||t.suspense)&&a("error"),r},n.getNextResult=function(e){var t=this;return new Promise((function(n,r){var a=t.subscribe((function(t){t.isFetching||(a(),t.isError&&(null==e?void 0:e.throwOnError)?r(t.error):n(t))}))}))},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(e){return this.fetch((0,p.A)({},e,{meta:{refetchPage:null==e?void 0:e.refetchPage}}))},n.fetchOptimistic=function(e){var t=this,n=this.client.defaultQueryObserverOptions(e),r=this.client.getQueryCache().build(this.client,n);return r.fetch().then((function(){return t.createResult(r,n)}))},n.fetch=function(e){var t=this;return this.executeFetch(e).then((function(){return t.updateResult(),t.currentResult}))},n.executeFetch=function(e){this.updateQuery();var t=this.currentQuery.fetch(this.options,e);return(null==e?void 0:e.throwOnError)||(t=t.catch(y.lQ)),t},n.updateStaleTimeout=function(){var e=this;if(this.clearStaleTimeout(),!y.S$&&!this.currentResult.isStale&&(0,y.gn)(this.options.staleTime)){var t=(0,y.j3)(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout((function(){e.currentResult.isStale||e.updateResult()}),t)}},n.computeRefetchInterval=function(){var e;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(e=this.options.refetchInterval)&&e},n.updateRefetchInterval=function(e){var t=this;this.clearRefetchInterval(),this.currentRefetchInterval=e,!y.S$&&!1!==this.options.enabled&&(0,y.gn)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval((function(){(t.options.refetchIntervalInBackground||g.m.isFocused())&&t.executeFetch()}),this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(e,t){var n,r=this.currentQuery,a=this.options,i=this.currentResult,o=this.currentResultState,c=this.currentResultOptions,l=e!==r,u=l?e.state:this.currentQueryInitialState,h=l?this.currentResult:this.previousQueryResult,d=e.state,f=d.dataUpdatedAt,m=d.error,p=d.errorUpdatedAt,v=d.isFetching,g=d.status,C=!1,b=!1;if(t.optimisticResults){var E=this.hasListeners(),_=!E&&w(e,t),T=E&&S(e,r,t,a);(_||T)&&(v=!0,f||(g="loading"))}if(t.keepPreviousData&&!d.dataUpdateCount&&(null==h?void 0:h.isSuccess)&&"error"!==g)n=h.data,f=h.dataUpdatedAt,g=h.status,C=!0;else if(t.select&&void 0!==d.data)if(i&&d.data===(null==o?void 0:o.data)&&t.select===this.selectFn)n=this.selectResult;else try{this.selectFn=t.select,n=t.select(d.data),!1!==t.structuralSharing&&(n=(0,y.BH)(null==i?void 0:i.data,n)),this.selectResult=n,this.selectError=null}catch(e){(0,s.t)().error(e),this.selectError=e}else n=d.data;if(void 0!==t.placeholderData&&void 0===n&&("loading"===g||"idle"===g)){var q;if((null==i?void 0:i.isPlaceholderData)&&t.placeholderData===(null==c?void 0:c.placeholderData))q=i.data;else if(q="function"==typeof t.placeholderData?t.placeholderData():t.placeholderData,t.select&&void 0!==q)try{q=t.select(q),!1!==t.structuralSharing&&(q=(0,y.BH)(null==i?void 0:i.data,q)),this.selectError=null}catch(e){(0,s.t)().error(e),this.selectError=e}void 0!==q&&(g="success",n=q,b=!0)}return this.selectError&&(m=this.selectError,n=this.selectResult,p=Date.now(),g="error"),{status:g,isLoading:"loading"===g,isSuccess:"success"===g,isError:"error"===g,isIdle:"idle"===g,data:n,dataUpdatedAt:f,error:m,errorUpdatedAt:p,failureCount:d.fetchFailureCount,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>u.dataUpdateCount||d.errorUpdateCount>u.errorUpdateCount,isFetching:v,isRefetching:v&&"loading"!==g,isLoadingError:"error"===g&&0===d.dataUpdatedAt,isPlaceholderData:b,isPreviousData:C,isRefetchError:"error"===g&&0!==d.dataUpdatedAt,isStale:O(e,t),refetch:this.refetch,remove:this.remove}},n.shouldNotifyListeners=function(e,t){if(!t)return!0;var n=this.options,r=n.notifyOnChangeProps,a=n.notifyOnChangePropsExclusions;if(!r&&!a)return!0;if("tracked"===r&&!this.trackedProps.length)return!0;var i="tracked"===r?this.trackedProps:r;return Object.keys(e).some((function(n){var r=n,s=e[r]!==t[r],o=null==i?void 0:i.some((function(e){return e===n})),c=null==a?void 0:a.some((function(e){return e===n}));return s&&!c&&(!i||o)}))},n.updateResult=function(e){var t=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!(0,y.f8)(this.currentResult,t)){var n={cache:!0};!1!==(null==e?void 0:e.listeners)&&this.shouldNotifyListeners(this.currentResult,t)&&(n.listeners=!0),this.notify((0,p.A)({},n,e))}},n.updateQuery=function(){var e=this.client.getQueryCache().build(this.client,this.options);if(e!==this.currentQuery){var t=this.currentQuery;this.currentQuery=e,this.currentQueryInitialState=e.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==t||t.removeObserver(this),e.addObserver(this))}},n.onQueryUpdate=function(e){var t={};"success"===e.type?t.onSuccess=!0:"error"!==e.type||(0,b.wm)(e.error)||(t.onError=!0),this.updateResult(t),this.hasListeners()&&this.updateTimers()},n.notify=function(e){var t=this;r.j.batch((function(){e.onSuccess?(null==t.options.onSuccess||t.options.onSuccess(t.currentResult.data),null==t.options.onSettled||t.options.onSettled(t.currentResult.data,null)):e.onError&&(null==t.options.onError||t.options.onError(t.currentResult.error),null==t.options.onSettled||t.options.onSettled(void 0,t.currentResult.error)),e.listeners&&t.listeners.forEach((function(e){e(t.currentResult)})),e.cache&&t.client.getQueryCache().notify({query:t.currentQuery,type:"observerResultsUpdated"})}))},t}(C.Q);function w(e,t){return function(e,t){return!(!1===t.enabled||e.state.dataUpdatedAt||"error"===e.state.status&&!1===t.retryOnMount)}(e,t)||e.state.dataUpdatedAt>0&&_(e,t,t.refetchOnMount)}function _(e,t,n){if(!1!==t.enabled){var r="function"==typeof n?n(e):n;return"always"===r||!1!==r&&O(e,t)}return!1}function S(e,t,n,r){return!1!==n.enabled&&(e!==t||!1===r.enabled)&&(!n.suspense||"error"!==e.state.status)&&O(e,n)}function O(e,t){return e.isStaleByTime(t.staleTime)}var T,q=l().createContext((T=!1,{clearReset:function(){T=!1},reset:function(){T=!0},isReset:function(){return T}})),A=function(){return l().useContext(q)};function M(e,t,n){return function(e,t){var n=l().useRef(!1),a=l().useState(0)[1],i=f(),s=A(),o=i.defaultQueryObserverOptions(e);o.optimisticResults=!0,o.onError&&(o.onError=r.j.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=r.j.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=r.j.batchCalls(o.onSettled)),o.suspense&&("number"!=typeof o.staleTime&&(o.staleTime=1e3),0===o.cacheTime&&(o.cacheTime=1)),(o.suspense||o.useErrorBoundary)&&(s.isReset()||(o.retryOnMount=!1));var c,u,h,d=l().useState((function(){return new t(i,o)}))[0],m=d.getOptimisticResult(o);if(l().useEffect((function(){n.current=!0,s.clearReset();var e=d.subscribe(r.j.batchCalls((function(){n.current&&a((function(e){return e+1}))})));return d.updateResult(),function(){n.current=!1,e()}}),[s,d]),l().useEffect((function(){d.setOptions(o,{listeners:!1})}),[o,d]),o.suspense&&m.isLoading)throw d.fetchOptimistic(o).then((function(e){var t=e.data;null==o.onSuccess||o.onSuccess(t),null==o.onSettled||o.onSettled(t,null)})).catch((function(e){s.clearReset(),null==o.onError||o.onError(e),null==o.onSettled||o.onSettled(void 0,e)}));if(m.isError&&!s.isReset()&&!m.isFetching&&(c=o.suspense,u=o.useErrorBoundary,h=[m.error,d.getCurrentQuery()],"function"==typeof u?u.apply(void 0,h):"boolean"==typeof u?u:c))throw m.error;return"tracked"===o.notifyOnChangeProps&&(m=d.trackResult(m,o)),m}((0,y.vh)(e,t,n),E)}},609:e=>{"use strict";e.exports=window.React},633:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.E});var r=n(672),a=n(640);n.o(a,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return a.QueryClientProvider}}),n.o(a,"useQuery")&&n.d(t,{useQuery:function(){return a.useQuery}})},640:()=>{},672:(e,t,n)=>{"use strict";n.d(t,{E:()=>y});var r=n(425),a=n(932),i=n(295),s=n(185),o=n(329),c=n(100),l=function(){function e(e){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=e.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(e){var t;this.options=(0,r.A)({},this.defaultOptions,e),this.meta=null==e?void 0:e.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(t=this.options.cacheTime)?t:3e5)},t.setDefaultOptions=function(e){this.defaultOptions=e},t.scheduleGc=function(){var e=this;this.clearGcTimeout(),(0,a.gn)(this.cacheTime)&&(this.gcTimeout=setTimeout((function(){e.optionalRemove()}),this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(e,t){var n,r,i=this.state.data,s=(0,a.Zw)(e,i);return(null==(n=(r=this.options).isDataEqual)?void 0:n.call(r,i,s))?s=i:!1!==this.options.structuralSharing&&(s=(0,a.BH)(i,s)),this.dispatch({data:s,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt}),s},t.setState=function(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})},t.cancel=function(e){var t,n=this.promise;return null==(t=this.retryer)||t.cancel(e),n?n.then(a.lQ).catch(a.lQ):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some((function(e){return!1!==e.options.enabled}))},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some((function(e){return e.getCurrentResult().isStale}))},t.isStaleByTime=function(e){return void 0===e&&(e=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,a.j3)(this.state.dataUpdatedAt,e)},t.onFocus=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnWindowFocus()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.onOnline=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnReconnect()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.addObserver=function(e){-1===this.observers.indexOf(e)&&(this.observers.push(e),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))},t.removeObserver=function(e){-1!==this.observers.indexOf(e)&&(this.observers=this.observers.filter((function(t){return t!==e})),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:e}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(e,t){var n,r,i,s=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==t?void 0:t.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var l;return null==(l=this.retryer)||l.continueRetry(),this.promise}if(e&&this.setOptions(e),!this.options.queryFn){var u=this.observers.find((function(e){return e.options.queryFn}));u&&this.setOptions(u.options)}var h=(0,a.HN)(this.queryKey),d=(0,a.jY)(),f={queryKey:h,pageParam:void 0,meta:this.meta};Object.defineProperty(f,"signal",{enumerable:!0,get:function(){if(d)return s.abortSignalConsumed=!0,d.signal}});var m,p,v={fetchOptions:t,options:this.options,queryKey:h,state:this.state,fetchFn:function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(f)):Promise.reject("Missing queryFn")},meta:this.meta};return(null==(n=this.options.behavior)?void 0:n.onFetch)&&(null==(m=this.options.behavior)||m.onFetch(v)),this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(r=v.fetchOptions)?void 0:r.meta)||this.dispatch({type:"fetch",meta:null==(p=v.fetchOptions)?void 0:p.meta}),this.retryer=new c.eJ({fn:v.fetchFn,abort:null==d||null==(i=d.abort)?void 0:i.bind(d),onSuccess:function(e){s.setData(e),null==s.cache.config.onSuccess||s.cache.config.onSuccess(e,s),0===s.cacheTime&&s.optionalRemove()},onError:function(e){(0,c.wm)(e)&&e.silent||s.dispatch({type:"error",error:e}),(0,c.wm)(e)||(null==s.cache.config.onError||s.cache.config.onError(e,s),(0,o.t)().error(e)),0===s.cacheTime&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:v.options.retry,retryDelay:v.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(e){var t=this;this.state=this.reducer(this.state,e),s.j.batch((function(){t.observers.forEach((function(t){t.onQueryUpdate(e)})),t.cache.notify({query:t,type:"queryUpdated",action:e})}))},t.getDefaultState=function(e){var t="function"==typeof e.initialData?e.initialData():e.initialData,n=void 0!==e.initialData?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0,r=void 0!==t;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:r?"success":"idle"}},t.reducer=function(e,t){var n,a;switch(t.type){case"failed":return(0,r.A)({},e,{fetchFailureCount:e.fetchFailureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"fetch":return(0,r.A)({},e,{fetchFailureCount:0,fetchMeta:null!=(n=t.meta)?n:null,isFetching:!0,isPaused:!1},!e.dataUpdatedAt&&{error:null,status:"loading"});case"success":return(0,r.A)({},e,{data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(a=t.dataUpdatedAt)?a:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var i=t.error;return(0,c.wm)(i)&&i.revert&&this.revertState?(0,r.A)({},this.revertState):(0,r.A)({},e,{error:i,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return(0,r.A)({},e,{isInvalidated:!0});case"setState":return(0,r.A)({},e,t.state);default:return e}},e}(),u=n(352),h=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.queries=[],n.queriesMap={},n}(0,i.A)(t,e);var n=t.prototype;return n.build=function(e,t,n){var r,i=t.queryKey,s=null!=(r=t.queryHash)?r:(0,a.F$)(i,t),o=this.get(s);return o||(o=new l({cache:this,queryKey:i,queryHash:s,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(i),meta:t.meta}),this.add(o)),o},n.add=function(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"queryAdded",query:e}))},n.remove=function(e){var t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter((function(t){return t!==e})),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"queryRemoved",query:e}))},n.clear=function(){var e=this;s.j.batch((function(){e.queries.forEach((function(t){e.remove(t)}))}))},n.get=function(e){return this.queriesMap[e]},n.getAll=function(){return this.queries},n.find=function(e,t){var n=(0,a.b_)(e,t)[0];return void 0===n.exact&&(n.exact=!0),this.queries.find((function(e){return(0,a.MK)(n,e)}))},n.findAll=function(e,t){var n=(0,a.b_)(e,t)[0];return Object.keys(n).length>0?this.queries.filter((function(e){return(0,a.MK)(n,e)})):this.queries},n.notify=function(e){var t=this;s.j.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},n.onFocus=function(){var e=this;s.j.batch((function(){e.queries.forEach((function(e){e.onFocus()}))}))},n.onOnline=function(){var e=this;s.j.batch((function(){e.queries.forEach((function(e){e.onOnline()}))}))},t}(u.Q),d=function(){function e(e){this.options=(0,r.A)({},e.defaultOptions,e.options),this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.observers=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0},this.meta=e.meta}var t=e.prototype;return t.setState=function(e){this.dispatch({type:"setState",state:e})},t.addObserver=function(e){-1===this.observers.indexOf(e)&&this.observers.push(e)},t.removeObserver=function(e){this.observers=this.observers.filter((function(t){return t!==e}))},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(a.lQ).catch(a.lQ)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var e,t=this,n="loading"===this.state.status,r=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),r=r.then((function(){null==t.mutationCache.config.onMutate||t.mutationCache.config.onMutate(t.state.variables,t)})).then((function(){return null==t.options.onMutate?void 0:t.options.onMutate(t.state.variables)})).then((function(e){e!==t.state.context&&t.dispatch({type:"loading",context:e,variables:t.state.variables})}))),r.then((function(){return t.executeMutation()})).then((function(n){e=n,null==t.mutationCache.config.onSuccess||t.mutationCache.config.onSuccess(e,t.state.variables,t.state.context,t)})).then((function(){return null==t.options.onSuccess?void 0:t.options.onSuccess(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(e,null,t.state.variables,t.state.context)})).then((function(){return t.dispatch({type:"success",data:e}),e})).catch((function(e){return null==t.mutationCache.config.onError||t.mutationCache.config.onError(e,t.state.variables,t.state.context,t),(0,o.t)().error(e),Promise.resolve().then((function(){return null==t.options.onError?void 0:t.options.onError(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(void 0,e,t.state.variables,t.state.context)})).then((function(){throw t.dispatch({type:"error",error:e}),e}))}))},t.executeMutation=function(){var e,t=this;return this.retryer=new c.eJ({fn:function(){return t.options.mutationFn?t.options.mutationFn(t.state.variables):Promise.reject("No mutationFn found")},onFail:function(){t.dispatch({type:"failed"})},onPause:function(){t.dispatch({type:"pause"})},onContinue:function(){t.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(e){var t=this;this.state=function(e,t){switch(t.type){case"failed":return(0,r.A)({},e,{failureCount:e.failureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"loading":return(0,r.A)({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return(0,r.A)({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return(0,r.A)({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return(0,r.A)({},e,t.state);default:return e}}(this.state,e),s.j.batch((function(){t.observers.forEach((function(t){t.onMutationUpdate(e)})),t.mutationCache.notify(t)}))},e}(),f=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.mutations=[],n.mutationId=0,n}(0,i.A)(t,e);var n=t.prototype;return n.build=function(e,t,n){var r=new d({mutationCache:this,mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:n,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0,meta:t.meta});return this.add(r),r},n.add=function(e){this.mutations.push(e),this.notify(e)},n.remove=function(e){this.mutations=this.mutations.filter((function(t){return t!==e})),e.cancel(),this.notify(e)},n.clear=function(){var e=this;s.j.batch((function(){e.mutations.forEach((function(t){e.remove(t)}))}))},n.getAll=function(){return this.mutations},n.find=function(e){return void 0===e.exact&&(e.exact=!0),this.mutations.find((function(t){return(0,a.nJ)(e,t)}))},n.findAll=function(e){return this.mutations.filter((function(t){return(0,a.nJ)(e,t)}))},n.notify=function(e){var t=this;s.j.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var e=this.mutations.filter((function(e){return e.state.isPaused}));return s.j.batch((function(){return e.reduce((function(e,t){return e.then((function(){return t.continue().catch(a.lQ)}))}),Promise.resolve())}))},t}(u.Q),m=n(814),p=n(543);function v(e,t){return null==e.getNextPageParam?void 0:e.getNextPageParam(t[t.length-1],t)}var y=function(){function e(e){void 0===e&&(e={}),this.queryCache=e.queryCache||new h,this.mutationCache=e.mutationCache||new f,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var e=this;this.unsubscribeFocus=m.m.subscribe((function(){m.m.isFocused()&&p.t.isOnline()&&(e.mutationCache.onFocus(),e.queryCache.onFocus())})),this.unsubscribeOnline=p.t.subscribe((function(){m.m.isFocused()&&p.t.isOnline()&&(e.mutationCache.onOnline(),e.queryCache.onOnline())}))},t.unmount=function(){var e,t;null==(e=this.unsubscribeFocus)||e.call(this),null==(t=this.unsubscribeOnline)||t.call(this)},t.isFetching=function(e,t){var n=(0,a.b_)(e,t)[0];return n.fetching=!0,this.queryCache.findAll(n).length},t.isMutating=function(e){return this.mutationCache.findAll((0,r.A)({},e,{fetching:!0})).length},t.getQueryData=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state.data},t.getQueriesData=function(e){return this.getQueryCache().findAll(e).map((function(e){return[e.queryKey,e.state.data]}))},t.setQueryData=function(e,t,n){var r=(0,a.vh)(e),i=this.defaultQueryOptions(r);return this.queryCache.build(this,i).setData(t,n)},t.setQueriesData=function(e,t,n){var r=this;return s.j.batch((function(){return r.getQueryCache().findAll(e).map((function(e){var a=e.queryKey;return[a,r.setQueryData(a,t,n)]}))}))},t.getQueryState=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state},t.removeQueries=function(e,t){var n=(0,a.b_)(e,t)[0],r=this.queryCache;s.j.batch((function(){r.findAll(n).forEach((function(e){r.remove(e)}))}))},t.resetQueries=function(e,t,n){var i=this,o=(0,a.b_)(e,t,n),c=o[0],l=o[1],u=this.queryCache,h=(0,r.A)({},c,{active:!0});return s.j.batch((function(){return u.findAll(c).forEach((function(e){e.reset()})),i.refetchQueries(h,l)}))},t.cancelQueries=function(e,t,n){var r=this,i=(0,a.b_)(e,t,n),o=i[0],c=i[1],l=void 0===c?{}:c;void 0===l.revert&&(l.revert=!0);var u=s.j.batch((function(){return r.queryCache.findAll(o).map((function(e){return e.cancel(l)}))}));return Promise.all(u).then(a.lQ).catch(a.lQ)},t.invalidateQueries=function(e,t,n){var i,o,c,l=this,u=(0,a.b_)(e,t,n),h=u[0],d=u[1],f=(0,r.A)({},h,{active:null==(i=null!=(o=h.refetchActive)?o:h.active)||i,inactive:null!=(c=h.refetchInactive)&&c});return s.j.batch((function(){return l.queryCache.findAll(h).forEach((function(e){e.invalidate()})),l.refetchQueries(f,d)}))},t.refetchQueries=function(e,t,n){var i=this,o=(0,a.b_)(e,t,n),c=o[0],l=o[1],u=s.j.batch((function(){return i.queryCache.findAll(c).map((function(e){return e.fetch(void 0,(0,r.A)({},l,{meta:{refetchPage:null==c?void 0:c.refetchPage}}))}))})),h=Promise.all(u).then(a.lQ);return(null==l?void 0:l.throwOnError)||(h=h.catch(a.lQ)),h},t.fetchQuery=function(e,t,n){var r=(0,a.vh)(e,t,n),i=this.defaultQueryOptions(r);void 0===i.retry&&(i.retry=!1);var s=this.queryCache.build(this,i);return s.isStaleByTime(i.staleTime)?s.fetch(i):Promise.resolve(s.state.data)},t.prefetchQuery=function(e,t,n){return this.fetchQuery(e,t,n).then(a.lQ).catch(a.lQ)},t.fetchInfiniteQuery=function(e,t,n){var r=(0,a.vh)(e,t,n);return r.behavior={onFetch:function(e){e.fetchFn=function(){var t,n,r,i,s,o,l,u,h,d=null==(t=e.fetchOptions)||null==(n=t.meta)?void 0:n.refetchPage,f=null==(r=e.fetchOptions)||null==(i=r.meta)?void 0:i.fetchMore,m=null==f?void 0:f.pageParam,p="forward"===(null==f?void 0:f.direction),y="backward"===(null==f?void 0:f.direction),g=(null==(s=e.state.data)?void 0:s.pages)||[],C=(null==(o=e.state.data)?void 0:o.pageParams)||[],b=(0,a.jY)(),E=null==b?void 0:b.signal,w=C,_=!1,S=e.options.queryFn||function(){return Promise.reject("Missing queryFn")},O=function(e,t,n,r){return w=r?[t].concat(w):[].concat(w,[t]),r?[n].concat(e):[].concat(e,[n])},T=function(t,n,r,a){if(_)return Promise.reject("Cancelled");if(void 0===r&&!n&&t.length)return Promise.resolve(t);var i={queryKey:e.queryKey,signal:E,pageParam:r,meta:e.meta},s=S(i),o=Promise.resolve(s).then((function(e){return O(t,r,e,a)}));return(0,c.dd)(s)&&(o.cancel=s.cancel),o};if(g.length)if(p){var q=void 0!==m,A=q?m:v(e.options,g);l=T(g,q,A)}else if(y){var M=void 0!==m,R=M?m:(u=e.options,h=g,null==u.getPreviousPageParam?void 0:u.getPreviousPageParam(h[0],h));l=T(g,M,R,!0)}else!function(){w=[];var t=void 0===e.options.getNextPageParam,n=!d||!g[0]||d(g[0],0,g);l=n?T([],t,C[0]):Promise.resolve(O([],C[0],g[0]));for(var r=function(n){l=l.then((function(r){if(!d||!g[n]||d(g[n],n,g)){var a=t?C[n]:v(e.options,r);return T(r,t,a)}return Promise.resolve(O(r,C[n],g[n]))}))},a=1;a<g.length;a++)r(a)}();else l=T([]);var F=l.then((function(e){return{pages:e,pageParams:w}}));return F.cancel=function(){_=!0,null==b||b.abort(),(0,c.dd)(l)&&l.cancel()},F}}},this.fetchQuery(r)},t.prefetchInfiniteQuery=function(e,t,n){return this.fetchInfiniteQuery(e,t,n).then(a.lQ).catch(a.lQ)},t.cancelMutations=function(){var e=this,t=s.j.batch((function(){return e.mutationCache.getAll().map((function(e){return e.cancel()}))}));return Promise.all(t).then(a.lQ).catch(a.lQ)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(e){return this.mutationCache.build(this,e).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(e){this.defaultOptions=e},t.setQueryDefaults=function(e,t){var n=this.queryDefaults.find((function(t){return(0,a.Od)(e)===(0,a.Od)(t.queryKey)}));n?n.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})},t.getQueryDefaults=function(e){var t;return e?null==(t=this.queryDefaults.find((function(t){return(0,a.Cp)(e,t.queryKey)})))?void 0:t.defaultOptions:void 0},t.setMutationDefaults=function(e,t){var n=this.mutationDefaults.find((function(t){return(0,a.Od)(e)===(0,a.Od)(t.mutationKey)}));n?n.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})},t.getMutationDefaults=function(e){var t;return e?null==(t=this.mutationDefaults.find((function(t){return(0,a.Cp)(e,t.mutationKey)})))?void 0:t.defaultOptions:void 0},t.defaultQueryOptions=function(e){if(null==e?void 0:e._defaulted)return e;var t=(0,r.A)({},this.defaultOptions.queries,this.getQueryDefaults(null==e?void 0:e.queryKey),e,{_defaulted:!0});return!t.queryHash&&t.queryKey&&(t.queryHash=(0,a.F$)(t.queryKey,t)),t},t.defaultQueryObserverOptions=function(e){return this.defaultQueryOptions(e)},t.defaultMutationOptions=function(e){return(null==e?void 0:e._defaulted)?e:(0,r.A)({},this.defaultOptions.mutations,this.getMutationDefaults(null==e?void 0:e.mutationKey),e,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}()},795:e=>{"use strict";e.exports=window.ReactDOM},814:(e,t,n)=>{"use strict";n.d(t,{m:()=>s});var r=n(295),a=n(352),i=n(932),s=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!i.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},t}(0,r.A)(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"==typeof e?n.setFocused(e):n.onFocus()}))},n.setFocused=function(e){this.focused=e,e&&this.onFocus()},n.onFocus=function(){this.listeners.forEach((function(e){e()}))},n.isFocused=function(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},t}(a.Q))},932:(e,t,n)=>{"use strict";n.d(t,{BH:()=>g,Cp:()=>v,F$:()=>m,G6:()=>S,HN:()=>c,MK:()=>d,Od:()=>p,S$:()=>a,Zw:()=>s,b_:()=>h,f8:()=>C,gn:()=>o,j3:()=>l,jY:()=>O,lQ:()=>i,nJ:()=>f,vh:()=>u,yy:()=>_});var r=n(425),a="undefined"==typeof window;function i(){}function s(e,t){return"function"==typeof e?e(t):e}function o(e){return"number"==typeof e&&e>=0&&e!==1/0}function c(e){return Array.isArray(e)?e:[e]}function l(e,t){return Math.max(e+(t||0)-Date.now(),0)}function u(e,t,n){return w(e)?"function"==typeof t?(0,r.A)({},n,{queryKey:e,queryFn:t}):(0,r.A)({},t,{queryKey:e}):e}function h(e,t,n){return w(e)?[(0,r.A)({},t,{queryKey:e}),n]:[e||{},t]}function d(e,t){var n=e.active,r=e.exact,a=e.fetching,i=e.inactive,s=e.predicate,o=e.queryKey,c=e.stale;if(w(o))if(r){if(t.queryHash!==m(o,t.options))return!1}else if(!v(t.queryKey,o))return!1;var l=function(e,t){return!0===e&&!0===t||null==e&&null==t?"all":!1===e&&!1===t?"none":(null!=e?e:!t)?"active":"inactive"}(n,i);if("none"===l)return!1;if("all"!==l){var u=t.isActive();if("active"===l&&!u)return!1;if("inactive"===l&&u)return!1}return!("boolean"==typeof c&&t.isStale()!==c||"boolean"==typeof a&&t.isFetching()!==a||s&&!s(t))}function f(e,t){var n=e.exact,r=e.fetching,a=e.predicate,i=e.mutationKey;if(w(i)){if(!t.options.mutationKey)return!1;if(n){if(p(t.options.mutationKey)!==p(i))return!1}else if(!v(t.options.mutationKey,i))return!1}return!("boolean"==typeof r&&"loading"===t.state.status!==r||a&&!a(t))}function m(e,t){return((null==t?void 0:t.queryKeyHashFn)||p)(e)}function p(e){var t;return t=c(e),JSON.stringify(t,(function(e,t){return b(t)?Object.keys(t).sort().reduce((function(e,n){return e[n]=t[n],e}),{}):t}))}function v(e,t){return y(c(e),c(t))}function y(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((function(n){return!y(e[n],t[n])}))}function g(e,t){if(e===t)return e;var n=Array.isArray(e)&&Array.isArray(t);if(n||b(e)&&b(t)){for(var r=n?e.length:Object.keys(e).length,a=n?t:Object.keys(t),i=a.length,s=n?[]:{},o=0,c=0;c<i;c++){var l=n?c:a[c];s[l]=g(e[l],t[l]),s[l]===e[l]&&o++}return r===i&&o===r?e:s}return t}function C(e,t){if(e&&!t||t&&!e)return!1;for(var n in e)if(e[n]!==t[n])return!1;return!0}function b(e){if(!E(e))return!1;var t=e.constructor;if(void 0===t)return!0;var n=t.prototype;return!!E(n)&&!!n.hasOwnProperty("isPrototypeOf")}function E(e){return"[object Object]"===Object.prototype.toString.call(e)}function w(e){return"string"==typeof e||Array.isArray(e)}function _(e){return new Promise((function(t){setTimeout(t,e)}))}function S(e){Promise.resolve().then(e).catch((function(e){return setTimeout((function(){throw e}))}))}function O(){if("function"==typeof AbortController)return new AbortController}},957:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.QueryClient,QueryClientProvider:()=>a.QueryClientProvider,useQuery:()=>a.useQuery});var r=n(633);n.o(r,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return r.QueryClientProvider}}),n.o(r,"useQuery")&&n.d(t,{useQuery:function(){return r.useQuery}});var a=n(592)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e;n.g.importScripts&&(e=n.g.location+"");var t=n.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var a=r.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=r[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=e+"../"})(),(()=>{"use strict";const e=window.wp.element;var t=n(609);const r=()=>(0,e.createElement)("svg",{className:"betterdocs-launch-icon",xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("mask",{id:"a",width:24,height:24,x:0,y:0,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},(0,e.createElement)("path",{fill:"#fff",d:"M24 0H0v24h24V0Z"}),(0,e.createElement)("path",{fill:"#fff",d:"M13.172 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.344 0ZM17.86 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.343 0ZM8.484 12a1.172 1.172 0 1 1-2.343 0 1.172 1.172 0 0 1 2.343 0Z"})),(0,e.createElement)("g",{mask:"url(#a)"},(0,e.createElement)("mask",{id:"b",width:24,height:24,x:0,y:0,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0V0Z"})),(0,e.createElement)("g",{mask:"url(#b)"},(0,e.createElement)("path",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:10,strokeWidth:1.5,d:"M12 .938C5.89.938.937 5.89.937 12c0 2.15.615 4.158 1.677 5.856L.938 23.062l5.206-1.676A11.01 11.01 0 0 0 12 23.063c6.11 0 11.063-4.953 11.063-11.063S18.11.938 12 .938Z"}),(0,e.createElement)("path",{fill:"#fff",d:"M13.172 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.344 0ZM17.86 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.343 0ZM8.484 12a1.172 1.172 0 1 1-2.343 0 1.172 1.172 0 0 1 2.343 0Z"})))),a=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:15,height:15,className:"betterdocs-launch-icon betterdocs-launch-icon-close"},(0,e.createElement)("path",{d:"M14.793.207a.758.758 0 0 0-.227-.156.774.774 0 0 0-.27-.059.774.774 0 0 0-.269.059.758.758 0 0 0-.226.156L7.5 6.504 1.2.207A.682.682 0 0 0 .702 0a.682.682 0 0 0-.496.207A.682.682 0 0 0 0 .703c0 .188.07.363.207.496L6.504 7.5.207 13.8a.682.682 0 0 0-.207.497c0 .187.07.363.207.496A.682.682 0 0 0 .703 15c.188 0 .363-.07.496-.207L7.5 8.496l6.3 6.297a.682.682 0 0 0 .497.207c.187 0 .363-.07.496-.207a.682.682 0 0 0 .207-.496.682.682 0 0 0-.207-.496L8.496 7.5l6.297-6.3a.758.758 0 0 0 .156-.227.769.769 0 0 0 .051-.27.769.769 0 0 0-.05-.27.758.758 0 0 0-.157-.226Zm0 0",style:{stroke:"none",fillRule:"nonzero",fill:"#fff",fillOpacity:1}})),i=window.wp.hooks,s=betterdocs,o=s?.BASE_URL,c=(s.IA_NONCE,Boolean(s.HOME_DOCS_SWITCH)),l=Boolean(s.HOME_FAQ_SWITCH),u=s.CHAT,h=Boolean(s.TAB_AI_CHATBOT),d=s.FAQ,f=s.HOME_FAQ,m=s.SEARCH,p=s.SEARCH_LETTER_LIMIT,v=s.ASKFORM,y=v.FILE_UPLOAD_SWITCH,g=s.THANKS,C=s.DOC_CATEGORY,b=C["doc-title"],E=d["faq-title"],w=d["faq-switch"],_=d.faq_content_type,S=d["faq-terms"],O=d["faq-list"],T=d["faq-terms-order"],q=d["faq-terms-order-by"],A=d["faq-list-orderby"],M=d["faq-list-order"],R=C["doc-category-switch"],F=C["doc-terms"],N=C["doc-terms-order"],k=C["doc-terms-order-by"],P=C["doc-subcategory-switch"],x=s?.URL,Q=s?.HOME_CONTENT,L=s?.HOME_CONTENT_DOC_CATEGORY_TITLE,D=s?.HOME_CONTENT_DOCS_TITLE,H="docs"==Q?D:L,I="docs"==Q?"post_type":"taxonomy",j=s?.FEEDBACK?.DISPLAY,z=s?.FEEDBACK?.URL,Z=s?.FEEDBACK?.SUCCESS,U=s?.FEEDBACK?.TEXT,B=s.HOME_TITLE,V=s.HOME_SUBTITLE,K=s.RESOURCES_TITLE,$=s.RESOURCES_TAB_TITLE,G=(s.HEADER_ICON,s.HEADER_LOGO),W=s.TAB_HOME_ICON,J=s.TAB_MESSAGE_ICON,Y=s.TAB_RESOURCE_ICON,X=s.LAUNCHER?.open_icon,ee=s.LAUNCHER?.close_icon,te=s.BRANDING?.show,ne=s.HOME_TAB_TITLE,re={home:W?.url,feedback_tab:J?.url,resources:Y?.url},ae=({toogle:n})=>{const[s,o]=(0,t.useState)(void 0),[c,l]=(0,t.useState)(void 0),[u,h]=(0,t.useState)(!1);return(0,t.useEffect)((()=>{(0,i.addAction)("openIconPreviewAction","instant_answer",(e=>{const{img_url:t,preview:n}=e;o(t),h(n)})),(0,i.addAction)("closeIconPreviewAction","instant_answer",(e=>{const{img_url:t,preview:n}=e;l(t),h(n)}))}),[]),!n&&u?null!=s?(0,e.createElement)("img",{src:s,height:25,width:25}):(0,e.createElement)(r,null):n&&u?null!=c?(0,e.createElement)("img",{src:c,height:25,width:25}):(0,e.createElement)(a,null):n&&!u?null!=ee?(0,e.createElement)("img",{src:ee,height:25,width:25}):(0,e.createElement)(a,null):n||u?void 0:null!=X?(0,e.createElement)("img",{src:X,height:25,width:25}):(0,e.createElement)(r,null)},ie=({toggleState:t,setToggleState:n})=>(0,e.createElement)("div",{className:"betterdocs-ia-launcher-wrapper"},(0,e.createElement)("button",{className:"betterdocs-ia-launcher",onClick:()=>n(!t)},(0,e.createElement)(ae,{toogle:t}))),se=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{clipPath:"url(#a)"},(0,e.createElement)("mask",{id:"b",width:24,height:24,x:0,y:0,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0V0Z"})),(0,e.createElement)("g",{fill:"#202223",mask:"url(#b)"},(0,e.createElement)("path",{fillRule:"evenodd",d:"M16.242 22.547a6.307 6.307 0 0 1-6.183-5.07l-1.472.292a7.807 7.807 0 0 0 11.61 5.203l3.837 1.061-1.062-3.837A7.807 7.807 0 0 0 17.77 8.587l-.292 1.472a6.307 6.307 0 0 1 4.056 9.613l-.184.283.533 1.927-1.927-.533-.283.184a6.271 6.271 0 0 1-3.43 1.014Z",clipRule:"evenodd"}),(0,e.createElement)("path",{fillRule:"evenodd",d:"M-.047 9.164A9.211 9.211 0 1 1 4.5 17.108L-.033 18.36 1.22 13.83A9.172 9.172 0 0 1-.047 9.164Zm9.211-7.71A7.711 7.711 0 0 0 2.662 13.31l.18.281-.724 2.618 2.618-.724.281.18A7.71 7.71 0 1 0 9.164 1.453Z",clipRule:"evenodd"}),(0,e.createElement)("path",{d:"M9.867 14.11H8.461v-1.407h1.406v1.406Z"}),(0,e.createElement)("path",{fillRule:"evenodd",d:"M9.914 10.22v1.077h-1.5V9.56l1.667-1.525a1.36 1.36 0 1 0-2.276-1.003h-1.5a2.86 2.86 0 1 1 4.789 2.11l-1.18 1.079Z",clipRule:"evenodd"}))),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),oe=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{fill:"#16CA9E",clipPath:"url(#a)"},(0,e.createElement)("path",{d:"M24 16.242a7.782 7.782 0 0 0-4.268-6.929c-.079 5.71-4.709 10.34-10.419 10.42A7.782 7.782 0 0 0 16.243 24a7.73 7.73 0 0 0 3.947-1.078l3.776 1.044-1.045-3.776A7.73 7.73 0 0 0 24 16.242Z"}),(0,e.createElement)("path",{d:"M18.328 9.164C18.328 4.111 14.218 0 9.164 0 4.111 0 0 4.11 0 9.164c0 1.647.438 3.25 1.27 4.658L.035 18.294l4.472-1.237a9.135 9.135 0 0 0 4.658 1.271c5.053 0 9.164-4.11 9.164-9.164ZM7.758 7.031H6.352A2.816 2.816 0 0 1 9.164 4.22a2.816 2.816 0 0 1 2.813 2.812 2.82 2.82 0 0 1-.915 2.076L9.867 10.2v1.097H8.461V9.58l1.652-1.512a1.408 1.408 0 0 0-.948-2.444c-.776 0-1.407.63-1.407 1.406Zm.703 5.672h1.406v1.406H8.461v-1.406Z"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),ce=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{clipPath:"url(#a)"},(0,e.createElement)("path",{fill:"#000",d:"m23.4 10.392-.002-.002L13.608.6a2.194 2.194 0 0 0-1.562-.647c-.59 0-1.145.23-1.563.647L.698 10.385a2.212 2.212 0 0 0-.006 3.13 2.197 2.197 0 0 0 1.535.648h.39v7.204a2.589 2.589 0 0 0 2.586 2.586h3.83a.703.703 0 0 0 .703-.703v-5.648c0-.651.53-1.18 1.18-1.18h2.26c.65 0 1.18.529 1.18 1.18v5.648c0 .388.314.703.702.703h3.83a2.589 2.589 0 0 0 2.586-2.586v-7.204h.362c.59 0 1.145-.23 1.563-.648.86-.86.86-2.261.001-3.123Zm-.996 2.13a.798.798 0 0 1-.568.235h-1.065a.703.703 0 0 0-.703.703v7.907c0 .65-.529 1.18-1.18 1.18h-3.127v-4.945a2.589 2.589 0 0 0-2.586-2.586h-2.259a2.59 2.59 0 0 0-2.586 2.586v4.945H5.203c-.65 0-1.18-.53-1.18-1.18V13.46a.703.703 0 0 0-.703-.703H2.273a.797.797 0 0 1-.586-.236.804.804 0 0 1 0-1.136h.001l9.79-9.79a.797.797 0 0 1 .568-.236c.214 0 .416.084.568.236l9.787 9.787a.805.805 0 0 1 .003 1.14Z"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),le=({url:t,active:n})=>t?(0,e.createElement)("img",{src:TAB_MESSAGE_ICON?.url,width:"24",height:"24"}):(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{clipPath:"url(#a)"},(0,e.createElement)("path",{fill:"#16CA9E",d:"m23.353 10.439-.002-.002-9.79-9.79A2.194 2.194 0 0 0 12 0c-.59 0-1.145.23-1.563.647L.652 10.432l-.01.01a2.212 2.212 0 0 0 .004 3.12 2.197 2.197 0 0 0 1.534.648h.39v7.204A2.589 2.589 0 0 0 5.156 24h3.83a.703.703 0 0 0 .704-.703v-5.649c0-.65.529-1.18 1.18-1.18h2.259c.65 0 1.18.53 1.18 1.18v5.649c0 .388.314.703.702.703h3.83a2.589 2.589 0 0 0 2.587-2.586V14.21h.361c.59 0 1.145-.23 1.563-.648.86-.86.86-2.261 0-3.123Z"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),ue=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{fill:"#000",clipPath:"url(#a)"},(0,e.createElement)("path",{d:"M2.768 18.719a4.668 4.668 0 0 1-2.296-.603.723.723 0 0 1-.288-1.013c.725-1.16.987-2.576.655-3.908C.495 11.815-.003 10.651 0 9.19.013 4.06 4.282-.098 9.406.002c4.949.1 9.027 4.26 9.027 9.21 0 6.465-6.775 11.004-12.757 8.508-.824.647-1.86.999-2.908.999Zm-.975-1.579c1.127.35 2.394.07 3.263-.77a.713.713 0 0 1 .803-.13c5.121 2.449 11.149-1.39 11.149-7.028 0-4.208-3.424-7.7-7.631-7.785-4.336-.086-7.94 3.426-7.951 7.766-.003 1.388.538 2.498.834 3.814a6.362 6.362 0 0 1-.467 4.133Z"}),(0,e.createElement)("path",{d:"M21.232 24a4.734 4.734 0 0 1-2.908-1c-3.181 1.328-6.965.724-9.573-1.529a.713.713 0 0 1 .931-1.079c2.314 1.998 5.702 2.447 8.459 1.13a.713.713 0 0 1 .803.13 3.305 3.305 0 0 0 3.263.77 6.335 6.335 0 0 1-.248-4.892c.41-.968.618-1.996.615-3.056-.004-1.87-.626-3.599-1.798-5.001a.713.713 0 1 1 1.094-.914A9.26 9.26 0 0 1 24 14.47a9.15 9.15 0 0 1-.719 3.594c-.503 1.459-.272 3.02.535 4.32a.723.723 0 0 1-.288 1.013 4.67 4.67 0 0 1-2.296.603ZM9.217 10.375a1.128 1.128 0 1 0 0-2.255 1.128 1.128 0 0 0 0 2.255ZM5.061 10.375a1.128 1.128 0 1 0 0-2.255 1.128 1.128 0 0 0 0 2.255ZM13.373 10.375a1.128 1.128 0 1 0 0-2.255 1.128 1.128 0 0 0 0 2.255Z"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),he=()=>(0,e.createElement)("svg",{width:21,height:24,viewBox:"0 0 21 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M8.55 19.4v-.75H5.6c-.964 0-1.612-.002-2.095-.066-.461-.063-.659-.17-.789-.3l-.53.53.53-.53c-.13-.13-.237-.328-.3-.79-.064-.482-.066-1.13-.066-2.094V9.5c0-.964.002-1.612.067-2.095.062-.461.169-.659.3-.789s.327-.237.788-.3c.483-.064 1.131-.066 2.095-.066h9c.964 0 1.612.002 2.095.067.461.062.659.169.789.3l.53-.531-.53.53c.13.13.237.328.3.79.064.482.066 1.13.066 2.094v5.9c0 .964-.002 1.612-.067 2.095-.062.461-.169.659-.3.789s-.327.237-.788.3c-.483.064-1.131.066-2.095.066h-1.743l-.21.182-4.097 3.531z",stroke:"#344054",strokeWidth:"1.5"}),(0,e.createElement)("path",{d:"M6.2 14c2.3 2 5.3 2 7.8 0",stroke:"#344054",strokeWidth:"1.6"}),(0,e.createElement)("path",{d:"M7 10.1v1.5m6.2-1.5v1.5M10.1 7V1",stroke:"#344054",strokeWidth:"1.5"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.6 13.2v-3.1H0v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#344054"}),(0,e.createElement)("circle",{cx:"10.1",cy:"1.5",r:"1.5",fill:"#344054"})),de=()=>(0,e.createElement)("svg",{width:22,height:24,viewBox:"0 0 22 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("mask",{id:"a",fill:"#fff"},(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z"})),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z",fill:"#00B682"}),(0,e.createElement)("path",{d:"m18.915 6.086-1.061 1.06zm0 12.728-1.061-1.06zm-4.879.586v-1.5h-.557l-.422.364zM8.7 24H7.2v3.274l2.48-2.138zm0-4.6h1.5v-1.5H8.7zm-5.614-.586 1.06-1.06zm12.314-4.19.937 1.172 1.172-.937-.938-1.171zm-8.825-.02-1.131-.985-.985 1.132 1.132.985zm7.826-1.229 1.17-.937-.936-1.171-1.171.937zm-6.776.021.985-1.132-1.132-.984-.985 1.132zM7.15 10.1V8.6h-1.5v1.5zm0 1.5h-1.5v1.5h1.5zm1.5-1.5h1.5V8.6h-1.5zm0 1.5v1.5h1.5v-1.5zm4.7 0h-1.5v1.5h1.5zm0-1.5V8.6h-1.5v1.5zm1.5 1.5v1.5h1.5v-1.5zm0-1.5h1.5V8.6h-1.5zM15.5 4h-9v3h9zm4.475 1.025c-.618-.618-1.37-.843-2.08-.938C17.227 3.997 16.4 4 15.5 4v3c.986 0 1.574.003 1.995.06a1.4 1.4 0 0 1 .354.084l.006.003h-.001q-.002-.001 0 0zM21 9.5c0-.9.004-1.727-.086-2.394-.096-.711-.32-1.463-.939-2.08l-2.121 2.12q0 .002 0 0l.003.005.01.02c.017.044.048.141.073.334.057.422.06 1.01.06 1.995zm0 5.9V9.5h-3v5.9zm-1.025 4.475c.618-.618.843-1.37.939-2.08.09-.668.086-1.495.086-2.395h-3c0 .985-.003 1.573-.06 1.995a1.4 1.4 0 0 1-.083.354l-.003.005q0-.002 0 0zM15.5 20.9c.9 0 1.727.003 2.395-.087.71-.095 1.462-.32 2.08-.938l-2.121-2.121q-.002 0 0 0l-.005.002-.02.01c-.044.018-.14.048-.334.074-.421.057-1.01.06-1.995.06zm-1.464 0H15.5v-3h-1.464zM9.68 25.136l5.336-4.6-1.96-2.272-5.335 4.6zM7.2 19.4V24h3v-4.6zm-.7 1.5h2.2v-3H6.5zm-4.475-1.025c.619.618 1.37.843 2.081.938.668.09 1.494.087 2.394.087v-3c-.985 0-1.573-.003-1.994-.06a1.4 1.4 0 0 1-.354-.084l-.006-.003h.001q0 .002 0 0zM1 15.4c0 .9-.003 1.727.087 2.394.096.711.32 1.463.938 2.08l2.122-2.12q-.002-.002 0 0l-.003-.005-.01-.02a1.4 1.4 0 0 1-.074-.334C4.004 16.973 4 16.385 4 15.4zm0-5.9v5.9h3V9.5zm1.025-4.475c-.618.618-.842 1.37-.938 2.08C.997 7.774 1 8.6 1 9.5h3c0-.985.004-1.573.06-1.995a1.4 1.4 0 0 1 .084-.354l.003-.005q-.002.002 0 0zM6.5 4c-.9 0-1.726-.003-2.394.087-.711.095-1.462.32-2.08.938l2.12 2.121h.001-.001l.006-.002.02-.01c.044-.018.14-.048.334-.074C4.927 7.003 5.516 7 6.5 7zm7.963 9.453c-2.257 1.806-4.878 1.78-6.903.019l-1.97 2.264c3.2 2.783 7.444 2.701 10.746.06zm-1.234.86 1 1.249 2.342-1.874-1-1.25zm-6.588.215c1.24 1.079 2.72 1.67 4.283 1.672 1.558.001 3.077-.584 4.414-1.653l-1.874-2.343c-.885.708-1.759.997-2.537.996-.774 0-1.57-.286-2.317-.936zm1.066 1.06 1.05-1.207-2.264-1.969-1.05 1.207zM5.65 10.1v1.5h3v-1.5zm3-1.5h-1.5v3h1.5zm1.5 3v-1.5h-3v1.5zm-3 1.5h1.5v-3h-1.5zm7.7-1.5v-1.5h-3v1.5zm0-1.5h-1.5v3h1.5zm-1.5 0v1.5h3v-1.5zm0 1.5h1.5v-3h-1.5z",mask:"url(#a)"}),(0,e.createElement)("path",{d:"M11 7V1",stroke:"#00B682",strokeWidth:"1.5"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.5 13.2v-3.1H.9v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#00B682"}),(0,e.createElement)("circle",{cx:11,cy:"1.5",r:"1.5",fill:"#00B682"})),fe=({icon:t,active:n})=>{if(null!=re?.[t])return(0,e.createElement)("img",{src:re[t],width:"24",height:"24"});const r={home:{default:(0,e.createElement)(ce,null),active:(0,e.createElement)(le,null)},feedback_tab:{default:(0,e.createElement)(ue,null),active:(0,e.createElement)(ue,null)},resources:{default:(0,e.createElement)(se,null),active:(0,e.createElement)(oe,null)},chatbot:{default:(0,e.createElement)(he,null),active:(0,e.createElement)(de,null)}};return n?r?.[t]?.active:r?.[t].default},me=(0,t.createContext)(),pe=()=>{const{Tabs:n,setTab:r,activeTabClass:a,setactiveTabClass:i}=(0,t.useContext)(me);return(0,e.createElement)("ul",{className:"betterdocs-ia-tabs"},n.filter((e=>1==e?.showTab&&"tab"==e?.type)).map((t=>(0,e.createElement)("li",{className:a==t?.id?t?.class+" active":t?.class,key:Math.random(),onClick:()=>(e=>{let t=n.find((t=>t?.id==e));"feedback_tab"!=e&&i(e),r(t?.id)})(t?.id)},t?.icon,(0,e.createElement)("p",null,t?.title)))))},ve=()=>{const{selectedTab:n,Tabs:r}=(0,t.useContext)(me),a=r?.find((e=>e.id==n))?.component,i=r?.find((e=>e.id==n))?.require_components,s="home"==n?" home-content":"resources"==n?" resources-content":"";return(0,e.createElement)("div",{className:`betterdocs-ia-main-content${s}`},a&&(0,e.createElement)(a,i))},ye=window.wp.i18n,ge=window.wp.url,Ce=({queryKey:e})=>{const[t,n]=e,{parent:r,taxonomy:a,terms:i,terms_per_page:s,term_id:c,post_type:l,post_id:u,post_ids:h,posts_order:d,posts_orderby:f,posts_per_page:m,terms_orderby:p,terms_order:v}=n;let y="wp/v2/",g={},C={hide_empty:!0};return null!=a?(y+=a,i?.length>0&&(C.include=i.join(",")),null!=r&&0==r&&(C.parent=0),null!=p&&(C.orderby=p),null!=v&&(C.order=v),null!=s&&(C.per_page=s),null!=c&&"doc_category"==a&&(y=y.replace(a,""),y+="docs",C[a]=c),null!=c&&"betterdocs_faq_category"==a&&(y=y.replace(a,""),y+="betterdocs_faq",C[a]=c),fetch(`${o}${(0,ge.addQueryArgs)(y,C)}`,{method:"GET"}).then((e=>e.json()))):null!=l?(y+=l+"/",h?.length>0&&(g.include=h.join(",")),null!=u&&(y+=u),null!=d&&(g.order=d),null!=f&&(g.orderby=f),null!=m&&(g.per_page=m),fetch(`${o}${(0,ge.addQueryArgs)(y,g)}`,{method:"GET"}).then((e=>e.json()))):void 0},be=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("path",{fill:"#fff",d:"M15.675 21.3 6.9 12.525a.762.762 0 0 1-.175-.25.734.734 0 0 1-.05-.275c0-.1.017-.192.05-.275a.762.762 0 0 1 .175-.25L15.675 2.7a.948.948 0 0 1 .7-.275c.283 0 .517.092.7.275.2.2.3.437.3.712 0 .275-.1.513-.3.713L9.2 12l7.875 7.875c.217.217.317.458.3.725a1.011 1.011 0 0 1-.3.675c-.2.2-.438.3-.712.3a.933.933 0 0 1-.688-.275Z"})),Ee=({layout:n})=>{const{Tabs:r,selectedTab:a,setNavigationHistory:i,setTab:s,setactiveTabClass:o,mainWrapperStyleRef:d}=(0,t.useContext)(me);let f="single-doc"==n?"content-icon-back":"header__back header__button";const{current:m}=d,{style:p}=m,v=()=>{const e=r.filter((e=>e.showTab&&"tab"===e.type));return e.length>0?e[0].id:null};return(0,e.createElement)("div",{className:f,onClick:()=>{if("resources"===a){const e=(()=>{const e={home:c||l,resources:R||w,chatbot:h,feedback_tab:u?.show},t=["home","resources","chatbot","feedback_tab"].filter((t=>"resources"!==t&&e[t]));return t.length>0?t[0]:v()})();if(e)return s(e),o(e),i([{id:e}]),void(""!=p?.width&&""!=p?.height&&Object.assign(p,{width:"",height:""}))}i((e=>{let t=[...e];if(t?.length>0){if(t.pop(),t?.length<1)if(c||l)t.push({id:"home"});else if(R||w)t.push({id:"resources"});else if(h)t.push({id:"chatbot"});else if(u?.show)t.push({id:"feedback_tab"});else{const e=v();e&&t.push({id:e})}let e=t[t.length-1];return s(e?.id),"home"!==e?.id&&"resources"!==e?.id&&"chatbot"!==e?.id&&"feedback_tab"!==e?.id||o(e?.id),t}return t})),""!=p?.width&&""!=p?.height&&Object.assign(p,{width:"",height:""})}},(0,e.createElement)(be,null))},we=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16"},(0,e.createElement)("path",{d:"M7.29289 1.2876C3.98708 1.2876 1.29419 3.98048 1.29419 7.28631C1.29419 10.5921 3.98708 13.2902 7.29289 13.2902C8.7049 13.2902 10.0035 12.7954 11.0299 11.9738L13.5286 14.4712C13.6547 14.5921 13.8231 14.6588 13.9977 14.657C14.1724 14.6552 14.3394 14.5851 14.463 14.4617C14.5866 14.3382 14.657 14.1713 14.659 13.9967C14.661 13.822 14.5946 13.6535 14.4739 13.5272L11.9752 11.0285C12.7975 10.0006 13.2929 8.69995 13.2929 7.28631C13.2929 3.98048 10.5987 1.2876 7.29289 1.2876ZM7.29289 2.62095C9.87811 2.62095 11.9583 4.70108 11.9583 7.28631C11.9583 9.87153 9.87811 11.9569 7.29289 11.9569C4.70766 11.9569 2.62752 9.87153 2.62752 7.28631C2.62752 4.70108 4.70766 2.62095 7.29289 2.62095Z"})),_e=({onClick:t})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"currentColor",stroke:"currentColor",strokeWidth:0,viewBox:"0 0 24 24",onClick:t},(0,e.createElement)("path",{stroke:"none",d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),Se=({searchCallback:t,searchKeyword:n})=>(0,e.createElement)("label",{className:"betterdocs-ia-search"},(0,e.createElement)("input",{className:"betterdocs-ia-search-field",onChange:e=>t(e?.target?.value),name:"betterdocs-ia-search",placeholder:m?.SEARCH_PLACEHOLDER,value:n}),(0,e.createElement)("div",{className:"betterdocs-ia-search-icon"},n?.length>0?(0,e.createElement)(_e,{onClick:()=>t("")}):(0,e.createElement)(we,null))),Oe=()=>(0,e.createElement)("svg",{width:"190",viewBox:"0 0 366 85",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M63.063 32.6198H44.0562C42.1943 32.6198 40.6873 31.1128 40.6873 29.2509C40.6873 27.389 42.1943 25.882 44.0562 25.882H63.063C64.9249 25.882 66.4319 27.389 66.4319 29.2509C66.4319 31.1128 64.9249 32.6198 63.063 32.6198Z",fill:"white"}),(0,e.createElement)("path",{d:"M55.4024 45.7461H36.4065C34.5446 45.7461 33.0376 44.2391 33.0376 42.3772C33.0376 40.5152 34.5446 39.0082 36.4065 39.0082H55.4024C57.2643 39.0082 58.7713 40.5152 58.7713 42.3772C58.7713 44.2391 57.2643 45.7461 55.4024 45.7461Z",fill:"white"}),(0,e.createElement)("path",{d:"M47.6545 59.1016H28.6476C26.7857 59.1016 25.2787 57.5946 25.2787 55.7327C25.2787 53.8708 26.7857 52.3638 28.6476 52.3638H47.6545C49.5164 52.3638 51.0234 53.8708 51.0234 55.7327C51.0234 57.5946 49.5164 59.1016 47.6545 59.1016Z",fill:"white"}),(0,e.createElement)("path",{d:"M77.0901 40.2531C76.97 40.002 76.8171 39.7727 76.6479 39.5597L81.7204 30.8235C84.4832 26.0731 84.4996 20.3891 81.7586 15.6224C79.0121 10.8502 74.0925 8 68.5832 8H45.6342C40.2559 8 35.2217 10.8993 32.497 15.5678L10.0831 54.1765C7.32026 58.9269 7.30388 64.6109 10.0449 69.3776C12.7913 74.1498 17.7109 77 23.2202 77H62.5443C69.5497 77 75.9599 73.4618 79.6892 67.5266C83.4185 61.5969 83.8334 54.2857 80.7976 47.9738L77.0901 40.2531ZM29.0189 69.8745H23.2148C20.2936 69.8745 17.6782 68.362 16.2203 65.8285C14.7679 63.3005 14.7734 60.2865 16.2422 57.7639L38.667 19.1496C40.114 16.6653 42.7894 15.1255 45.6451 15.1255H68.5941C71.5153 15.1255 74.1307 16.638 75.5886 19.1715C77.041 21.6995 77.0355 24.7135 75.5668 27.2361L53.131 65.8558C51.7004 68.3293 49.0359 69.869 46.1747 69.869H29.0189V69.8745Z",fill:"white"}),(0,e.createElement)("path",{d:"M124.384 42.1533C125.82 41.4325 126.913 40.4279 127.65 39.1338C128.387 37.8397 128.758 36.3819 128.758 34.7657C128.758 33.5044 128.534 32.3359 128.081 31.2603C127.633 30.1791 126.929 29.2455 125.979 28.4537C125.023 27.662 123.822 27.045 122.364 26.5918C120.906 26.1441 119.17 25.9148 117.155 25.9148H104.433C104.127 25.9148 103.882 26.1605 103.882 26.4662V59.4183C103.882 59.6749 104.056 59.8988 104.302 59.9534C105.672 60.2646 107.212 60.5212 108.927 60.7178C110.816 60.9362 112.82 61.0399 114.944 61.0399C117.641 61.0399 119.951 60.7997 121.878 60.3137C123.8 59.8278 125.378 59.1343 126.601 58.2389C127.824 57.3379 128.725 56.235 129.299 54.9191C129.872 53.6087 130.161 52.1399 130.161 50.5237C130.161 48.4707 129.659 46.7398 128.649 45.3147C127.639 43.895 126.219 42.8412 124.384 42.1587V42.1533ZM110.516 31.9209C110.516 31.6152 110.767 31.364 111.073 31.364H116.129C118.214 31.3312 119.732 31.708 120.688 32.4997C121.638 33.2914 122.118 34.3725 122.118 35.7376C122.118 37.1791 121.643 38.2984 120.688 39.112C119.732 39.9201 118.231 40.3241 116.183 40.3241H111.078C110.772 40.3241 110.521 40.0729 110.521 39.7672V31.9264L110.516 31.9209ZM121.605 54.4004C120.328 55.2631 118.269 55.6944 115.43 55.6944C114.387 55.6944 113.486 55.6671 112.732 55.6125C112.126 55.5689 111.531 55.487 110.952 55.3614C110.701 55.3068 110.521 55.0774 110.521 54.8208V46.0464C110.521 45.7406 110.767 45.4949 111.073 45.4949H116.669C119.006 45.4949 120.732 45.9426 121.851 46.8435C122.965 47.7445 123.522 48.9839 123.522 50.5674C123.522 52.26 122.883 53.5322 121.605 54.4004Z",fill:"white"}),(0,e.createElement)("path",{d:"M273.916 31.2275C272.278 29.715 270.302 28.5466 267.981 27.7221C265.66 26.8976 263.078 26.4826 260.239 26.4826H250.612C250.312 26.4826 250.066 26.7283 250.066 27.0287V60.5213C250.066 60.8216 250.312 61.0673 250.612 61.0673H260.239C263.078 61.0673 265.66 60.6523 267.981 59.8278C270.302 59.0033 272.278 57.8239 273.916 56.2951C275.554 54.7662 276.821 52.9426 277.722 50.8186C278.623 48.6946 279.071 46.3412 279.071 43.7531C279.071 41.165 278.623 38.8062 277.722 36.6877C276.821 34.5637 275.554 32.7509 273.916 31.2384V31.2275ZM271.541 48.5471C270.984 49.9504 270.176 51.1571 269.111 52.1617C268.052 53.1719 266.747 53.9527 265.202 54.5096C263.657 55.0666 261.877 55.345 259.862 55.345H257.257C256.952 55.345 256.706 55.0993 256.706 54.7935V32.7564C256.706 32.4506 256.952 32.2049 257.257 32.2049H259.862C261.877 32.2049 263.657 32.4833 265.202 33.0403C266.747 33.5972 268.052 34.3889 269.111 35.4155C270.171 36.442 270.979 37.6705 271.541 39.112C272.098 40.5535 272.376 42.1314 272.376 43.8623C272.376 45.5932 272.098 47.1548 271.541 48.558V48.5471Z",fill:"white"}),(0,e.createElement)("path",{d:"M305.798 38.7516C304.581 37.5995 303.172 36.7095 301.567 36.0816C299.961 35.4537 298.225 35.137 296.358 35.137C294.49 35.137 292.797 35.4591 291.176 36.1089C289.554 36.7586 288.14 37.665 286.944 38.8335C285.743 40.002 284.793 41.3779 284.089 42.9614C283.384 44.5448 283.029 46.2538 283.029 48.0885C283.029 50.0323 283.373 51.7959 284.061 53.3739C284.749 54.9573 285.699 56.3224 286.917 57.4745C288.135 58.6266 289.554 59.5166 291.176 60.1445C292.797 60.7724 294.528 61.0891 296.358 61.0891C298.187 61.0891 299.912 60.7669 301.517 60.1172C303.123 59.4674 304.531 58.572 305.749 57.4199C306.967 56.2678 307.928 54.8918 308.632 53.292C309.336 51.6922 309.691 49.9558 309.691 48.083C309.691 46.2102 309.347 44.4301 308.659 42.8467C307.971 41.2633 307.021 39.8982 305.804 38.7461L305.798 38.7516ZM302.675 50.8895C302.342 51.7522 301.861 52.5057 301.244 53.1555C300.627 53.8052 299.901 54.3076 299.077 54.6679C298.247 55.0283 297.34 55.2085 296.352 55.2085C295.364 55.2085 294.457 55.0283 293.627 54.6679C292.798 54.3076 292.077 53.8052 291.46 53.1555C290.843 52.5057 290.368 51.7522 290.029 50.8895C289.696 50.0268 289.527 49.0931 289.527 48.083C289.527 47.0729 289.696 46.1392 290.029 45.2765C290.362 44.4138 290.843 43.6603 291.46 43.0105C292.077 42.3608 292.798 41.8584 293.627 41.498C294.457 41.1377 295.364 40.9575 296.352 40.9575C297.34 40.9575 298.247 41.1377 299.077 41.498C299.907 41.8584 300.627 42.3608 301.244 43.0105C301.861 43.6603 302.336 44.4138 302.675 45.2765C303.008 46.1392 303.177 47.0729 303.177 48.083C303.177 49.0931 303.008 50.0268 302.675 50.8895Z",fill:"white"}),(0,e.createElement)("path",{d:"M331.603 54.7226C330.576 55.0447 329.31 55.2085 327.797 55.2085C326.683 55.2085 325.657 55.0283 324.723 54.668C323.789 54.3076 322.976 53.8162 322.293 53.1828C321.611 52.5549 321.081 51.7959 320.699 50.9168C320.322 50.0377 320.131 49.0931 320.131 48.083C320.131 47.0729 320.322 46.0791 320.699 45.1946C321.076 44.3155 321.605 43.5565 322.293 42.9286C322.976 42.3007 323.795 41.8038 324.75 41.4434C325.7 41.0831 326.754 40.9029 327.906 40.9029C329.31 40.9029 330.522 41.0722 331.548 41.4161C331.92 41.5417 332.264 41.6728 332.586 41.8147C332.952 41.9731 333.356 41.7165 333.356 41.3179V36.5402C333.356 36.3109 333.214 36.1034 333.001 36.027C332.264 35.7649 331.461 35.5574 330.582 35.4045C329.555 35.2243 328.414 35.137 327.158 35.137C325.253 35.137 323.473 35.47 321.818 36.1362C320.164 36.8023 318.733 37.7196 317.527 38.8881C316.32 40.0566 315.37 41.4216 314.665 42.9887C313.967 44.5557 313.612 46.2539 313.612 48.0885C313.612 49.9231 313.945 51.5775 314.611 53.161C315.277 54.7444 316.205 56.1204 317.39 57.2888C318.575 58.4573 320 59.3855 321.654 60.0681C323.309 60.7506 325.144 61.0946 327.158 61.0946C328.594 61.0946 329.79 60.9799 330.746 60.7451C331.537 60.5486 332.285 60.3356 332.99 60.1063C333.214 60.0353 333.361 59.8224 333.361 59.5876V54.8754C333.361 54.4878 332.968 54.2257 332.608 54.3731C332.296 54.4987 331.963 54.6188 331.608 54.728L331.603 54.7226Z",fill:"white"}),(0,e.createElement)("path",{d:"M245.299 35.137H244.382C242.438 35.137 240.8 35.5956 239.473 36.5129C238.141 37.4303 237.207 38.5714 236.667 39.9365L236.334 35.7376C236.312 35.4373 236.044 35.208 235.739 35.2353L230.884 35.6775C230.59 35.7048 230.371 35.9615 230.388 36.2563L230.835 42.9013V60.5049C230.835 60.8106 231.081 61.0564 231.387 61.0564H236.76C237.065 61.0564 237.311 60.8106 237.311 60.5049V48.4052C237.311 46.2812 237.884 44.5558 239.036 43.2235C240.189 41.8912 241.805 41.2251 243.891 41.2251C244.338 41.2251 244.77 41.2469 245.174 41.296C245.501 41.3343 245.78 41.0776 245.78 40.75V35.4919C245.78 35.4919 245.758 35.1261 245.294 35.1261L245.299 35.137Z",fill:"white"}),(0,e.createElement)("path",{d:"M223.481 54.0236C222.907 54.3458 222.296 54.6188 221.646 54.8317C220.996 55.0501 220.281 55.2194 219.489 55.345C218.698 55.4706 217.819 55.5361 216.847 55.5361C214.362 55.5361 212.424 54.9682 211.021 53.838C209.617 52.7023 208.809 51.2226 208.591 49.388C209.132 50.0377 209.978 50.5401 211.124 50.9004C212.277 51.2608 213.609 51.441 215.116 51.441C218.496 51.4082 221.111 50.6656 222.968 49.2296C224.819 47.7936 225.747 45.757 225.747 43.1306C225.747 40.8264 224.911 38.9209 223.241 37.4138C221.57 35.9014 219.113 35.1479 215.875 35.1479C213.898 35.1479 212.064 35.4919 210.371 36.1744C208.678 36.8569 207.215 37.8015 205.976 39.0082C204.736 40.2149 203.77 41.6345 203.087 43.2726C202.405 44.9106 202.061 46.6961 202.061 48.6399C202.061 50.5837 202.394 52.2709 203.06 53.8216C203.726 55.3668 204.654 56.6827 205.839 57.7584C207.024 58.8395 208.449 59.664 210.103 60.2427C211.758 60.8161 213.576 61.1054 215.553 61.1054C217.709 61.1054 219.544 60.8816 221.056 60.4284C222.422 60.0243 223.519 59.5657 224.354 59.0579C224.518 58.9596 224.606 58.7849 224.606 58.5938V54.3294C224.606 53.909 224.147 53.6414 223.781 53.8598C223.683 53.9199 223.579 53.9799 223.481 54.0345V54.0236ZM211.07 42.2898C212.435 41.1213 214.035 40.5371 215.869 40.5371C216.95 40.5371 217.813 40.7773 218.457 41.2633C219.107 41.7492 219.429 42.4426 219.429 43.3381C219.429 43.9879 219.238 44.5612 218.862 45.0635C218.485 45.5659 217.873 45.9808 217.027 46.303C216.181 46.6251 215.067 46.8599 213.68 47.0019C212.293 47.1438 210.595 47.1821 208.58 47.1111C208.869 45.0635 209.694 43.4528 211.064 42.2843L211.07 42.2898Z",fill:"white"}),(0,e.createElement)("path",{d:"M355.655 48.225C354.393 46.9855 352.253 46.0409 349.234 45.3912C348.049 45.14 347.077 44.9161 346.318 44.7141C345.564 44.5175 344.975 44.3101 344.565 44.0917C344.15 43.8732 343.866 43.6494 343.702 43.4146C343.539 43.1798 343.462 42.9013 343.462 42.5792C343.462 41.105 344.975 40.3678 347.994 40.3678C349.359 40.3678 350.719 40.5316 352.067 40.8538C352.974 41.0722 353.858 41.3834 354.721 41.782C355.081 41.9458 355.491 41.6783 355.491 41.2797V36.846C355.491 36.6222 355.36 36.4201 355.158 36.3328C354.202 35.9396 353.165 35.6448 352.04 35.4537C350.779 35.2353 349.43 35.1315 347.994 35.1315C346.378 35.1315 344.909 35.3117 343.599 35.6721C342.283 36.0325 341.153 36.5457 340.197 37.2119C339.242 37.878 338.515 38.6861 338.013 39.6416C337.511 40.5972 337.26 41.6455 337.26 42.7976C337.26 44.8506 337.909 46.4777 339.203 47.679C340.497 48.8857 342.55 49.7757 345.351 50.349C346.575 50.5674 347.574 50.7803 348.344 50.9987C349.119 51.2171 349.731 51.4465 350.178 51.6976C350.626 51.9488 350.932 52.2218 351.096 52.5057C351.259 52.7951 351.336 53.1337 351.336 53.5323C351.336 55.0775 349.698 55.8528 346.427 55.8528C344.811 55.8528 343.178 55.6017 341.546 55.0993C340.465 54.7662 339.433 54.3076 338.455 53.7343C338.089 53.5213 337.636 53.7834 337.636 54.2093V58.6976C337.636 58.905 337.751 59.1016 337.937 59.1944C339.209 59.8333 340.53 60.3028 341.895 60.6031C343.369 60.9253 345.007 61.0891 346.804 61.0891C350.042 61.0891 352.641 60.3957 354.601 59.0142C356.561 57.6274 357.544 55.7327 357.544 53.3193C357.544 51.1625 356.916 49.459 355.655 48.2195V48.225Z",fill:"white"}),(0,e.createElement)("path",{d:"M196.158 54.941C195.405 55.2304 194.433 55.3723 193.242 55.3723C192.052 55.3723 190.987 55.0229 190.141 54.3185C189.295 53.6196 188.874 52.402 188.874 50.6766V41.1814H196.961C197.261 41.1814 197.507 40.9357 197.507 40.6354V36.2236C197.507 35.9232 197.261 35.6775 196.961 35.6775H188.874V28.421C188.874 28.0934 188.585 27.8368 188.257 27.875L182.934 28.5466C182.661 28.5793 182.453 28.8141 182.453 29.0871L182.399 51.7522C182.399 54.8809 183.245 57.2179 184.932 58.7685C186.625 60.3138 188.869 61.0891 191.675 61.0891C193.04 61.0891 194.203 60.9635 195.154 60.7124C196.104 60.4612 196.89 60.1554 197.501 59.7951V59.7732V55.1921C197.501 54.7935 197.092 54.5315 196.732 54.6953C196.551 54.7772 196.36 54.8591 196.153 54.941H196.158Z",fill:"white"}),(0,e.createElement)("path",{d:"M176.212 54.941C175.459 55.2304 174.487 55.3723 173.297 55.3723C172.106 55.3723 171.041 55.0229 170.195 54.3185C169.349 53.6196 168.928 52.402 168.928 50.6766V41.1814H176.993C177.293 41.1814 177.539 40.9357 177.539 40.6354V36.229C177.539 35.9287 177.293 35.683 176.993 35.683H168.928V28.4264C168.928 28.0988 168.639 27.8422 168.311 27.8804L162.988 28.552C162.715 28.5848 162.507 28.8196 162.507 29.0926L162.453 51.7577C162.453 54.8864 163.299 57.2233 164.986 58.774C166.679 60.3192 168.923 61.0946 171.729 61.0946C173.094 61.0946 174.257 60.969 175.208 60.7178C176.005 60.5103 176.682 60.2592 177.239 59.9752C177.419 59.8824 177.523 59.6913 177.523 59.4893V55.214C177.523 54.8154 177.113 54.5478 176.747 54.7171C176.578 54.7935 176.392 54.87 176.201 54.9464L176.212 54.941Z",fill:"white"}),(0,e.createElement)("path",{d:"M155.819 54.0236C155.245 54.3458 154.634 54.6188 153.984 54.8317C153.334 55.0501 152.619 55.2194 151.827 55.345C151.035 55.4706 150.156 55.5361 149.184 55.5361C146.7 55.5361 144.762 54.9682 143.358 53.838C141.955 52.7023 141.147 51.2226 140.929 49.388C141.469 50.0377 142.316 50.5401 143.462 50.9004C144.614 51.2608 145.947 51.441 147.454 51.441C150.833 51.4082 153.449 50.6656 155.305 49.2296C157.156 47.7936 158.085 45.757 158.085 43.1306C158.085 40.8264 157.249 38.9209 155.578 37.4138C153.908 35.9014 151.45 35.1479 148.213 35.1479C146.236 35.1479 144.401 35.4919 142.709 36.1744C141.016 36.8569 139.553 37.8015 138.313 39.0082C137.074 40.2149 136.107 41.6345 135.425 43.2726C134.742 44.9106 134.398 46.6961 134.398 48.6399C134.398 50.5837 134.731 52.2709 135.398 53.8216C136.064 55.3668 136.992 56.6827 138.177 57.7584C139.362 58.8395 140.787 59.664 142.441 60.2427C144.096 60.8161 145.914 61.1054 147.89 61.1054C150.047 61.1054 151.882 60.8816 153.394 60.4284C154.765 60.0189 155.868 59.5602 156.703 59.0524C156.861 58.9541 156.954 58.7794 156.954 58.5883V54.3185C156.954 53.898 156.496 53.6305 156.135 53.8489C156.032 53.9144 155.928 53.9745 155.824 54.0345L155.819 54.0236ZM143.408 42.2898C144.773 41.1213 146.372 40.5371 148.207 40.5371C149.288 40.5371 150.151 40.7773 150.795 41.2633C151.445 41.7492 151.767 42.4426 151.767 43.3381C151.767 43.9879 151.576 44.5612 151.199 45.0635C150.823 45.5659 150.211 45.9808 149.365 46.303C148.518 46.6251 147.404 46.8599 146.018 47.0019C144.631 47.1438 142.933 47.1821 140.918 47.1111C141.207 45.0635 142.032 43.4528 143.402 42.2843L143.408 42.2898Z",fill:"white"})),Te=({headingContent:t,headingLayout:n,headingSubContent:r,enableLogo:a,enableSearch:i,extraClass:s,enableBackButton:o,searchCallback:c,searchKeyword:l})=>{let u=null!=s?" "+s:"",h=t&&"home-layout"==n?(0,e.createElement)("h1",{className:"betterdocs-title"},t):!t||"resources-list"!=n&&"resources-category"!=n?"":(0,e.createElement)("h2",null,t),d=a?null==G||Array.isArray(G)?(0,e.createElement)("span",{className:"betterdocs-logo"},(0,e.createElement)(Oe,null)):(0,e.createElement)("img",{src:G?.url,width:100,height:20}):"",f=r?(0,e.createElement)("p",{className:"betterdocs-info"},r):"",m=i?(0,e.createElement)(Se,{searchCallback:c,searchKeyword:l}):"",p=o?(0,e.createElement)(Ee,null):"";return(0,e.createElement)("div",{className:`betterdocs-ia-common-header${u}`},(0,e.createElement)("div",{className:"betterdocs-ia-header-group"},d,p,h,f),m)},qe=()=>(0,e.createElement)("div",{className:"generic-loader"},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:40,height:40,preserveAspectRatio:"xMidYMid",style:{background:"0 0",shapeRendering:"auto"},viewBox:"0 0 100 100"},(0,e.createElement)("circle",{cx:50,cy:50,r:26,fill:"none",stroke:"#16ca9e",strokeDasharray:"122.52211349000194 42.840704496667314",strokeWidth:10},(0,e.createElement)("animateTransform",{attributeName:"transform",dur:"1s",keyTimes:"0;1",repeatCount:"indefinite",type:"rotate",values:"0 50 50;360 50 50"}))));var Ae=n(957);const Me=window.wp.apiFetch;var Re=n.n(Me);function Fe(e,t){const n=`wp/v2/${e}`,r={search:t};return"docs"==e&&(r.orderby="relevance"),Re()({url:`${o}${(0,ge.addQueryArgs)(n,r)}`})}function Ne(e,t){const n={s:e};t&&(n.no_result=1);try{return Re()({url:`${o}${(0,ge.addQueryArgs)("betterdocs/v1/search-insert",n)}`})}catch(e){return console.error("Please update BetterDocs free version: ",e),!1}}const ke=(e,n,r)=>{const a=((e,n=500)=>{const[r,a]=(0,t.useState)(e);return(0,t.useEffect)((()=>{let t=setTimeout((()=>{a(e)}),n);return()=>{clearTimeout(t)}}),[e,n]),r})(e,300),[i,s]=(0,t.useState)([]),[o,c]=(0,t.useState)(!1),[l,u]=(0,t.useState)(!1);return(0,t.useEffect)((()=>{0!==a.length?(c(!0),async function(){var t,i,o;switch(!0){case n&&r:u(!0),t=Fe("docs",a),i=Fe("betterdocs_faq",a),o=await Promise.all([t,i]);let[c,l]=o;a.length>=p&&(c.length>0?await Ne(e,0):await Ne(e,1)),s([...c,...l]),u(!1);break;case n:u(!0),t=await Fe("docs",a),a.length>=p&&(t.length?Ne(a,0):Ne(a,1)),s([...t]),u(!1);break;case r:u(!0),i=await Fe("betterdocs_faq",a),a.length>=p&&(i.length?Ne(a,0):Ne(a,1)),s([...i]),u(!1)}}()):c(!1)}),[a]),[o,l,i]},Pe=({DocList:n})=>{const[r,a]=(0,t.useState)(""),{termInfo:i}=(0,t.useContext)(me),{term_id:s,taxonomy:o,term_name:c}=i,[l,u,h]=ke(r,R,w),{data:d,isLoading:f,isSuccess:p}=(0,Ae.useQuery)({queryKey:["taxDocs",{taxonomy:o,term_id:s,terms_per_page:100}],queryFn:Ce,staleTime:1/0});return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Te,{enableBackButton:!0,headingContent:K,headingLayout:"resources-list",enableSearch:!0,extraClass:"list-page-header",searchCallback:a,searchKeyword:r}),f&&!l&&(0,e.createElement)(qe,null),p&&!l&&(0,e.createElement)(n,{docList:d,headingContent:c,contentType:"post_type"}),u&&l&&(0,e.createElement)(qe,null),!u&&l&&(0,e.createElement)(n,{docList:h,contentType:"post_type",noContent:m?.OOPS,noSubContent:m?.NOT_FOUND}))},xe=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:67,height:14,fill:"none"},(0,e.createElement)("path",{fill:"#00B682",d:"M10.871 5.14H7.293a.634.634 0 1 1 0-1.268h3.578a.634.634 0 1 1 0 1.268ZM9.429 7.611H5.853a.634.634 0 1 1 0-1.268h3.576a.634.634 0 1 1 0 1.268ZM7.97 10.125H4.392a.634.634 0 1 1 0-1.268H7.97a.634.634 0 1 1 0 1.268Z"}),(0,e.createElement)("path",{fill:"#00B682",d:"M13.511 6.577a.685.685 0 0 0-.083-.13l.955-1.645c.52-.894.523-1.964.007-2.861A2.83 2.83 0 0 0 11.91.506H7.59c-1.012 0-1.96.546-2.473 1.424L.898 9.198a2.829 2.829 0 0 0-.007 2.861 2.83 2.83 0 0 0 2.48 1.435h7.402A3.79 3.79 0 0 0 14 11.711a3.79 3.79 0 0 0 .209-3.68l-.698-1.454Zm-9.049 5.576H3.37c-.55 0-1.042-.285-1.317-.762a1.5 1.5 0 0 1 .004-1.518l4.221-7.268a1.526 1.526 0 0 1 1.314-.758h4.32c.55 0 1.042.285 1.316.762a1.5 1.5 0 0 1-.004 1.518l-4.223 7.27c-.27.465-.77.755-1.31.755H4.463Z"}),(0,e.createElement)("path",{fill:"#16342F",d:"M22.413 6.935c.27-.136.476-.325.615-.569.139-.243.208-.518.208-.822 0-.237-.042-.457-.127-.66a1.361 1.361 0 0 0-.396-.528 1.967 1.967 0 0 0-.68-.35 3.358 3.358 0 0 0-.98-.128h-2.396a.104.104 0 0 0-.103.104v6.203c0 .048.033.09.079.1.258.059.548.107.87.144.356.041.733.06 1.133.06.508 0 .942-.044 1.305-.136.362-.091.66-.222.89-.39.23-.17.4-.378.507-.625.108-.247.162-.523.162-.828 0-.386-.094-.712-.284-.98a1.667 1.667 0 0 0-.803-.594v-.001Zm-2.61-1.926c0-.058.047-.105.104-.105h.952c.393-.006.678.065.858.214.18.149.27.352.27.61 0 .27-.09.481-.27.634-.18.152-.462.228-.848.228h-.96a.105.105 0 0 1-.105-.104V5.01l-.002-.001ZM21.89 9.24c-.24.162-.628.244-1.163.244-.196 0-.365-.005-.507-.016a2.392 2.392 0 0 1-.335-.047.104.104 0 0 1-.082-.102V7.668c0-.058.047-.104.104-.104h1.054c.44 0 .765.084.975.254.21.17.315.402.315.7 0 .32-.12.559-.361.722ZM50.56 4.878a3.204 3.204 0 0 0-1.117-.66 4.331 4.331 0 0 0-1.457-.233h-1.812a.103.103 0 0 0-.103.103v6.304c0 .057.047.103.103.103h1.812c.535 0 1.02-.078 1.458-.233a3.168 3.168 0 0 0 1.117-.665c.308-.288.547-.631.716-1.031.17-.4.254-.843.254-1.33 0-.487-.084-.931-.254-1.33-.17-.4-.408-.741-.716-1.026v-.002Zm-.446 3.26a1.87 1.87 0 0 1-.458.68c-.2.19-.445.338-.736.443-.29.104-.626.157-1.005.157h-.49a.103.103 0 0 1-.104-.104V5.166c0-.058.046-.104.104-.104h.49c.38 0 .715.053 1.005.157.291.105.537.254.736.447.2.194.352.425.458.696.104.272.157.569.157.894 0 .326-.053.62-.157.884v-.002ZM56.561 6.294a2.439 2.439 0 0 0-.796-.502 2.668 2.668 0 0 0-.98-.178c-.352 0-.67.06-.976.183a2.407 2.407 0 0 0-1.334 1.29c-.133.298-.2.62-.2.965 0 .366.065.698.195.995.13.298.308.555.537.772.23.217.497.384.802.502.305.119.63.178.975.178s.67-.06.971-.183c.303-.122.568-.29.797-.508a2.406 2.406 0 0 0 .742-1.758c0-.352-.065-.687-.194-.985a2.324 2.324 0 0 0-.538-.772v.001Zm-.588 2.285a1.287 1.287 0 0 1-.269.427 1.243 1.243 0 0 1-.92.386 1.233 1.233 0 0 1-.92-.386 1.303 1.303 0 0 1-.271-.427 1.453 1.453 0 0 1-.095-.528c0-.19.032-.366.095-.528.063-.163.153-.305.27-.427a1.233 1.233 0 0 1 .92-.386 1.233 1.233 0 0 1 .92.386c.117.122.207.264.27.427.063.162.095.338.095.528s-.032.366-.095.528ZM61.419 9.3a2.42 2.42 0 0 1-.717.092c-.21 0-.402-.034-.578-.102a1.41 1.41 0 0 1-.458-.28 1.247 1.247 0 0 1-.3-.426 1.34 1.34 0 0 1-.107-.533c0-.19.036-.377.107-.544.071-.165.17-.308.3-.426a1.41 1.41 0 0 1 .463-.28c.179-.068.377-.102.594-.102.264 0 .492.032.685.097.07.024.135.048.196.075a.103.103 0 0 0 .145-.094v-.899a.102.102 0 0 0-.067-.096 2.773 2.773 0 0 0-.455-.118 3.744 3.744 0 0 0-.645-.05c-.358 0-.694.063-1.005.188-.311.125-.58.298-.808.518-.227.22-.406.477-.538.772a2.329 2.329 0 0 0-.199.96 2.413 2.413 0 0 0 .712 1.732c.223.22.49.394.802.523.312.128.657.193 1.036.193.27 0 .496-.021.675-.066.15-.037.29-.077.423-.12a.102.102 0 0 0 .07-.098V9.33a.103.103 0 0 0-.142-.094 2.348 2.348 0 0 1-.188.067l-.001-.001ZM45.173 5.614h-.172c-.366 0-.675.086-.924.259-.251.173-.427.388-.529.644l-.062-.79a.103.103 0 0 0-.112-.094l-.914.083a.103.103 0 0 0-.094.109l.085 1.25v3.314c0 .058.046.104.103.104h1.012a.104.104 0 0 0 .104-.104V8.112c0-.4.108-.725.324-.976.217-.25.522-.376.914-.376.084 0 .166.004.242.013a.102.102 0 0 0 .114-.102v-.99s-.004-.069-.092-.069l.001.002ZM41.066 9.17c-.108.06-.223.111-.345.151a2.505 2.505 0 0 1-.406.097 3.183 3.183 0 0 1-.498.036c-.467 0-.832-.107-1.096-.32a1.221 1.221 0 0 1-.458-.837c.102.122.262.216.477.284.217.068.468.102.752.102.636-.006 1.128-.146 1.478-.416.348-.27.523-.654.523-1.148 0-.434-.157-.793-.472-1.076-.315-.285-.777-.427-1.386-.427-.373 0-.718.065-1.036.193a2.525 2.525 0 0 0-.828.534 2.393 2.393 0 0 0-.544.802 2.598 2.598 0 0 0-.193 1.01c0 .367.063.684.188.976.126.291.3.539.523.741a2.3 2.3 0 0 0 .803.468c.311.108.654.162 1.026.162.406 0 .751-.042 1.036-.127.257-.076.463-.163.62-.258a.1.1 0 0 0 .048-.088v-.802c0-.08-.086-.13-.155-.089a1.6 1.6 0 0 1-.057.033V9.17ZM38.73 6.96c.257-.22.558-.33.904-.33.203 0 .365.046.487.137a.459.459 0 0 1 .183.39c0 .123-.036.231-.107.326a.754.754 0 0 1-.346.233c-.159.06-.369.105-.63.131a7.16 7.16 0 0 1-.96.021c.055-.385.21-.689.468-.909l.001.001ZM65.946 8.078c-.237-.234-.64-.411-1.209-.534-.223-.047-.406-.09-.548-.127a1.58 1.58 0 0 1-.33-.117.448.448 0 0 1-.163-.128.267.267 0 0 1-.045-.157c0-.278.285-.416.853-.416.257 0 .513.03.767.091.17.041.337.1.5.175.067.03.144-.02.144-.095v-.834a.105.105 0 0 0-.063-.097 2.745 2.745 0 0 0-.586-.165 4.468 4.468 0 0 0-.762-.061c-.304 0-.58.034-.827.102a2.06 2.06 0 0 0-.64.29 1.28 1.28 0 0 0-.412.457c-.094.18-.142.377-.142.594 0 .386.123.693.366.919.244.227.63.395 1.158.502.23.042.418.082.563.123.146.04.26.084.345.131a.443.443 0 0 1 .173.152c.03.055.045.119.045.194 0 .29-.308.436-.924.436-.304 0-.612-.047-.919-.141a2.746 2.746 0 0 1-.581-.257.102.102 0 0 0-.155.09v.844c0 .039.022.076.057.094.24.12.488.208.745.265.277.06.586.091.924.091.61 0 1.099-.13 1.468-.39.369-.261.554-.618.554-1.072 0-.406-.119-.727-.356-.96ZM35.923 9.342a1.554 1.554 0 0 1-.549.081.893.893 0 0 1-.584-.198c-.159-.132-.238-.361-.238-.686V6.752h1.522a.103.103 0 0 0 .103-.103v-.83a.103.103 0 0 0-.103-.103h-1.522V4.35a.104.104 0 0 0-.116-.103l-1.002.126a.103.103 0 0 0-.09.102l-.011 4.267c0 .589.16 1.028.477 1.32.319.291.74.437 1.27.437a2.6 2.6 0 0 0 .654-.07c.179-.048.327-.106.442-.174V9.39a.102.102 0 0 0-.145-.093l-.109.046h.001ZM32.17 9.342a1.553 1.553 0 0 1-.55.081.893.893 0 0 1-.584-.198c-.159-.132-.238-.361-.238-.686V6.752h1.518a.103.103 0 0 0 .103-.103v-.83a.103.103 0 0 0-.103-.102h-1.518V4.35a.104.104 0 0 0-.116-.103l-1.002.127a.103.103 0 0 0-.09.101l-.01 4.267c0 .589.158 1.028.476 1.32.319.291.741.437 1.27.437a2.6 2.6 0 0 0 .654-.07 1.9 1.9 0 0 0 .382-.14.102.102 0 0 0 .054-.092v-.805a.103.103 0 0 0-.146-.093 2.609 2.609 0 0 1-.103.043l.002-.001ZM28.33 9.17a1.93 1.93 0 0 1-.345.151 2.504 2.504 0 0 1-.406.097c-.15.024-.315.036-.498.036-.468 0-.832-.107-1.096-.32a1.221 1.221 0 0 1-.458-.837c.102.122.261.216.477.284.217.068.468.102.751.102.637-.006 1.129-.146 1.478-.416s.523-.654.523-1.148c0-.434-.157-.793-.471-1.076-.315-.285-.777-.427-1.387-.427-.372 0-.717.065-1.036.193a2.524 2.524 0 0 0-.827.534 2.391 2.391 0 0 0-.544.802 2.598 2.598 0 0 0-.193 1.01c0 .367.063.684.188.976.125.291.3.539.523.741a2.3 2.3 0 0 0 .803.468c.311.108.653.162 1.025.162.407 0 .752-.042 1.037-.127.258-.077.465-.164.622-.26a.102.102 0 0 0 .048-.087v-.803c0-.08-.087-.13-.154-.089-.02.013-.04.024-.06.035V9.17Zm-2.336-2.21c.257-.22.558-.33.903-.33.204 0 .366.046.487.137a.459.459 0 0 1 .183.39c0 .123-.036.231-.107.326a.754.754 0 0 1-.345.233c-.16.06-.369.105-.63.131-.261.027-.58.034-.96.021.055-.385.21-.689.468-.909v.001Z"})),Qe=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("path",{fill:"#16CA9E",d:"M23.995.901c-.001-.015-.004-.029-.006-.043L23.98.805l-.013-.05-.011-.045-.017-.048-.016-.045-.02-.043-.022-.045-.024-.04-.026-.044L23.8.402l-.027-.036a.993.993 0 0 0-.065-.073h-.001a.999.999 0 0 0-.073-.066L23.598.2l-.043-.032c-.014-.01-.029-.017-.043-.026l-.04-.024-.046-.022-.043-.02-.045-.016-.048-.017-.045-.012-.05-.012-.053-.008L23.1.005A1.022 1.022 0 0 0 23 0h-7a1 1 0 1 0 0 2h4.586l-7.293 7.293a1 1 0 0 0 1.414 1.414L22 3.414V8a1 1 0 1 0 2 0V1c0-.033-.002-.066-.005-.099ZM9.293 13.293 2 20.586V16a1 1 0 1 0-2 0v7c0 .033.002.066.005.099l.006.043.008.053.013.05.011.045.017.048.016.045.02.043.022.045.024.04.026.044.032.043.027.036c.02.025.042.05.065.072v.001h.001c.024.024.048.046.073.066l.036.027c.014.01.028.022.043.031.014.01.029.018.043.027l.04.024.046.022.043.02.045.016.048.017.045.011c.017.005.033.01.05.013l.053.008.043.006C.934 23.998.967 24 1 24h7a1 1 0 1 0 0-2H3.414l7.293-7.293a1 1 0 1 0-1.414-1.414Z"})),Le=()=>{const{mainWrapperStyleRef:n}=(0,t.useContext)(me),{current:r}=n;return(0,e.createElement)("span",{className:"content-icon-expand",onClick:()=>{let{style:e}=r,{width:t,height:n}=e;""===t&&""===n?Object.assign(e,{width:"686px",height:"800px"}):Object.assign(e,{width:"",height:""})}},(0,e.createElement)(Qe,null))},De=({scrollData:t,scrollTitle:n,contentTitle:r})=>{const{current:a}=r,i=null!=a?.offsetHeight?a?.offsetHeight:0;return(0,e.createElement)("div",{className:"betterdocs-ia-singleDoc-header"+(t>i?" on-scroll":"")},(0,e.createElement)(Ee,{layout:"single-doc"}),t>i&&(0,e.createElement)("h2",null,n),(0,e.createElement)(Le,null))},He=({clickHandler:t})=>(0,e.createElement)("div",{className:"ia-reaction ia-neutral",onClick:t},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",xmlSpace:"preserve",className:"betterdocs-emo betterdocs-neutral-icon",viewBox:"0 0 20 20","data-reaction":"normal"},(0,e.createElement)("path",{d:"M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0zM6.6 6.4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5c-.8.1-1.5-.6-1.5-1.4 0-.9.7-1.6 1.5-1.6zm7.7 8H5.7c-.3 0-.6-.3-.6-.6s.3-.6.6-.6h8.5c.3 0 .6.3.6.6.1.3-.2.6-.5.6zm-1-4.9c-.8 0-1.5-.7-1.5-1.5s.7-1.5 1.5-1.5 1.5.7 1.5 1.5c.1.8-.6 1.5-1.5 1.5z"}))),Ie=({clickHandler:t})=>(0,e.createElement)("div",{className:"ia-reaction ia-sad",onClick:t},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",xmlSpace:"preserve",className:"betterdocs-emo betterdocs-sad-icon",viewBox:"0 0 20 20","data-reaction":"sad"},(0,e.createElement)("path",{d:"M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0zm3.3 6.4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5zm-6.6 0c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5c-.9.1-1.6-.6-1.6-1.4 0-.9.7-1.6 1.6-1.6zm7.5 8.6c-.2 0-.4-.1-.5-.2-.9-1.1-2.2-1.8-3.7-1.8s-2.8.7-3.7 1.7c-.2.2-.3.3-.5.3-.6 0-.9-.7-.5-1.1 1.2-1.3 2.9-2.2 4.7-2.2 1.8 0 3.6.8 4.7 2.2.4.4.1 1.1-.5 1.1z",className:"st-path"}))),je=({clickHandler:t})=>(0,e.createElement)("div",{className:"ia-reaction ia-happy",onClick:t},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",xmlSpace:"preserve",className:"betterdocs-emo betterdocs-happy-icon",viewBox:"0 0 20 20","data-reaction":"happy"},(0,e.createElement)("path",{d:"M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0zm3.3 6.4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5zm-6.6 0c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5c-.9.1-1.6-.6-1.6-1.4 0-.9.7-1.6 1.6-1.6zm3.3 9.9c-2.6 0-5-1.6-5.9-4.1l1.2-.5c.7 1.9 2.6 3.2 4.6 3.2s3.9-1.3 4.6-3.2l1.2.5c-.7 2.4-3.1 4.1-5.7 4.1z"}))),ze=({sadOnclick:t,neutralOnclick:n,happyOnClick:r,reactionText:a})=>{let i=U==a?"":" success";return(0,e.createElement)("div",{className:`betterdocs-ia-footer-feedback${i}`},(0,e.createElement)("p",null,a),U==a&&(0,e.createElement)("div",{className:"betterdocs-ia-reaction-group"},(0,e.createElement)(je,{clickHandler:r}),(0,e.createElement)(Ie,{clickHandler:t}),(0,e.createElement)(He,{clickHandler:n})))},Ze=()=>{const{singleDocInfo:n,feedbackText:r,setFeedbackText:a}=(0,t.useContext)(me),{doc_id:i,post_type:s}=n,[o,c]=(0,t.useState)(0),[l,u]=(0,t.useState)(""),h=(0,t.useRef)(null),{data:d,isLoading:f}=(0,Ae.useQuery)({queryKey:["docContent",{post_type:s,post_id:i}],queryFn:Ce,staleTime:1/0});function m(e,t){fetch(`${z}/${e}?feelings=${t}`,{method:"POST"}).then((e=>e.json())).then((e=>{a(e?Z:"Oops! Something went wrong with your reaction."),setTimeout((()=>{a(U)}),3e3)}))}return(0,e.createElement)(e.Fragment,null,f?(0,e.createElement)(qe,null):(0,e.createElement)("div",{className:"betterdocs-ia-single-docs-wrapper",onScroll:e=>{c(e?.target?.scrollTop),u(d?.title?.rendered)}},(0,e.createElement)(De,{scrollData:o,scrollTitle:l,contentTitle:h}),(0,e.createElement)("div",{className:"betterdocs-ia-singleDoc-content"},(0,e.createElement)("h4",{className:"doc-title",dangerouslySetInnerHTML:{__html:d?.title?.rendered},ref:h}),(0,e.createElement)("div",{dangerouslySetInnerHTML:{__html:d?.content?.rendered}})),(0,e.createElement)("div",{className:"betterdocs-ia-singleDoc-footer"},j&&"betterdocs_faq"!=s&&(0,e.createElement)(ze,{sadOnclick:e=>{m(i,"sad")},neutralOnclick:e=>{m(i,"normal")},happyOnClick:e=>{m(i,"happy")},reactionText:r}),te&&(0,e.createElement)("div",{className:"betterdocs-ia-footer-group"},(0,e.createElement)("h5",null,(0,ye.__)("Powered by","betterdocs-pro")),(0,e.createElement)("span",{className:"logoFooter"},(0,e.createElement)("a",{href:"https://betterdocs.co/",target:"_blank"},(0,e.createElement)(xe,null)))))))},Ue=()=>(0,e.createElement)(Ze,null),Be=n.p+"images/nodoc.c8afc2a7.png",Ve=({content:n,subContent:r})=>{const{setTab:a}=(0,t.useContext)(me);return(0,e.createElement)("div",{className:"betterdocs-ia-no-doc"},(0,e.createElement)("img",{src:Be,alt:"nodoc"}),n&&(0,e.createElement)("h4",null,n),r&&(0,e.createElement)("p",null,r),(0,e.createElement)("button",{onClick:()=>{a("feedback_form")}},(0,ye.__)("Contact Us","betterdocs-pro")))},Ke=({DocList:n})=>{const[r,a]=(0,t.useState)(""),[i,s,o]=ke(r,R,w);let c,l,u=[{querykey:"docDefault",queryParams:{taxonomy:"doc_category",terms_per_page:5,terms_order:N,terms_orderby:k,parent:1==P?1:0},queryFn:Ce,queryRenderCondition:R&&0==F.length},{querykey:"docPerPage",queryParams:{taxonomy:"doc_category",terms_per_page:100,terms_order:N,terms_orderby:k,parent:1==P?1:0},queryFn:Ce,queryRenderCondition:R&&F.includes("all")},{querykey:"docTerms",queryParams:{taxonomy:"doc_category",terms:F,terms_order:N,terms_orderby:k},queryFn:Ce,queryRenderCondition:R&&!F.includes("all")&&F.length>0}],h=[{querykey:"faqDefault",queryParams:{post_type:"betterdocs_faq",posts_per_page:5,posts_order:M,posts_orderby:A},queryFn:Ce,queryRenderCondition:w&&"faq-list"===_&&0==O.length},{querykey:"faqAll",queryParams:{post_type:"betterdocs_faq",posts_per_page:100,posts_order:M,posts_orderby:A},queryFn:Ce,queryRenderCondition:w&&"faq-list"===_&&O.includes("all")},{querykey:"faqSpecificList",queryParams:{post_type:"betterdocs_faq",post_ids:O,posts_order:M,posts_orderby:A},queryFn:Ce,queryRenderCondition:w&&"faq-list"===_&&!O.includes("all")},{queryKey:"faqListPerPage",queryParams:{taxonomy:"betterdocs_faq_category",terms_per_page:5,terms_order:T,terms_orderby:q},queryFn:Ce,queryRenderCondition:w&&"faq-group"===_&&0==S.length},{queryKey:"faqListPerPage",queryParams:{taxonomy:"betterdocs_faq_category",terms_per_page:100,terms_order:T,terms_orderby:q},queryFn:Ce,queryRenderCondition:w&&"faq-group"===_&&S.includes("all")},{queryKey:"faqSpecficTerms",queryParams:{taxonomy:"betterdocs_faq_category",terms:S,terms_order:T,terms_orderby:q},queryFn:Ce,queryRenderCondition:w&&"faq-group"===_&&!S.includes("all")}];for(const e of u)if(e?.queryRenderCondition){l=(0,Ae.useQuery)({queryKey:[e?.querykey,e?.queryParams],queryFn:e?.queryFn,staleTime:1/0,enabled:R});break}for(const e of h)if(e?.queryRenderCondition){c=(0,Ae.useQuery)({queryKey:[e?.querykey,e?.queryParams],queryFn:e?.queryFn,staleTime:1/0,enabled:w});break}return(0,e.createElement)("div",{className:"betterdocs-ia-tab-faq-content"},(0,e.createElement)(Te,{headingLayout:"resources-category",headingContent:K,enableSearch:!0,extraClass:"resources-page-header",searchCallback:a,searchKeyword:r,enableBackButton:!0}),i&&s&&(0,e.createElement)(qe,null),i&&!s&&(0,e.createElement)(n,{docList:o,contentType:"post_type",borderRadius:!0,noContent:m?.OOPS,noSubContent:m?.NOT_FOUND}),!R&&w&&c?.isLoading&&!i&&(0,e.createElement)(qe,null),!w&&R&&l?.isLoading&&!i&&(0,e.createElement)(qe,null),R&&w&&l?.isLoading&&c?.isLoading&&!i&&(0,e.createElement)(qe,null),R&&!i&&l?.isSuccess&&l?.data?.length>0&&(0,e.createElement)(n,{docList:l?.data,headingContent:b,contentType:"taxonomy",borderRadius:!0}),w&&c?.isSuccess&&!i&&c?.data?.length>0&&(0,e.createElement)(n,{docList:c?.data,headingContent:E,contentType:"faq-list"==_?"post_type":"taxonomy",borderRadius:!0}),R&&w&&0==c?.data?.length&&0==l?.data?.length&&!i&&(0,e.createElement)(Ve,{content:(0,ye.__)("No Faq & Doc Categories Available","betterdocs-pro"),subContent:(0,ye.__)("There are no categories available","betterdocs-pro")}),R&&!w&&0==l?.data?.length&&!i&&(0,e.createElement)(Ve,{content:(0,ye.__)("No Doc Categories Available","betterdocs-pro"),subContent:(0,ye.__)("There are no categories available","betterdocs-pro")}),!R&&w&&0==c?.data?.length&&!i&&(0,e.createElement)(Ve,{content:(0,ye.__)("No FAQ Available","betterdocs-pro"),subContent:(0,ye.__)("There are no FAQs available","betterdocs-pro")}),!R&&!w&&!i&&(0,e.createElement)(Ve,{content:(0,ye.__)("No Content Available","betterdocs-pro"),subContent:(0,ye.__)("Please enable Doc Categories or FAQ in settings","betterdocs-pro")}))},$e=({DocList:n})=>{const[r,a]=(0,t.useState)(""),[i,s,o]=ke(r,c,!1),[u,h,d]=ke(r,!1,l),{data:p,isLoading:v,isSuccess:y}=(0,Ae.useQuery)({queryKey:["homeContent"],queryFn:()=>fetch(x,{method:"GET"}).then((e=>e.json())),staleTime:1/0,enabled:c}),{data:g,isLoading:C,isSuccess:b}=(0,Ae.useQuery)({queryKey:["homeFaqContent"],queryFn:()=>fetch(f?.FAQ_URL,{method:"GET"}).then((e=>e.json())),staleTime:1/0,enabled:l});return(0,e.createElement)("div",{className:"betterdocs-ia-tab-home-content"},(0,e.createElement)(Te,{headingContent:B,headingSubContent:V,headingLayout:"home-layout",enableLogo:!1,enableSearch:!0,extraClass:"home-page-header",searchCallback:a,searchKeyword:r}),(0,e.createElement)("div",{className:"betterdocs-ia-tab-main-content"},(0,e.createElement)("div",{className:"betterdocs-ia-home-content-list"},(c&&v||l&&C)&&!i&&(0,e.createElement)(qe,null),c&&y&&!i&&p?.length>0&&(0,e.createElement)(n,{docList:p,headingContent:H,borderRadius:!0,contentType:I,noContent:"post_type"==I?(0,ye.__)("No Docs Available","betterdocs-pro"):(0,ye.__)("No Doc Categories Found","betterdocs-pro")}),l&&b&&!i&&g?.length>0&&(0,e.createElement)(n,{docList:g,headingContent:(0,ye.__)("FAQ","betterdocs-pro"),borderRadius:!0,contentType:"faq-list"==f.HOME_FAQ_CONTENT_TYPE?"post_type":"taxonomy",noContent:(0,ye.__)("No FAQ Available","betterdocs-pro")}),!i&&(c&&l&&y&&b&&0==p?.length&&0==g?.length||c&&!l&&y&&0==p?.length||!c&&l&&b&&0==g?.length)&&(0,e.createElement)(Ve,{content:c&&l?"post_type"==I?(0,ye.__)("No Docs & FAQ Available","betterdocs-pro"):(0,ye.__)("No Doc Categories & FAQ Found","betterdocs-pro"):c?"post_type"==I?(0,ye.__)("No Docs Available","betterdocs-pro"):(0,ye.__)("No Doc Categories Found","betterdocs-pro"):(0,ye.__)("No FAQ Available","betterdocs-pro")}),(i&&s||u&&h)&&(0,e.createElement)(qe,null),c&&l&&i&&u&&!s&&!h&&o?.length>0&&d?.length>0&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n,{docList:o,headingContent:(0,ye.__)(H,"betterdocs-pro"),borderRadius:!0,contentType:"post_type",noContent:m?.OOPS,noSubContent:m?.NOT_FOUND}),(0,e.createElement)(n,{docList:d,headingContent:(0,ye.__)("FAQ","betterdocs-pro"),borderRadius:!0,contentType:"post_type",noContent:m?.OOPS,noSubContent:m?.NOT_FOUND})),c&&i&&!s&&(l&&u&&!h&&o?.length>0&&0==d?.length||!l&&o?.length>0)&&(0,e.createElement)(n,{docList:o,headingContent:(0,ye.__)(D,"betterdocs-pro"),borderRadius:!0,contentType:"post_type",noContent:m?.OOPS,noSubContent:m?.NOT_FOUND}),l&&u&&!h&&(c&&i&&!s&&0==o?.length&&d?.length>0||!c&&d?.length>0)&&(0,e.createElement)(n,{docList:d,headingContent:(0,ye.__)("FAQ","betterdocs-pro"),borderRadius:!0,contentType:"post_type",noContent:m?.OOPS,noSubContent:m?.NOT_FOUND}),(c&&i&&!s&&0==o?.length||!c)&&(l&&u&&!h&&0==d?.length||!l)&&(i||u)&&(0,e.createElement)(Ve,{content:m?.OOPS,subContent:m?.NOT_FOUND}))))},Ge=window.wp.htmlEntities,We=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:8,height:16,fill:"none"},(0,e.createElement)("path",{fill:"#00B682",d:"M.94.97a.81.81 0 0 0-.514.173.991.991 0 0 0-.332.479 1.126 1.126 0 0 0-.025.608c.048.2.15.38.291.512l5.401 5.242-5.401 5.24a.97.97 0 0 0-.24.304 1.127 1.127 0 0 0-.08.795c.034.132.094.254.173.358.079.105.176.19.286.25a.79.79 0 0 0 .706.03.882.882 0 0 0 .301-.223L7.69 8.744a.997.997 0 0 0 .229-.342 1.113 1.113 0 0 0 0-.838.998.998 0 0 0-.229-.342l-6.184-6A.828.828 0 0 0 .941.97Z"})),Je=(0,t.memo)((({docList:n,headingContent:r,borderRadius:a,contentType:i,noContent:s,noSubContent:o})=>{const{setTab:c,setSingleDocInfo:l,setTermInfo:u}=(0,t.useContext)(me);return(0,e.createElement)(e.Fragment,null,n?.length>0?(0,e.createElement)("div",{className:"betterdocs-ia-docs"+(a?" radius-layout":"")},r&&(0,e.createElement)("div",{className:"betterdocs-ia-docs-heading"},(0,e.createElement)("h4",{className:"doc-title"},(0,ye.__)((0,Ge.decodeEntities)(r),"betterdocs-pro"))),n?.map((t=>(0,e.createElement)("div",{className:"betterdocs-ia-docs-content",onClick:()=>{return e=t,"post_type"==(n=i)&&(c("single_doc_view"),l({doc_id:e?.id,post_type:e.hasOwnProperty("doc_category")?"docs":"betterdocs_faq"})),void("taxonomy"==n&&(c("doc_list"),u({term_name:e?.name,term_id:e?.id,taxonomy:e?.taxonomy})));var e,n},key:Math?.random()},(0,e.createElement)("div",{className:"content-item"},(0,e.createElement)("h4",{dangerouslySetInnerHTML:{__html:"taxonomy"==i?(0,Ge.decodeEntities)(t?.name):t?.title?.rendered}}),"taxonomy"!=i&&(0,e.createElement)("p",null,(0,Ge.decodeEntities)(t?.excerpt?.rendered?.replace(/<[^>]+>/g,"")?.replace("[&hellip;]",""))?.split(" ")?.slice(0,10)?.join(" "))),(0,e.createElement)("div",{className:"content-icon"},(0,e.createElement)(We,null)))))):(0,e.createElement)(Ve,{content:s,subContent:o}))})),Ye=n.p+"images/messageLoader.d2dc7c6a.gif",Xe=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none"},(0,e.createElement)("g",{clipPath:"url(#a)"},(0,e.createElement)("path",{stroke:"#475467",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.667,d:"M13.334 13.333 10 10m0 0-3.333 3.333M10 10v7.5m6.992-2.175A4.167 4.167 0 0 0 15 7.5h-1.05A6.667 6.667 0 1 0 2.5 13.583"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h20v20H0z"})))),et=({className:t,onClick:n})=>(0,e.createElement)("svg",{className:t,xmlns:"http://www.w3.org/2000/svg",width:20,height:25,fill:"none",onClick:n},(0,e.createElement)("path",{stroke:"#667085",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.667,d:"M2.5 5h1.667m0 0H17.5M4.167 5v11.667a1.667 1.667 0 0 0 1.666 1.666h8.334a1.667 1.667 0 0 0 1.666-1.666V5H4.167Zm2.5 0V3.333a1.667 1.667 0 0 1 1.666-1.666h3.334a1.667 1.667 0 0 1 1.666 1.666V5m-5 4.167v5m3.334-5v5"})),tt=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:25,fill:"none"},(0,e.createElement)("path",{stroke:"#01BAB4",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.333,d:"M8.666 1.333H4a1.333 1.333 0 0 0-1.333 1.334v10.666A1.333 1.333 0 0 0 4 14.667h8a1.333 1.333 0 0 0 1.333-1.334V6M8.666 1.333 13.333 6M8.666 1.333V6h4.667"})),nt=(0,t.forwardRef)((({fileName:t,fileSize:n,fileID:r,fileDatas:a,formState:i},s)=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"betterdocs-ia-progress-wrapper",ref:s},(0,e.createElement)("div",{className:"progress-content-wrapper"},(0,e.createElement)("div",{className:"progress-icon-details"},(0,e.createElement)(tt,null),(0,e.createElement)("div",{className:"progress-details"},(0,e.createElement)("h5",{className:"brand-info-title"},t),(0,e.createElement)("span",{className:"brand-info-sub"},n+"mb"))),(0,e.createElement)(et,{className:"progress-remove-icon",onClick:()=>{let e=a.filter(((e,t)=>t!=r));i((t=>({...t,fileData:[...e]})))}})))))),rt=n.p+"images/msg-receive.fe68435f.png",at=()=>(0,e.createElement)("div",{className:"betterdocs-ia-msg-receive"},(0,e.createElement)("img",{src:rt,alt:"receiveImg"}),(0,e.createElement)("h4",null,(0,ye.__)(g?.title)),(0,e.createElement)("p",null,(0,ye.__)(g?.text))),it=({enableBackButton:t,heading:n,subHeading:r})=>{let a=t?(0,e.createElement)(Ee,null):"";return(0,e.createElement)("div",{className:"message__header betterdocs-ia-common-header"},(0,e.createElement)("div",{className:"betterdocs-ia-header-group"},a,(0,e.createElement)("div",{className:"header__content"},n?(0,e.createElement)("h4",null,n):null,r?(0,e.createElement)("p",null,r):null)))},st=()=>{const{feedbackFormInfo:n,setFeedbackFormInfo:r,validation:a,setValidation:i,messageStatus:s,setMessageStatus:o,buttonStatus:c,setButtonStatus:l}=(0,t.useContext)(me),h=(0,t.useRef)(),d=(0,t.useRef)(),{current:f}=h,{name:m,subject:p,message:g,email:C,fileData:b}=n,{message:E,fieldType:w}=a;function _(e){let t,n,r=["png","gif","pdf","jpeg","jpg"];for(let a of e)if(n=a?.name?.split(".")[0],t=a?.name?.split(".")[a?.name?.split(".")?.length-1].toLowerCase(),!r.includes(t))return i((e=>({...e,message:`Only ${r.join(", ")} are allowed`,fieldType:"file"}))),!1;return!0}function S(e){let t;for(let n of e)if(t=Math.ceil(n?.size/1048576),t>5)return i((e=>({...e,message:"Max file size limit 5 mb",fieldType:"file"}))),!1;return!0}function O(e){const{payload:t,type:n}=e,{target:a}=t,{value:i}=a;switch(n){case"setEmail":r((e=>({...e,email:i})));break;case"setName":r((e=>({...e,name:i})));break;case"setSubject":r((e=>({...e,subject:i})));break;case"setMessage":r((e=>({...e,message:i})));break;case"setFileData":r((e=>({...e,fileData:t})))}}return(0,t.useEffect)((()=>{d?.current?.scrollIntoView({behavior:"smooth",block:"end",inline:"nearest"})}),[b.length]),(0,e.createElement)("div",{className:"betterdocs-ia-tab-message-container"},(0,e.createElement)(it,{enableBackButton:!0,heading:(0,ye.__)(u?.subtitle),subHeading:(0,ye.__)(u?.subtitle_two)}),"idle"==s?(0,e.createElement)("div",{className:"betterdocs-ia-feedback-form"},(0,e.createElement)("div",{className:`betterdocs-ia-group betterdocs-ia-email-group ${"email"==w&&"betterdocs-ia-warning-group"}`},(0,e.createElement)("p",null,v?.EMAIL),(0,e.createElement)("input",{className:"ia-input",name:"ia-email",onChange:e=>O({payload:e,type:"setEmail"})}),"email"==w&&(0,e.createElement)("span",{className:"warning-text"},(0,e.createElement)("p",null,E))),(0,e.createElement)("div",{className:`betterdocs-ia-group betterdocs-ia-name-group ${"name"==w&&"betterdocs-ia-warning-group"}`},(0,e.createElement)("p",null,v?.NAME),(0,e.createElement)("input",{className:"ia-input",name:"ia-name",onChange:e=>O({payload:e,type:"setName"})}),"name"==w&&(0,e.createElement)("span",{className:"warning-text"},(0,e.createElement)("p",null,E))),(0,e.createElement)("div",{className:`betterdocs-ia-group betterdocs-ia-subject-group ${"subject"==w&&"betterdocs-ia-warning-group"}`},(0,e.createElement)("p",null,v?.SUBJECT),(0,e.createElement)("input",{className:"ia-input",name:"ia-subject",onChange:e=>O({payload:e,type:"setSubject"})}),"subject"==w&&(0,e.createElement)("span",{className:"warning-text"},(0,e.createElement)("p",null,E))),(0,e.createElement)("div",{className:`betterdocs-ia-group betterdocs-ia-message-group ${"message"==w&&"betterdocs-ia-warning-group"}`},(0,e.createElement)("p",null,v?.TEXTAREA),(0,e.createElement)("textarea",{className:"ia-message",name:"ia-message",onChange:e=>O({payload:e,type:"setMessage"})}),"message"==w&&(0,e.createElement)("span",{className:"warning-text"},(0,e.createElement)("p",null,E))),y&&(0,e.createElement)("div",{className:"betterdocs-ia-attachments-group",onDragOver:function(e){e.preventDefault()},onDragLeave:function(e){e.preventDefault()},onDrop:function(e){e.preventDefault();const{files:t}=e.dataTransfer;_(t)&&S(t)&&(i((e=>({...e,message:"",fieldType:""}))),r((e=>{const{fileData:n}=e;return n.push(...t),{...e,fileData:[...n]}})))}},(0,e.createElement)("button",{onClick:e=>{f?.click()}},(0,e.createElement)(Xe,null)),(0,e.createElement)("p",null,(0,ye.__)("Click To Upload Or Drag and Drop","betterdocs-pro")),(0,e.createElement)("p",null,v?.ATTACHMENT),(0,e.createElement)("input",{type:"file",name:"ia-upload-file",multiple:!0,accept:"image/png, image/gif, image/jpeg, image/jpg, .pdf",style:{display:"none"},ref:h,onChange:function(e){const{files:t}=e.target;_(t)&&S(t)&&(""!=E&&i((e=>({...e,message:"",fieldType:""}))),r((e=>{const{fileData:n}=e;return n.push(...t),{...e,fileData:[...n]}})))}})),"file"==w&&(0,e.createElement)("div",{className:"betterdocs-ia-group"+("message"===w?" betterdocs-ia-warning-group":"")},(0,e.createElement)("span",{className:"warning-text file-warning"},(0,e.createElement)("p",null,E))),(0,e.createElement)("div",{className:"betterdocs-ia-submit"},(0,e.createElement)("button",{type:"submit",onClick:()=>function(){let e=/^([a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;switch(!0){case 0===C.length:return void i((e=>({...e,message:`${v?.EMAIL} Field Must Not Be Empty`,fieldType:"email"})));case!e.test(C):return void i((e=>({...e,message:"Email Address Invalid",fieldType:"email"})));case 0===m.length:return void i((e=>({...e,message:`${v?.NAME} Field Must Not Be Empty`,fieldType:"name"})));case 0===p.length:return void i((e=>({...e,message:`${v?.SUBJECT} Field Must Not Be Empty`,fieldType:"subject"})));case 0===g.length:return void i((e=>({...e,message:"Text Area Field Must Not Be Empty",fieldType:"message"})))}i((e=>({...e,message:"",fieldType:""}))),l("submitting"),function(){let e=new FormData;e.append("name",m),e.append("email",C),e.append("subject",p),e.append("message",g);for(let t=0;t<b.length;t++)e.append("file[]",b[t]);fetch(betterdocs?.ASK_URL,{method:"POST",body:e}).then((e=>e.json())).then((e=>{e&&(o("sent"),setTimeout((()=>{o("idle"),l("idle"),r({email:"",name:"",subject:"",message:"",fileData:[]})}),5e3))})).catch((e=>{console.error(e)})),i((e=>({...e,message:"",fieldType:""})))}()}()},"submitting"==c?(0,e.createElement)("img",{src:Ye,className:"submit-loader",height:20,width:25}):"",(0,e.createElement)("span",null,"idle"==c?v?.SEND:v?.SENDING)))):(0,e.createElement)(at,null),b.length>0&&"idle"==s?b.map(((t,n)=>{let a=t?.name,i=Math.ceil(t?.size/1048576);return(0,e.createElement)(nt,{fileName:a,fileSize:i,key:n,fileID:n,fileDatas:b,formState:r,ref:d})})):"")},ot=()=>{const[n,r]=(0,t.useState)(void 0),[a,s]=(0,t.useState)({taxonomy:"doc_category"}),o=(0,t.useRef)(null),[d,f]=(0,t.useState)([]),[m,p]=(0,t.useState)({}),[v,y]=(0,t.useState)({email:"",name:"",subject:"",message:"",fileData:[]}),[g,C]=(0,t.useState)({message:"",fieldType:""}),[b,E]=(0,t.useState)("home"),[_,S]=(0,t.useState)("idle"),[O,T]=(0,t.useState)("idle"),[q,A]=(0,t.useState)(U),M=[{id:"home",class:"betterdocs-ia-home",type:"tab",title:null!=ne&&""!=ne?ne:(0,ye.__)("Home","betterdocs"),default:!0,icon:(0,e.createElement)(fe,{active:"home"===b,icon:"home"}),component:$e,require_components:{DocList:Je},showTab:c||l,showTabInComponent:!0},...h?(0,i.applyFilters)("tab_chatbot_preview",[],b):[],{id:"feedback_tab",class:"betterdocs-ia-message",type:"tab",title:""!=u?.label&&null!=u?.label?u?.label:(0,ye.__)("Message","betterdocs"),default:!1,icon:(0,e.createElement)(fe,{active:"feedback_tab"===b,icon:"feedback_tab"}),component:st,showTab:u?.show,showTabInComponent:!1},{id:"resources",class:"betterdocs-ia-faq",type:"tab",title:""!=$&&null!=$?$:(0,ye.__)("Resources","betterdocs"),default:!1,icon:(0,e.createElement)(fe,{active:"resources"===b,icon:"resources"}),component:Ke,require_components:{DocList:Je},showTab:R||w,showTabInComponent:!0},{id:"feedback_form",class:"",type:"page",title:"",default:!1,icon:"",component:st,showTab:!1},{id:"single_doc_view",class:"",type:"page",title:"",default:!1,icon:"",component:Ue,showTab:!1},{id:"doc_list",class:"",type:"page",title:"",default:!1,icon:"",require_components:{DocList:Je},component:Pe,showTab:!0,showTabInComponent:!0}],F=()=>{const e=M?.find((e=>e?.default&&e?.showTab));if(e)r(e.id);else{const e=M?.find((e=>e?.showTab));e&&r(e.id)}};(0,t.useEffect)((()=>{F()}),[]),(0,t.useEffect)((()=>{F()}),[c,l,R,w]),(0,t.useEffect)((()=>{d.includes(n)||f((e=>{if("home"===n)return[{id:"home"}];if("resources"===n)return[{id:"resources"}];if("chatbot"===n)return[{id:"chatbot"}];if("resources"!=n&&!d.includes(n)||"home"!=n&&!d.includes(n)){let t=[...e];return t[t.length-1]?.id!=n&&t.push({id:n}),t}}))}),[n]);const N=M?.find((e=>e?.id==n));return(0,e.createElement)("div",{className:"betterdocs-ia-main-wrapper",ref:o},(0,e.createElement)(me.Provider,{value:{Tabs:M,selectedTab:n,setTab:r,termInfo:a,setTermInfo:s,singleDocInfo:m,setSingleDocInfo:p,mainWrapperStyleRef:o,setNavigationHistory:f,navigationHistory:d,feedbackFormInfo:v,setFeedbackFormInfo:y,validation:g,setValidation:C,activeTabClass:b,setactiveTabClass:E,messageStatus:_,setMessageStatus:S,buttonStatus:O,setButtonStatus:T,feedbackText:q,setFeedbackText:A}},(0,e.createElement)(e.Fragment,null,(0,e.createElement)(ve,null),N?.showTab&&N?.showTabInComponent&&M.filter((e=>e?.showTab&&"tab"===e?.type)).length>1?(0,e.createElement)(pe,null):"")))};var ct=n(576);const lt=document.getElementById("betterdocs-ia");(0,ct.H)(lt).render((0,e.createElement)((()=>{const[n,r]=(0,t.useState)(!1),a=new Ae.QueryClient;return(0,t.useEffect)((()=>{let e=e=>{"Escape"==e?.key&&n&&r(!1)};return document.addEventListener("keydown",e,!1),()=>{document.removeEventListener("keydown",e,!1)}}),[n]),(0,e.createElement)(Ae.QueryClientProvider,{client:a},(0,e.createElement)("div",{className:"betterdocs-ia-root"},n&&(0,e.createElement)(ot,null),(0,e.createElement)(ie,{toggleState:n,setToggleState:r})))}),null))})()})();