<?php
/**
 * Test script to check if Feedlane plugin is working
 */

// Include WordPress
require_once '../../../wp-config.php';

echo "=== Feedlane Plugin Test ===\n\n";

// Check if plugin is active
$plugin_file = 'feedlane/feedlane.php';
$is_active = is_plugin_active($plugin_file);

echo "Plugin Status: " . ($is_active ? "ACTIVE" : "INACTIVE") . "\n";

if (!$is_active) {
    echo "Activating plugin...\n";
    $result = activate_plugin($plugin_file);
    
    if (is_wp_error($result)) {
        echo "Error: " . $result->get_error_message() . "\n";
        exit;
    } else {
        echo "Plugin activated successfully!\n";
    }
}

// Check if classes exist
echo "\n=== Class Check ===\n";
$classes = [
    'WPDeveloper\Feedlane\Plugin',
    'WPDeveloper\Feedlane\Core\Admin',
    'WPDeveloper\Feedlane\Core\PostTypes',
    'WPDeveloper\Feedlane\Widgets\ElementorWidget',
    'WPDeveloper\Feedlane\Widgets\GutenbergBlock',
];

foreach ($classes as $class) {
    echo $class . ": " . (class_exists($class) ? "EXISTS" : "NOT FOUND") . "\n";
}

// Check if post types are registered
echo "\n=== Post Types Check ===\n";
$post_types = ['feedlane_posts', 'feedlane_ideas'];
foreach ($post_types as $post_type) {
    echo $post_type . ": " . (post_type_exists($post_type) ? "REGISTERED" : "NOT FOUND") . "\n";
}

// Check if taxonomies are registered
echo "\n=== Taxonomies Check ===\n";
$taxonomies = ['idea_category', 'roadmap_status'];
foreach ($taxonomies as $taxonomy) {
    echo $taxonomy . ": " . (taxonomy_exists($taxonomy) ? "REGISTERED" : "NOT FOUND") . "\n";
}

// Check if assets exist
echo "\n=== Assets Check ===\n";
$assets = [
    'js/feedlane-sidebar.min.js',
    'css/feedlane-sidebar.min.css',
    'js/feedlane-admin.min.js',
    'css/feedlane-admin.min.css',
];

$assets_dir = plugin_dir_path(__FILE__) . 'assets/';
foreach ($assets as $asset) {
    $file_path = $assets_dir . $asset;
    echo $asset . ": " . (file_exists($file_path) ? "EXISTS (" . round(filesize($file_path)/1024, 1) . "KB)" : "NOT FOUND") . "\n";
}

// Check admin menu
echo "\n=== Admin Menu Check ===\n";
global $menu, $submenu;

$feedlane_menu_found = false;
if (isset($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && $menu_item[2] === 'feedlane') {
            $feedlane_menu_found = true;
            echo "Main menu: FOUND\n";
            break;
        }
    }
}

if (!$feedlane_menu_found) {
    echo "Main menu: NOT FOUND\n";
}

if (isset($submenu['feedlane'])) {
    echo "Submenus: " . count($submenu['feedlane']) . " items\n";
    foreach ($submenu['feedlane'] as $submenu_item) {
        echo "  - " . $submenu_item[0] . " (" . $submenu_item[2] . ")\n";
    }
} else {
    echo "Submenus: NOT FOUND\n";
}

echo "\n=== URLs ===\n";
echo "Dashboard: " . admin_url('admin.php?page=feedlane') . "\n";
echo "Settings: " . admin_url('admin.php?page=feedlane-settings') . "\n";
echo "Ideas: " . admin_url('admin.php?page=feedlane-ideas') . "\n";
echo "Analytics: " . admin_url('admin.php?page=feedlane-analytics') . "\n";

echo "\n=== Test Complete ===\n";
?>
