<?php
/**
 * Plugin activation script for testing
 * 
 * This file can be used to quickly activate the plugin for testing purposes
 */

// Include WordPress
require_once '../../../wp-config.php';

// Activate the plugin
$plugin_file = 'feedlane/feedlane.php';

if (!is_plugin_active($plugin_file)) {
    $result = activate_plugin($plugin_file);
    
    if (is_wp_error($result)) {
        echo "Error activating plugin: " . $result->get_error_message() . "\n";
    } else {
        echo "Plugin activated successfully!\n";
        echo "You can now:\n";
        echo "1. Visit the admin panel at: " . admin_url('admin.php?page=feedlane') . "\n";
        echo "2. Configure settings at: " . admin_url('admin.php?page=feedlane-settings') . "\n";
        echo "3. Add newsfeed posts at: " . admin_url('post-new.php?post_type=feedlane_posts') . "\n";
        echo "4. View ideas at: " . admin_url('admin.php?page=feedlane-ideas') . "\n";
    }
} else {
    echo "Plugin is already active!\n";
}
?>
