<?php

namespace WPDeveloper\Feedlane\Widgets;

/**
 * Gutenberg Block Class
 */
class GutenbergBlock {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'init', [ $this, 'register_block' ] );
        add_action( 'enqueue_block_editor_assets', [ $this, 'enqueue_block_editor_assets' ] );
    }
    
    /**
     * Register Gutenberg block
     */
    public function register_block() {
        register_block_type( 'feedlane/sidebar', [
            'attributes' => [
                'position' => [
                    'type' => 'string',
                    'default' => 'left',
                ],
                'height' => [
                    'type' => 'string',
                    'default' => '600px',
                ],
                'primaryColor' => [
                    'type' => 'string',
                    'default' => '#0ea5e9',
                ],
            ],
            'render_callback' => [ $this, 'render_block' ],
        ] );
    }
    
    /**
     * Enqueue block editor assets
     */
    public function enqueue_block_editor_assets() {
        // Register block script
        wp_enqueue_script(
            'feedlane-block-editor',
            FEEDLANE_ASSETS_DIR_URL . 'js/feedlane-block-editor.js',
            [ 'wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n' ],
            FEEDLANE_VERSION,
            true
        );
        
        // Register block styles
        wp_enqueue_style(
            'feedlane-block-editor',
            FEEDLANE_ASSETS_DIR_URL . 'css/feedlane-block-editor.css',
            [],
            FEEDLANE_VERSION
        );
        
        // Localize script
        wp_localize_script( 'feedlane-block-editor', 'feedlaneBlock', [
            'title' => __( 'Feedlane Sidebar', 'feedlane' ),
            'description' => __( 'Add a feedback sidebar with newsfeed, ideas, and roadmap tabs.', 'feedlane' ),
            'category' => 'widgets',
            'icon' => 'feedback',
        ] );
    }
    
    /**
     * Render block
     */
    public function render_block( $attributes ) {
        // Extract attributes
        $position = $attributes['position'] ?? 'left';
        $height = $attributes['height'] ?? '600px';
        $primary_color = $attributes['primaryColor'] ?? '#0ea5e9';
        
        // Generate unique ID
        $unique_id = 'feedlane-gutenberg-' . wp_generate_uuid4();
        
        // Enqueue assets
        feedlane()->assets->enqueue( 'feedlane-sidebar', 'js/feedlane-sidebar.min.js' );
        feedlane()->assets->enqueue( 'feedlane-sidebar', 'css/feedlane-sidebar.min.css' );
        
        // Localize script data
        feedlane()->assets->localize(
            'feedlane-sidebar',
            'feedlaneGutenberg',
            [
                'instance_id' => $unique_id,
                'position' => $position,
                'height' => $height,
                'primary_color' => $primary_color,
            ]
        );
        
        ob_start();
        ?>
        <div id="<?php echo esc_attr( $unique_id ); ?>" 
             class="feedlane-gutenberg-block" 
             data-position="<?php echo esc_attr( $position ); ?>"
             data-height="<?php echo esc_attr( $height ); ?>"
             style="--feedlane-primary-color: <?php echo esc_attr( $primary_color ); ?>">
        </div>
        <?php
        return ob_get_clean();
    }
}
