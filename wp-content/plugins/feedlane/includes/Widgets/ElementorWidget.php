<?php

namespace WPDeveloper\Feedlane\Widgets;

use <PERSON>ementor\Widget_Base;
use Elementor\Controls_Manager;

/**
 * Feedlane Elementor Widget
 */
class ElementorWidget extends Widget_Base {

    /**
     * Constructor
     */
    public function __construct( $data = [], $args = null ) {
        parent::__construct( $data, $args );
    }

    /**
     * Get widget name
     */
    public function get_name() {
        return 'feedlane-sidebar';
    }

    /**
     * Get widget title
     */
    public function get_title() {
        return __( 'Feedlane Sidebar', 'feedlane' );
    }

    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-comments';
    }

    /**
     * Get widget categories
     */
    public function get_categories() {
        return [ 'general' ];
    }

    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return [ 'feedlane', 'feedback', 'sidebar', 'ideas', 'roadmap' ];
    }

    /**
     * Register widget controls
     */
    protected function _register_controls() {
        // Content Section
        $this->start_controls_section(
            'content_section',
            [
                'label' => __( 'Settings', 'feedlane' ),
                'tab' => Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'position',
            [
                'label' => __( 'Position', 'feedlane' ),
                'type' => Controls_Manager::SELECT,
                'default' => 'left',
                'options' => [
                    'left' => __( 'Left', 'feedlane' ),
                    'right' => __( 'Right', 'feedlane' ),
                ],
            ]
        );

        $this->add_control(
            'height',
            [
                'label' => __( 'Height', 'feedlane' ),
                'type' => Controls_Manager::TEXT,
                'default' => '600px',
                'description' => __( 'Set the height of the sidebar (e.g., 600px, 100vh)', 'feedlane' ),
            ]
        );

        $this->end_controls_section();

        // Style Section
        $this->start_controls_section(
            'style_section',
            [
                'label' => __( 'Style', 'feedlane' ),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'primary_color',
            [
                'label' => __( 'Primary Color', 'feedlane' ),
                'type' => Controls_Manager::COLOR,
                'default' => '#0ea5e9',
                'description' => __( 'Choose the primary color for buttons and accents', 'feedlane' ),
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Render widget output
     */
    protected function render() {
        $settings = $this->get_settings_for_display();

        // Generate unique ID
        $unique_id = 'feedlane-elementor-' . $this->get_id();

        // Enqueue assets
        feedlane()->assets->enqueue( 'feedlane-sidebar', 'js/feedlane-sidebar.min.js' );
        feedlane()->assets->enqueue( 'feedlane-sidebar', 'css/feedlane-sidebar.min.css' );

        // Localize script data
        feedlane()->assets->localize(
            'feedlane-sidebar',
            'feedlaneElementor',
            [
                'instance_id' => $unique_id,
                'position' => $settings['position'],
                'height' => $settings['height'],
                'primary_color' => $settings['primary_color'],
            ]
        );

        ?>
        <div id="<?php echo esc_attr( $unique_id ); ?>"
             class="feedlane-elementor-widget"
             data-position="<?php echo esc_attr( $settings['position'] ); ?>"
             data-height="<?php echo esc_attr( $settings['height'] ); ?>"
             style="--feedlane-primary-color: <?php echo esc_attr( $settings['primary_color'] ); ?>">
        </div>
        <?php
    }

    /**
     * Render widget output in the editor
     */
    protected function content_template() {
        ?>
        <#
        var unique_id = 'feedlane-elementor-' + view.getID();
        #>
        <div id="{{ unique_id }}"
             class="feedlane-elementor-widget feedlane-elementor-preview"
             data-position="{{ settings.position }}"
             data-height="{{ settings.height }}"
             style="--feedlane-primary-color: {{ settings.primary_color }}">
            <div class="feedlane-elementor-preview__content">
                <div class="feedlane-elementor-preview__icon">
                    <i class="eicon-comments"></i>
                </div>
                <h4><?php _e( 'Feedlane Sidebar', 'feedlane' ); ?></h4>
                <p><?php _e( 'Position:', 'feedlane' ); ?> {{ settings.position }}</p>
                <p><?php _e( 'Height:', 'feedlane' ); ?> {{ settings.height }}</p>
            </div>
        </div>

        <style>
        .feedlane-elementor-preview {
            border: 2px dashed #ddd;
            padding: 40px 20px;
            text-align: center;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .feedlane-elementor-preview__icon {
            font-size: 48px;
            color: #666;
            margin-bottom: 16px;
        }
        .feedlane-elementor-preview h4 {
            margin: 0 0 12px 0;
            color: #333;
        }
        .feedlane-elementor-preview p {
            margin: 4px 0;
            color: #666;
            font-size: 14px;
        }
        </style>
        <?php
    }
}
