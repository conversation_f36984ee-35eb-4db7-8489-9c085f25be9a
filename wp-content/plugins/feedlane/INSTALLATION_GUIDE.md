# Feedlane Plugin Installation & Troubleshooting Guide

## 🚀 Quick Installation Steps

### 1. **Activate the Debug Plugin First**
```
1. Go to WordPress Admin → Plugins
2. Activate "Feedlane Debug" plugin
3. Go to Admin → Feedlane Debug
4. Check the status of all components
```

### 2. **Activate Feedlane Plugin**
```
1. Go to WordPress Admin → Plugins
2. Find "Feedlane" plugin
3. Click "Activate"
4. Check for any error messages
```

### 3. **Verify Installation**
After activation, you should see:
- ✅ **Admin Menu**: "Feedlane" in the WordPress admin sidebar
- ✅ **Post Types**: Go to Admin → Posts → Add New → You should see "Feedlane Posts" and "Feedlane Ideas" in the admin menu
- ✅ **Gutenberg Block**: In block editor, search for "Feedlane Sidebar"
- ✅ **Elementor Widget**: In Elementor, search for "Feedlane Sidebar" widget

## 🔧 Troubleshooting Common Issues

### Issue 1: No Admin Menu Appearing

**Symptoms**: No "Feedlane" menu in WordPress admin

**Solutions**:
1. **Check Plugin Activation**:
   ```
   Go to Plugins → Make sure Feedlane is "Active"
   ```

2. **Check User Permissions**:
   ```
   Make sure you're logged in as Administrator
   ```

3. **Clear Cache**:
   ```
   If using caching plugins, clear all caches
   Refresh the admin page (Ctrl+F5)
   ```

4. **Check for PHP Errors**:
   ```
   Enable WP_DEBUG in wp-config.php:
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   ```

### Issue 2: Gutenberg Block Not Showing

**Symptoms**: Can't find "Feedlane Sidebar" block in editor

**Solutions**:
1. **Refresh Block Editor**:
   ```
   Go to Posts → Add New
   Click the "+" button to add blocks
   Search for "Feedlane"
   ```

2. **Check Block Registration**:
   ```
   Go to Feedlane Debug page
   Check if "Init Hook Fired" shows "YES"
   ```

3. **Manual Block Registration**:
   ```
   Add this to your theme's functions.php temporarily:
   
   add_action('init', function() {
       if (function_exists('register_block_type')) {
           register_block_type('feedlane/sidebar', [
               'render_callback' => function($attributes) {
                   return '<div class="feedlane-block">Feedlane Sidebar Block</div>';
               }
           ]);
       }
   });
   ```

### Issue 3: Elementor Widget Not Showing

**Symptoms**: Can't find "Feedlane Sidebar" widget in Elementor

**Solutions**:
1. **Check Elementor Version**:
   ```
   Make sure Elementor is installed and active
   Update to latest version if needed
   ```

2. **Clear Elementor Cache**:
   ```
   Go to Elementor → Tools → Regenerate CSS
   Clear Elementor cache
   ```

3. **Manual Widget Test**:
   ```
   Add this to functions.php to test:
   
   add_action('elementor/widgets/register', function($widgets_manager) {
       $widgets_manager->register(new class extends \Elementor\Widget_Base {
           public function get_name() { return 'feedlane-test'; }
           public function get_title() { return 'Feedlane Test'; }
           public function get_icon() { return 'eicon-posts-ticker'; }
           public function get_categories() { return ['general']; }
           protected function render() { echo '<div>Feedlane Test Widget</div>'; }
       });
   });
   ```

## 🔍 Debug Steps

### Step 1: Use the Debug Plugin
1. Activate "Feedlane Debug" plugin
2. Go to Admin → Feedlane Debug
3. Check all status indicators
4. Look for ❌ (red X) indicators

### Step 2: Check WordPress Error Log
1. Enable debugging in wp-config.php:
   ```php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   define('WP_DEBUG_DISPLAY', false);
   ```
2. Check `/wp-content/debug.log` for errors

### Step 3: Test Plugin Components
1. **Test Autoloader**:
   ```php
   // Add to functions.php temporarily
   add_action('init', function() {
       if (class_exists('WPDeveloper\Feedlane\Plugin')) {
           echo '<div class="notice notice-success"><p>Feedlane classes loaded!</p></div>';
       }
   });
   ```

2. **Test Post Types**:
   ```php
   // Add to functions.php temporarily
   add_action('admin_notices', function() {
       if (post_type_exists('feedlane_posts')) {
           echo '<div class="notice notice-success"><p>Feedlane post types registered!</p></div>';
       }
   });
   ```

## 🛠️ Manual Fixes

### Fix 1: Force Plugin Reactivation
```php
// Add to functions.php temporarily
add_action('admin_init', function() {
    if (isset($_GET['feedlane_force_activate'])) {
        deactivate_plugins('feedlane/feedlane.php');
        activate_plugin('feedlane/feedlane.php');
        wp_redirect(admin_url('admin.php?page=feedlane-debug'));
        exit;
    }
});

// Then visit: /wp-admin/admin.php?feedlane_force_activate=1
```

### Fix 2: Manual Menu Registration
```php
// Add to functions.php if admin menu doesn't appear
add_action('admin_menu', function() {
    add_menu_page(
        'Feedlane',
        'Feedlane',
        'manage_options',
        'feedlane-manual',
        function() {
            echo '<div class="wrap"><h1>Feedlane Manual Menu</h1><p>This is a manually registered menu.</p></div>';
        },
        'dashicons-feedback',
        30
    );
});
```

## 📞 Getting Help

If you're still having issues:

1. **Check the Debug Plugin Output**: Go to Admin → Feedlane Debug and screenshot the results
2. **Check Error Logs**: Look for PHP errors in `/wp-content/debug.log`
3. **Test with Default Theme**: Switch to a default WordPress theme temporarily
4. **Disable Other Plugins**: Test with only Feedlane active

## ✅ Success Indicators

When everything is working correctly, you should see:

- ✅ **Admin Menu**: "Feedlane" appears in WordPress admin sidebar
- ✅ **Submenus**: Dashboard, Settings, Analytics, Ideas Management
- ✅ **Post Types**: "Feedlane Posts" and "Feedlane Ideas" in admin menu
- ✅ **Gutenberg Block**: "Feedlane Sidebar" block available in editor
- ✅ **Elementor Widget**: "Feedlane Sidebar" widget in Elementor panel
- ✅ **Frontend**: Floating button appears on website pages
- ✅ **Assets**: CSS and JS files load without 404 errors

## 🎯 Next Steps After Installation

1. **Configure Settings**: Go to Feedlane → Settings
2. **Create Content**: Add some newsfeed posts
3. **Test Frontend**: Visit your website and click the floating button
4. **Add Shortcodes**: Try adding `[feedlane_sidebar]` to a page
5. **Test Submissions**: Submit test ideas and feedback
