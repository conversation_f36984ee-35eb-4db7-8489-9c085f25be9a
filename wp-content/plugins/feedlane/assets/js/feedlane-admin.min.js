(()=>{"use strict";var e={70:(e,t,a)=>{e.exports=a(462)},462:(e,t,a)=>{var s=a(609),n=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),r=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,a){var s,l={},c=null,u=null;for(s in void 0!==a&&(c=""+a),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,s)&&!o.hasOwnProperty(s)&&(l[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===l[s]&&(l[s]=t[s]);return{$$typeof:n,type:e,key:c,ref:u,props:l,_owner:r.current}}},576:(e,t,a)=>{var s=a(795);t.H=s.createRoot,s.hydrateRoot},609:e=>{e.exports=window.React},795:e=>{e.exports=window.ReactDOM}},t={};function a(s){var n=t[s];if(void 0!==n)return n.exports;var i=t[s]={exports:{}};return e[s](i,i.exports,a),i.exports}var s=a(609),n=a(576),i="undefined"==typeof window||"Deno"in globalThis;function r(){}function o(e,t){return"function"==typeof e?e(t):e}function l(e,t){const{type:a="all",exact:s,fetchStatus:n,predicate:i,queryKey:r,stale:o}=e;if(r)if(s){if(t.queryHash!==u(r,t.options))return!1}else if(!h(t.queryKey,r))return!1;if("all"!==a){const e=t.isActive();if("active"===a&&!e)return!1;if("inactive"===a&&e)return!1}return!("boolean"==typeof o&&t.isStale()!==o||n&&n!==t.state.fetchStatus||i&&!i(t))}function c(e,t){const{exact:a,status:s,predicate:n,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(a){if(d(t.options.mutationKey)!==d(i))return!1}else if(!h(t.options.mutationKey,i))return!1}return!(s&&t.state.status!==s||n&&!n(t))}function u(e,t){return(t?.queryKeyHashFn||d)(e)}function d(e){return JSON.stringify(e,((e,t)=>f(t)?Object.keys(t).sort().reduce(((e,a)=>(e[a]=t[a],e)),{}):t))}function h(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&Object.keys(t).every((a=>h(e[a],t[a])))}function m(e,t){if(e===t)return e;const a=p(e)&&p(t);if(a||f(e)&&f(t)){const s=a?e:Object.keys(e),n=s.length,i=a?t:Object.keys(t),r=i.length,o=a?[]:{};let l=0;for(let n=0;n<r;n++){const r=a?n:i[n];(!a&&s.includes(r)||a)&&void 0===e[r]&&void 0===t[r]?(o[r]=void 0,l++):(o[r]=m(e[r],t[r]),o[r]===e[r]&&void 0!==e[r]&&l++)}return n===r&&l===n?e:o}return t}function p(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function f(e){if(!y(e))return!1;const t=e.constructor;if(void 0===t)return!0;const a=t.prototype;return!!y(a)&&!!a.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function y(e){return"[object Object]"===Object.prototype.toString.call(e)}function b(e,t,a){return"function"==typeof a.structuralSharing?a.structuralSharing(e,t):!1!==a.structuralSharing?m(e,t):t}function v(e,t,a=0){const s=[...e,t];return a&&s.length>a?s.slice(1):s}function g(e,t,a=0){const s=[t,...e];return a&&s.length>a?s.slice(0,-1):s}var E=Symbol();function w(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==E?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}var x=e=>setTimeout(e,0),C=function(){let e=[],t=0,a=e=>{e()},s=e=>{e()},n=x;const i=s=>{t?e.push(s):n((()=>{a(s)}))};return{batch:i=>{let r;t++;try{r=i()}finally{t--,t||(()=>{const t=e;e=[],t.length&&n((()=>{s((()=>{t.forEach((e=>{a(e)}))}))}))})()}return r},batchCalls:e=>(...t)=>{i((()=>{e(...t)}))},schedule:i,setNotifyFunction:e=>{a=e},setBatchNotifyFunction:e=>{s=e},setScheduler:e=>{n=e}}}(),N=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},_=new class extends N{#e;#t;#a;constructor(){super(),this.#a=e=>{if(!i&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#a)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#a=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},O=new class extends N{#s=!0;#t;#a;constructor(){super(),this.#a=e=>{if(!i&&window.addEventListener){const t=()=>e(!0),a=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",a,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",a)}}}}onSubscribe(){this.#t||this.setEventListener(this.#a)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#a=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#s!==e&&(this.#s=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#s}};function S(e){return Math.min(1e3*2**e,3e4)}function q(e){return"online"!==(e??"online")||O.isOnline()}var P=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function F(e){return e instanceof P}function k(e){let t,a=!1,s=0,n=!1;const r=function(){let e,t;const a=new Promise(((a,s)=>{e=a,t=s}));function s(e){Object.assign(a,e),delete a.resolve,delete a.reject}return a.status="pending",a.catch((()=>{})),a.resolve=t=>{s({status:"fulfilled",value:t}),e(t)},a.reject=e=>{s({status:"rejected",reason:e}),t(e)},a}(),o=()=>_.isFocused()&&("always"===e.networkMode||O.isOnline())&&e.canRun(),l=()=>q(e.networkMode)&&e.canRun(),c=a=>{n||(n=!0,e.onSuccess?.(a),t?.(),r.resolve(a))},u=a=>{n||(n=!0,e.onError?.(a),t?.(),r.reject(a))},d=()=>new Promise((a=>{t=e=>{(n||o())&&a(e)},e.onPause?.()})).then((()=>{t=void 0,n||e.onContinue?.()})),h=()=>{if(n)return;let t;const r=0===s?e.initialPromise:void 0;try{t=r??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(c).catch((t=>{if(n)return;const r=e.retry??(i?0:3),l=e.retryDelay??S,c="function"==typeof l?l(s,t):l,m=!0===r||"number"==typeof r&&s<r||"function"==typeof r&&r(s,t);var p;!a&&m?(s++,e.onFail?.(s,t),(p=c,new Promise((e=>{setTimeout(e,p)}))).then((()=>o()?void 0:d())).then((()=>{a?u(t):h()}))):u(t)}))};return{promise:r,cancel:t=>{n||(u(new P(t)),e.abort?.())},continue:()=>(t?.(),r),cancelRetry:()=>{a=!0},continueRetry:()=>{a=!1},canStart:l,start:()=>(l()?h():d().then(h),r)}}var D=class{#n;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#n=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(i?1/0:3e5))}clearGcTimeout(){this.#n&&(clearTimeout(this.#n),this.#n=void 0)}},A=class extends D{#i;#r;#o;#l;#c;#u;#d;constructor(e){super(),this.#d=!1,this.#u=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.client,this.#o=this.#l.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#i=function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,a=void 0!==t,s=a?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:a?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:a?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#i,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(e){this.options={...this.#u,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(e,t){const a=b(this.state.data,e,this.options);return this.#h({data:a,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),a}setState(e,t){this.#h({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#c?.promise;return this.#c?.cancel(e),t?t.then(r).catch(r):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#i)}isActive(){return this.observers.some((e=>{return!1!==("function"==typeof(t=e.options.enabled)?t(this):t);var t}))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===E||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!function(e,t){return Math.max(e+(t||0)-Date.now(),0)}(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#c&&(this.#d?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#h({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const a=new AbortController,s=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,a.signal)})},n={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:()=>{const e=w(this.options,t),a={client:this.#l,queryKey:this.queryKey,meta:this.meta};return s(a),this.#d=!1,this.options.persister?this.options.persister(e,a,this):e(a)}};s(n),this.options.behavior?.onFetch(n,this),this.#r=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===n.fetchOptions?.meta||this.#h({type:"fetch",meta:n.fetchOptions?.meta});const i=e=>{F(e)&&e.silent||this.#h({type:"error",error:e}),F(e)||(this.#o.config.onError?.(e,this),this.#o.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#c=k({initialPromise:t?.initialPromise,fn:n.fetchFn,abort:a.abort.bind(a),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void i(e)}this.#o.config.onSuccess?.(e,this),this.#o.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else i(new Error(`${this.queryHash} data is undefined`))},onError:i,onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:()=>{this.#h({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#c.start()}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...(a=t.data,s=this.options,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:q(s.networkMode)?"fetching":"paused",...void 0===a&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=e.error;return F(n)&&n.revert&&this.#r?{...this.#r,fetchStatus:"idle"}:{...t,error:n,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}var a,s})(this.state),C.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#o.notify({query:this,type:"updated",action:e})}))}},R=class extends N{constructor(e={}){super(),this.config=e,this.#m=new Map}#m;build(e,t,a){const s=t.queryKey,n=t.queryHash??u(s,t);let i=this.get(n);return i||(i=new A({client:e,queryKey:s,queryHash:n,options:e.defaultQueryOptions(t),state:a,defaultOptions:e.getQueryDefaults(s)}),this.add(i)),i}add(e){this.#m.has(e.queryHash)||(this.#m.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#m.get(e.queryHash);t&&(e.destroy(),t===e&&this.#m.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){C.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#m.get(e)}getAll(){return[...this.#m.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>l(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>l(e,t))):t}notify(e){C.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){C.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){C.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}},T=class extends D{#p;#f;#c;constructor(e){super(),this.mutationId=e.mutationId,this.#f=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#f.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter((t=>t!==e)),this.scheduleGc(),this.#f.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#f.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#h({type:"continue"})};this.#c=k({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#f.canRun(this)});const a="pending"===this.state.status,s=!this.#c.canStart();try{if(a)t();else{this.#h({type:"pending",variables:e,isPaused:s}),await(this.#f.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#h({type:"pending",context:t,variables:e,isPaused:s})}const n=await this.#c.start();return await(this.#f.config.onSuccess?.(n,e,this.state.context,this)),await(this.options.onSuccess?.(n,e,this.state.context)),await(this.#f.config.onSettled?.(n,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(n,null,e,this.state.context)),this.#h({type:"success",data:n}),n}catch(t){try{throw await(this.#f.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#f.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#h({type:"error",error:t})}}finally{this.#f.runNext(this)}}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),C.batch((()=>{this.#p.forEach((t=>{t.onMutationUpdate(e)})),this.#f.notify({mutation:this,type:"updated",action:e})}))}},M=class extends N{constructor(e={}){super(),this.config=e,this.#y=new Set,this.#b=new Map,this.#v=0}#y;#b;#v;build(e,t,a){const s=new T({mutationCache:this,mutationId:++this.#v,options:e.defaultMutationOptions(t),state:a});return this.add(s),s}add(e){this.#y.add(e);const t=I(e);if("string"==typeof t){const a=this.#b.get(t);a?a.push(e):this.#b.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#y.delete(e)){const t=I(e);if("string"==typeof t){const a=this.#b.get(t);if(a)if(a.length>1){const t=a.indexOf(e);-1!==t&&a.splice(t,1)}else a[0]===e&&this.#b.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=I(e);if("string"==typeof t){const a=this.#b.get(t),s=a?.find((e=>"pending"===e.state.status));return!s||s===e}return!0}runNext(e){const t=I(e);if("string"==typeof t){const a=this.#b.get(t)?.find((t=>t!==e&&t.state.isPaused));return a?.continue()??Promise.resolve()}return Promise.resolve()}clear(){C.batch((()=>{this.#y.forEach((e=>{this.notify({type:"removed",mutation:e})})),this.#y.clear(),this.#b.clear()}))}getAll(){return Array.from(this.#y)}find(e){const t={exact:!0,...e};return this.getAll().find((e=>c(t,e)))}findAll(e={}){return this.getAll().filter((t=>c(e,t)))}notify(e){C.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return C.batch((()=>Promise.all(e.map((e=>e.continue().catch(r))))))}};function I(e){return e.options.scope?.id}function j(e){return{onFetch:(t,a)=>{const s=t.options,n=t.fetchOptions?.meta?.fetchMore?.direction,i=t.state.data?.pages||[],r=t.state.data?.pageParams||[];let o={pages:[],pageParams:[]},l=0;const c=async()=>{let a=!1;const c=w(t.options,t.fetchOptions),u=async(e,s,n)=>{if(a)return Promise.reject();if(null==s&&e.pages.length)return Promise.resolve(e);const i={client:t.client,queryKey:t.queryKey,pageParam:s,direction:n?"backward":"forward",meta:t.options.meta};var r;r=i,Object.defineProperty(r,"signal",{enumerable:!0,get:()=>(t.signal.aborted?a=!0:t.signal.addEventListener("abort",(()=>{a=!0})),t.signal)});const o=await c(i),{maxPages:l}=t.options,u=n?g:v;return{pages:u(e.pages,o,l),pageParams:u(e.pageParams,s,l)}};if(n&&i.length){const e="backward"===n,t={pages:i,pageParams:r},a=(e?U:Q)(s,t);o=await u(t,a,e)}else{const t=e??i.length;do{const e=0===l?r[0]??s.initialPageParam:Q(s,o);if(l>0&&null==e)break;o=await u(o,e),l++}while(l<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},a):t.fetchFn=c}}}function Q(e,{pages:t,pageParams:a}){const s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,a[s],a):void 0}function U(e,{pages:t,pageParams:a}){return t.length>0?e.getPreviousPageParam?.(t[0],t,a[0],a):void 0}var K=a(70),H=s.createContext(void 0),L=({client:e,children:t})=>(s.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,K.jsx)(H.Provider,{value:e,children:t}));let $={data:""},G=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||$,B=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,z=/\/\*[^]*?\*\/|  +/g,V=/\n+/g,J=(e,t)=>{let a="",s="",n="";for(let i in e){let r=e[i];"@"==i[0]?"i"==i[1]?a=i+" "+r+";":s+="f"==i[1]?J(r,i):i+"{"+J(r,"k"==i[1]?"":t)+"}":"object"==typeof r?s+=J(r,t?t.replace(/([^,])+/g,(e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):i):null!=r&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=J.p?J.p(i,r):i+":"+r+";")}return a+(t&&n?t+"{"+n+"}":n)+s},W={},Y=e=>{if("object"==typeof e){let t="";for(let a in e)t+=a+Y(e[a]);return t}return e},Z=(e,t,a,s,n)=>{let i=Y(e),r=W[i]||(W[i]=(e=>{let t=0,a=11;for(;t<e.length;)a=101*a+e.charCodeAt(t++)>>>0;return"go"+a})(i));if(!W[r]){let t=i!==e?e:(e=>{let t,a,s=[{}];for(;t=B.exec(e.replace(z,""));)t[4]?s.shift():t[3]?(a=t[3].replace(V," ").trim(),s.unshift(s[0][a]=s[0][a]||{})):s[0][t[1]]=t[2].replace(V," ").trim();return s[0]})(e);W[r]=J(n?{["@keyframes "+r]:t}:t,a?"":"."+r)}let o=a&&W.g?W.g:null;return a&&(W.g=W[r]),((e,t,a,s)=>{s?t.data=t.data.replace(s,e):-1===t.data.indexOf(e)&&(t.data=a?e+t.data:t.data+e)})(W[r],t,s,o),r};function X(e){let t=this||{},a=e.call?e(t.p):e;return Z(a.unshift?a.raw?((e,t,a)=>e.reduce(((e,s,n)=>{let i=t[n];if(i&&i.call){let e=i(a),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":J(e,""):!1===e?"":e}return e+s+(null==i?"":i)}),""))(a,[].slice.call(arguments,1),t.p):a.reduce(((e,a)=>Object.assign(e,a&&a.call?a(t.p):a)),{}):a,G(t.target),t.g,t.o,t.k)}X.bind({g:1});let ee,te,ae,se=X.bind({k:1});function ne(e,t){let a=this||{};return function(){let s=arguments;function n(i,r){let o=Object.assign({},i),l=o.className||n.className;a.p=Object.assign({theme:te&&te()},o),a.o=/ *go\d+/.test(l),o.className=X.apply(a,s)+(l?" "+l:""),t&&(o.ref=r);let c=e;return e[0]&&(c=o.as||e,delete o.as),ae&&c[0]&&ae(o),ee(c,o)}return t?t(n):n}}var ie=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,re=(()=>{let e=0;return()=>(++e).toString()})(),oe=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),le=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case 2:let{toast:a}=t;return le(e,{type:e.toasts.find((e=>e.id===a.id))?1:0,toast:a});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map((e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))};case 5:return{...e,pausedAt:t.time};case 6:let n=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+n})))}}},ce=[],ue={toasts:[],pausedAt:void 0},de=e=>{ue=le(ue,e),ce.forEach((e=>{e(ue)}))},he={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},me=e=>(t,a)=>{let s=((e,t="blank",a)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...a,id:(null==a?void 0:a.id)||re()}))(t,e,a);return de({type:2,toast:s}),s.id},pe=(e,t)=>me("blank")(e,t);pe.error=me("error"),pe.success=me("success"),pe.loading=me("loading"),pe.custom=me("custom"),pe.dismiss=e=>{de({type:3,toastId:e})},pe.remove=e=>de({type:4,toastId:e}),pe.promise=(e,t,a)=>{let s=pe.loading(t.loading,{...a,...null==a?void 0:a.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let n=t.success?ie(t.success,e):void 0;return n?pe.success(n,{id:s,...a,...null==a?void 0:a.success}):pe.dismiss(s),e})).catch((e=>{let n=t.error?ie(t.error,e):void 0;n?pe.error(n,{id:s,...a,...null==a?void 0:a.error}):pe.dismiss(s)})),e};var fe=(e,t)=>{de({type:1,toast:{id:e,height:t}})},ye=()=>{de({type:5,time:Date.now()})},be=new Map,ve=se`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,ge=se`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Ee=se`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,we=ne("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${ve} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${ge} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Ee} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,xe=se`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Ce=ne("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${xe} 1s linear infinite;
`,Ne=se`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,_e=se`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Oe=ne("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ne} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${_e} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Se=ne("div")`
  position: absolute;
`,qe=ne("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Pe=se`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Fe=ne("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Pe} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,ke=({toast:e})=>{let{icon:t,type:a,iconTheme:n}=e;return void 0!==t?"string"==typeof t?s.createElement(Fe,null,t):t:"blank"===a?null:s.createElement(qe,null,s.createElement(Ce,{...n}),"loading"!==a&&s.createElement(Se,null,"error"===a?s.createElement(we,{...n}):s.createElement(Oe,{...n})))},De=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,Ae=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,Re=ne("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Te=ne("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Me=s.memo((({toast:e,position:t,style:a,children:n})=>{let i=e.height?((e,t)=>{let a=e.includes("top")?1:-1,[s,n]=oe()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[De(a),Ae(a)];return{animation:t?`${se(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${se(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||t||"top-center",e.visible):{opacity:0},r=s.createElement(ke,{toast:e}),o=s.createElement(Te,{...e.ariaProps},ie(e.message,e));return s.createElement(Re,{className:e.className,style:{...i,...a,...e.style}},"function"==typeof n?n({icon:r,message:o}):s.createElement(s.Fragment,null,r,o))}));!function(e){J.p=void 0,ee=e,te=void 0,ae=void 0}(s.createElement);var Ie=({id:e,className:t,style:a,onHeightUpdate:n,children:i})=>{let r=s.useCallback((t=>{if(t){let a=()=>{let a=t.getBoundingClientRect().height;n(e,a)};a(),new MutationObserver(a).observe(t,{subtree:!0,childList:!0,characterData:!0})}}),[e,n]);return s.createElement("div",{ref:r,className:t,style:a},i)},je=X`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Qe=({reverseOrder:e,position:t="top-center",toastOptions:a,gutter:n,children:i,containerStyle:r,containerClassName:o})=>{let{toasts:l,handlers:c}=(e=>{let{toasts:t,pausedAt:a}=((e={})=>{let[t,a]=(0,s.useState)(ue),n=(0,s.useRef)(ue);(0,s.useEffect)((()=>(n.current!==ue&&a(ue),ce.push(a),()=>{let e=ce.indexOf(a);e>-1&&ce.splice(e,1)})),[]);let i=t.toasts.map((t=>{var a,s,n;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(a=e[t.type])?void 0:a.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||he[t.type],style:{...e.style,...null==(n=e[t.type])?void 0:n.style,...t.style}}}));return{...t,toasts:i}})(e);(0,s.useEffect)((()=>{if(a)return;let e=Date.now(),s=t.map((t=>{if(t.duration===1/0)return;let a=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(a<0))return setTimeout((()=>pe.dismiss(t.id)),a);t.visible&&pe.dismiss(t.id)}));return()=>{s.forEach((e=>e&&clearTimeout(e)))}}),[t,a]);let n=(0,s.useCallback)((()=>{a&&de({type:6,time:Date.now()})}),[a]),i=(0,s.useCallback)(((e,a)=>{let{reverseOrder:s=!1,gutter:n=8,defaultPosition:i}=a||{},r=t.filter((t=>(t.position||i)===(e.position||i)&&t.height)),o=r.findIndex((t=>t.id===e.id)),l=r.filter(((e,t)=>t<o&&e.visible)).length;return r.filter((e=>e.visible)).slice(...s?[l+1]:[0,l]).reduce(((e,t)=>e+(t.height||0)+n),0)}),[t]);return(0,s.useEffect)((()=>{t.forEach((e=>{if(e.dismissed)((e,t=1e3)=>{if(be.has(e))return;let a=setTimeout((()=>{be.delete(e),de({type:4,toastId:e})}),t);be.set(e,a)})(e.id,e.removeDelay);else{let t=be.get(e.id);t&&(clearTimeout(t),be.delete(e.id))}}))}),[t]),{toasts:t,handlers:{updateHeight:fe,startPause:ye,endPause:n,calculateOffset:i}}})(a);return s.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...r},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map((a=>{let r=a.position||t,o=((e,t)=>{let a=e.includes("top"),s=a?{top:0}:{bottom:0},n=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:oe()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(a?1:-1)}px)`,...s,...n}})(r,c.calculateOffset(a,{reverseOrder:e,gutter:n,defaultPosition:t}));return s.createElement(Ie,{id:a.id,key:a.id,onHeightUpdate:c.updateHeight,className:a.visible?je:"",style:o},"custom"===a.type?ie(a.message,a):i?i(a):s.createElement(Me,{toast:a,position:r}))})))},Ue=pe;const Ke=()=>(0,s.createElement)("div",{className:"feedlane-admin"},(0,s.createElement)("div",{className:"feedlane-admin__header"},(0,s.createElement)("h1",null,"Feedlane Dashboard"),(0,s.createElement)("p",null,"Overview of your feedback system")),(0,s.createElement)("div",{className:"feedlane-admin__content"},(0,s.createElement)("div",{className:"feedlane-admin__grid"},(0,s.createElement)("div",{className:"feedlane-admin__card"},(0,s.createElement)("div",{className:"stat-number"},12),(0,s.createElement)("div",{className:"stat-label"},"Newsfeed Posts"),(0,s.createElement)("p",null,"Published announcements and updates")),(0,s.createElement)("div",{className:"feedlane-admin__card"},(0,s.createElement)("div",{className:"stat-number"},45),(0,s.createElement)("div",{className:"stat-label"},"Total Ideas"),(0,s.createElement)("p",null,"Ideas submitted by users")),(0,s.createElement)("div",{className:"feedlane-admin__card"},(0,s.createElement)("div",{className:"stat-number"},8),(0,s.createElement)("div",{className:"stat-label"},"Pending Ideas"),(0,s.createElement)("p",null,"Ideas awaiting approval")),(0,s.createElement)("div",{className:"feedlane-admin__card"},(0,s.createElement)("div",{className:"stat-number"},156),(0,s.createElement)("div",{className:"stat-label"},"Feedback Comments"),(0,s.createElement)("p",null,"User feedback on posts")),(0,s.createElement)("div",{className:"feedlane-admin__card"},(0,s.createElement)("div",{className:"stat-number"},234),(0,s.createElement)("div",{className:"stat-label"},"Total Votes"),(0,s.createElement)("p",null,"Votes cast on ideas")),(0,s.createElement)("div",{className:"feedlane-admin__card"},(0,s.createElement)("h3",null,"Quick Actions"),(0,s.createElement)("div",{className:"space-y-2"},(0,s.createElement)("a",{href:"post-new.php?post_type=feedlane_posts",className:"feedlane-btn feedlane-btn--primary feedlane-btn--small block text-center"},"Add Newsfeed Post"),(0,s.createElement)("a",{href:"admin.php?page=feedlane-ideas",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Manage Ideas"),(0,s.createElement)("a",{href:"admin.php?page=feedlane-settings",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Settings")))))),He=()=>{const[e,t]=(0,s.useState)({enable_newsfeed:!0,enable_ideas:!0,enable_roadmap:!0,enable_guest_submissions:!0,sidebar_position:"left",primary_color:"#0ea5e9",firebase_config:"",firebase_webhook_secret:""}),[a,n]=(0,s.useState)(!1),i=e=>{const{name:a,value:s,type:n,checked:i}=e.target;t((e=>({...e,[a]:"checkbox"===n?i:s})))};return(0,s.createElement)("div",{className:"feedlane-admin"},(0,s.createElement)("div",{className:"feedlane-admin__header"},(0,s.createElement)("h1",null,"Feedlane Settings"),(0,s.createElement)("p",null,"Configure your feedback system")),(0,s.createElement)("div",{className:"feedlane-admin__content"},(0,s.createElement)("form",{onSubmit:async e=>{e.preventDefault(),n(!0);try{await new Promise((e=>setTimeout(e,1e3))),Ue.success("Settings saved successfully!")}catch(e){Ue.error("Failed to save settings")}finally{n(!1)}},className:"feedlane-form"},(0,s.createElement)("div",{className:"feedlane-form__section"},(0,s.createElement)("h3",null,"General Settings"),(0,s.createElement)("p",null,"Configure which tabs are enabled and basic appearance"),(0,s.createElement)("div",{className:"space-y-4"},(0,s.createElement)("div",{className:"feedlane-form__field"},(0,s.createElement)("label",null,(0,s.createElement)("input",{type:"checkbox",name:"enable_newsfeed",checked:e.enable_newsfeed,onChange:i,className:"mr-2"}),"Enable Newsfeed Tab"),(0,s.createElement)("div",{className:"description"},"Show the newsfeed tab in the sidebar")),(0,s.createElement)("div",{className:"feedlane-form__field"},(0,s.createElement)("label",null,(0,s.createElement)("input",{type:"checkbox",name:"enable_ideas",checked:e.enable_ideas,onChange:i,className:"mr-2"}),"Enable Ideas Tab"),(0,s.createElement)("div",{className:"description"},"Show the ideas submission tab in the sidebar")),(0,s.createElement)("div",{className:"feedlane-form__field"},(0,s.createElement)("label",null,(0,s.createElement)("input",{type:"checkbox",name:"enable_roadmap",checked:e.enable_roadmap,onChange:i,className:"mr-2"}),"Enable Roadmap Tab"),(0,s.createElement)("div",{className:"description"},"Show the roadmap tab in the sidebar")),(0,s.createElement)("div",{className:"feedlane-form__field"},(0,s.createElement)("label",null,(0,s.createElement)("input",{type:"checkbox",name:"enable_guest_submissions",checked:e.enable_guest_submissions,onChange:i,className:"mr-2"}),"Enable Guest Submissions"),(0,s.createElement)("div",{className:"description"},"Allow non-logged-in users to submit feedback and ideas")),(0,s.createElement)("div",{className:"feedlane-form__field"},(0,s.createElement)("label",{htmlFor:"sidebar_position"},"Sidebar Position"),(0,s.createElement)("select",{id:"sidebar_position",name:"sidebar_position",value:e.sidebar_position,onChange:i},(0,s.createElement)("option",{value:"left"},"Left"),(0,s.createElement)("option",{value:"right"},"Right")),(0,s.createElement)("div",{className:"description"},"Choose which side of the screen the sidebar appears on")),(0,s.createElement)("div",{className:"feedlane-form__field"},(0,s.createElement)("label",{htmlFor:"primary_color"},"Primary Color"),(0,s.createElement)("input",{type:"color",id:"primary_color",name:"primary_color",value:e.primary_color,onChange:i}),(0,s.createElement)("div",{className:"description"},"Choose the primary color for the sidebar and buttons")))),(0,s.createElement)("div",{className:"feedlane-form__section"},(0,s.createElement)("h3",null,"Firebase Configuration"),(0,s.createElement)("p",null,"Configure Firebase for real-time comments functionality"),(0,s.createElement)("div",{className:"space-y-4"},(0,s.createElement)("div",{className:"feedlane-form__field"},(0,s.createElement)("label",{htmlFor:"firebase_config"},"Firebase Configuration JSON"),(0,s.createElement)("textarea",{id:"firebase_config",name:"firebase_config",value:e.firebase_config,onChange:i,rows:"6",placeholder:'{"apiKey": "...", "authDomain": "...", "projectId": "..."}'}),(0,s.createElement)("div",{className:"description"},"Paste your Firebase configuration JSON here for real-time comments")),(0,s.createElement)("div",{className:"feedlane-form__field"},(0,s.createElement)("label",{htmlFor:"firebase_webhook_secret"},"Webhook Secret"),(0,s.createElement)("input",{type:"password",id:"firebase_webhook_secret",name:"firebase_webhook_secret",value:e.firebase_webhook_secret,onChange:i}),(0,s.createElement)("div",{className:"description"},"Secret key for Firebase webhook authentication")))),(0,s.createElement)("div",{className:"feedlane-form__actions"},(0,s.createElement)("button",{type:"submit",disabled:a,className:"feedlane-btn feedlane-btn--primary"},a?"Saving...":"Save Settings")))))},Le=()=>{const e={totalViews:1234,totalReactions:567,totalVotes:234,topPosts:[{id:1,title:"New Feature Release",views:234,reactions:45},{id:2,title:"Bug Fix Update",views:189,reactions:32},{id:3,title:"Roadmap Update",views:156,reactions:28}],topIdeas:[{id:1,title:"Dark Mode Support",votes:89,category:"Feature Request"},{id:2,title:"Mobile App",votes:67,category:"Feature Request"},{id:3,title:"Better Search",votes:45,category:"Improvement"}]};return(0,s.createElement)("div",{className:"feedlane-admin"},(0,s.createElement)("div",{className:"feedlane-admin__header"},(0,s.createElement)("h1",null,"Analytics"),(0,s.createElement)("p",null,"Track engagement and performance metrics")),(0,s.createElement)("div",{className:"feedlane-admin__content"},(0,s.createElement)("div",{className:"feedlane-admin__grid"},(0,s.createElement)("div",{className:"feedlane-admin__card"},(0,s.createElement)("div",{className:"stat-number"},e.totalViews),(0,s.createElement)("div",{className:"stat-label"},"Total Views"),(0,s.createElement)("p",null,"Post and idea views")),(0,s.createElement)("div",{className:"feedlane-admin__card"},(0,s.createElement)("div",{className:"stat-number"},e.totalReactions),(0,s.createElement)("div",{className:"stat-label"},"Total Reactions"),(0,s.createElement)("p",null,"Emoji reactions on posts")),(0,s.createElement)("div",{className:"feedlane-admin__card"},(0,s.createElement)("div",{className:"stat-number"},e.totalVotes),(0,s.createElement)("div",{className:"stat-label"},"Total Votes"),(0,s.createElement)("p",null,"Votes on ideas"))),(0,s.createElement)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8"},(0,s.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,s.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Posts"),(0,s.createElement)("div",{className:"space-y-3"},e.topPosts.map((e=>(0,s.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,s.createElement)("div",null,(0,s.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,s.createElement)("p",{className:"text-sm text-gray-500"},e.views," views • ",e.reactions," reactions"))))))),(0,s.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,s.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Ideas"),(0,s.createElement)("div",{className:"space-y-3"},e.topIdeas.map((e=>(0,s.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,s.createElement)("div",null,(0,s.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,s.createElement)("p",{className:"text-sm text-gray-500"},e.category," • ",e.votes," votes"))))))))))},$e=()=>{const[e,t]=(0,s.useState)([{id:1,title:"Dark Mode Support",category:"Feature Request",status:"pending",votes:89,submitter:"John Doe",date:"2024-01-15",excerpt:"Add dark mode theme option for better user experience..."},{id:2,title:"Mobile App",category:"Feature Request",status:"under-review",votes:67,submitter:"Jane Smith",date:"2024-01-14",excerpt:"Create a mobile application for iOS and Android..."},{id:3,title:"Better Search",category:"Improvement",status:"planned",votes:45,submitter:"Mike Johnson",date:"2024-01-13",excerpt:"Improve search functionality with filters and sorting..."}]),[a,n]=(0,s.useState)("all"),i=(e,a)=>{t((t=>t.map((t=>t.id===e?{...t,status:a}:t)))),Ue.success("Status updated successfully!")},r="all"===a?e:e.filter((e=>e.status===a));return(0,s.createElement)("div",{className:"feedlane-admin"},(0,s.createElement)("div",{className:"feedlane-admin__header"},(0,s.createElement)("h1",null,"Ideas Management"),(0,s.createElement)("p",null,"Review and manage submitted ideas")),(0,s.createElement)("div",{className:"feedlane-admin__content"},(0,s.createElement)("div",{className:"mb-6"},(0,s.createElement)("div",{className:"flex gap-2"},(0,s.createElement)("button",{onClick:()=>n("all"),className:"feedlane-btn feedlane-btn--small "+("all"===a?"feedlane-btn--primary":"feedlane-btn--secondary")},"All (",e.length,")"),(0,s.createElement)("button",{onClick:()=>n("pending"),className:"feedlane-btn feedlane-btn--small "+("pending"===a?"feedlane-btn--primary":"feedlane-btn--secondary")},"Pending (",e.filter((e=>"pending"===e.status)).length,")"),(0,s.createElement)("button",{onClick:()=>n("under-review"),className:"feedlane-btn feedlane-btn--small "+("under-review"===a?"feedlane-btn--primary":"feedlane-btn--secondary")},"Under Review (",e.filter((e=>"under-review"===e.status)).length,")"),(0,s.createElement)("button",{onClick:()=>n("planned"),className:"feedlane-btn feedlane-btn--small "+("planned"===a?"feedlane-btn--primary":"feedlane-btn--secondary")},"Planned (",e.filter((e=>"planned"===e.status)).length,")"))),(0,s.createElement)("div",{className:"overflow-x-auto"},(0,s.createElement)("table",{className:"feedlane-table"},(0,s.createElement)("thead",null,(0,s.createElement)("tr",null,(0,s.createElement)("th",null,"Title"),(0,s.createElement)("th",null,"Category"),(0,s.createElement)("th",null,"Status"),(0,s.createElement)("th",null,"Votes"),(0,s.createElement)("th",null,"Submitter"),(0,s.createElement)("th",null,"Date"),(0,s.createElement)("th",null,"Actions"))),(0,s.createElement)("tbody",null,r.map((e=>{return(0,s.createElement)("tr",{key:e.id},(0,s.createElement)("td",null,(0,s.createElement)("div",null,(0,s.createElement)("div",{className:"font-medium text-gray-900"},e.title),(0,s.createElement)("div",{className:"text-sm text-gray-500"},e.excerpt))),(0,s.createElement)("td",null,e.category),(0,s.createElement)("td",null,(a=e.status,(0,s.createElement)("span",{className:`feedlane-badge ${{pending:"feedlane-badge--warning","under-review":"feedlane-badge--info",planned:"feedlane-badge--success","in-progress":"feedlane-badge--info",completed:"feedlane-badge--success"}[a]||"feedlane-badge--gray"}`},a.replace("-"," ").replace(/\b\w/g,(e=>e.toUpperCase()))))),(0,s.createElement)("td",null,e.votes),(0,s.createElement)("td",null,e.submitter),(0,s.createElement)("td",null,e.date),(0,s.createElement)("td",null,(0,s.createElement)("div",{className:"flex gap-2"},"pending"===e.status&&(0,s.createElement)(s.Fragment,null,(0,s.createElement)("button",{onClick:()=>{return t=e.id,void i(t,"under-review");var t},className:"feedlane-btn feedlane-btn--primary feedlane-btn--small"},"Approve"),(0,s.createElement)("button",{onClick:()=>{return a=e.id,t((e=>e.filter((e=>e.id!==a)))),void Ue.success("Idea rejected and removed");var a},className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Reject")),"pending"!==e.status&&(0,s.createElement)("select",{value:e.status,onChange:t=>i(e.id,t.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1"},(0,s.createElement)("option",{value:"under-review"},"Under Review"),(0,s.createElement)("option",{value:"planned"},"Planned"),(0,s.createElement)("option",{value:"in-progress"},"In Progress"),(0,s.createElement)("option",{value:"completed"},"Completed")))));var a}))))),0===r.length&&(0,s.createElement)("div",{className:"feedlane-empty"},(0,s.createElement)("div",{className:"feedlane-empty__icon"},"💡"),(0,s.createElement)("h3",null,"No Ideas Found"),(0,s.createElement)("p",null,"No ideas match the current filter."))))},Ge=new class{#g;#f;#u;#E;#w;#x;#C;#N;constructor(e={}){this.#g=e.queryCache||new R,this.#f=e.mutationCache||new M,this.#u=e.defaultOptions||{},this.#E=new Map,this.#w=new Map,this.#x=0}mount(){this.#x++,1===this.#x&&(this.#C=_.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#g.onFocus())})),this.#N=O.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#g.onOnline())})))}unmount(){this.#x--,0===this.#x&&(this.#C?.(),this.#C=void 0,this.#N?.(),this.#N=void 0)}isFetching(e){return this.#g.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#f.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),a=this.#g.build(this,t),s=a.state.data;return void 0===s?this.fetchQuery(e):(e.revalidateIfStale&&a.isStaleByTime(o(t.staleTime,a))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return this.#g.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,a){const s=this.defaultQueryOptions({queryKey:e}),n=this.#g.get(s.queryHash),i=n?.state.data,r=function(e,t){return"function"==typeof e?e(t):e}(t,i);if(void 0!==r)return this.#g.build(this,s).setData(r,{...a,manual:!0})}setQueriesData(e,t,a){return C.batch((()=>this.#g.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,a)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state}removeQueries(e){const t=this.#g;C.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const a=this.#g;return C.batch((()=>(a.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries({type:"active",...e},t))))}cancelQueries(e,t={}){const a={revert:!0,...t},s=C.batch((()=>this.#g.findAll(e).map((e=>e.cancel(a)))));return Promise.all(s).then(r).catch(r)}invalidateQueries(e,t={}){return C.batch((()=>(this.#g.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))))}refetchQueries(e,t={}){const a={...t,cancelRefetch:t.cancelRefetch??!0},s=C.batch((()=>this.#g.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,a);return a.throwOnError||(t=t.catch(r)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(s).then(r)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const a=this.#g.build(this,t);return a.isStaleByTime(o(t.staleTime,a))?a.fetch(t):Promise.resolve(a.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r).catch(r)}fetchInfiniteQuery(e){return e.behavior=j(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r).catch(r)}ensureInfiniteQueryData(e){return e.behavior=j(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return O.isOnline()?this.#f.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#g}getMutationCache(){return this.#f}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,t){this.#E.set(d(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#E.values()],a={};return t.forEach((t=>{h(e,t.queryKey)&&Object.assign(a,t.defaultOptions)})),a}setMutationDefaults(e,t){this.#w.set(d(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#w.values()],a={};return t.forEach((t=>{h(e,t.mutationKey)&&Object.assign(a,t.defaultOptions)})),a}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=u(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===E&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#g.clear(),this.#f.clear()}}({defaultOptions:{queries:{retry:2,staleTime:3e5}}}),Be=({page:e})=>(0,s.createElement)(L,{client:Ge},(()=>{switch(e){case"dashboard":default:return(0,s.createElement)(Ke,null);case"settings":return(0,s.createElement)(He,null);case"analytics":return(0,s.createElement)(Le,null);case"ideas":return(0,s.createElement)($e,null)}})(),(0,s.createElement)(Qe,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}));document.addEventListener("DOMContentLoaded",(()=>{const e=document.getElementById("feedlane-admin-dashboard");e&&(0,n.H)(e).render((0,s.createElement)(Be,{page:"dashboard"}));const t=document.getElementById("feedlane-admin-settings");t&&(0,n.H)(t).render((0,s.createElement)(Be,{page:"settings"}));const a=document.getElementById("feedlane-admin-analytics");a&&(0,n.H)(a).render((0,s.createElement)(Be,{page:"analytics"}));const i=document.getElementById("feedlane-admin-ideas");i&&(0,n.H)(i).render((0,s.createElement)(Be,{page:"ideas"}))}))})();