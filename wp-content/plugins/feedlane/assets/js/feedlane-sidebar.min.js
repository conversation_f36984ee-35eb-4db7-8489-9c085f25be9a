(()=>{"use strict";var e={14:(e,t,a)=>{a.d(t,{n:()=>u});var s=a(609),r=a(259),n=a(474),i=a(613),o=a(383),l=class extends i.Q{#e;#t=void 0;#a;#s;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#r()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#a,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():"pending"===this.#a?.state.status&&this.#a.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#a?.removeObserver(this)}onMutationUpdate(e){this.#r(),this.#n(e)}getCurrentResult(){return this.#t}reset(){this.#a?.removeObserver(this),this.#a=void 0,this.#r(),this.#n()}mutate(e,t){return this.#s=t,this.#a?.removeObserver(this),this.#a=this.#e.getMutationCache().build(this.#e,this.options),this.#a.addObserver(this),this.#a.execute(e)}#r(){const e=this.#a?.state??(0,r.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#n(e){n.jG.batch((()=>{if(this.#s&&this.hasListeners()){const t=this.#t.variables,a=this.#t.context;"success"===e?.type?(this.#s.onSuccess?.(e.data,t,a),this.#s.onSettled?.(e.data,null,t,a)):"error"===e?.type&&(this.#s.onError?.(e.error,t,a),this.#s.onSettled?.(void 0,e.error,t,a))}this.listeners.forEach((e=>{e(this.#t)}))}))}},c=a(967);function u(e,t){const a=(0,c.jE)(t),[r]=s.useState((()=>new l(a,e)));s.useEffect((()=>{r.setOptions(e)}),[r,e]);const i=s.useSyncExternalStore(s.useCallback((e=>r.subscribe(n.jG.batchCalls(e))),[r]),(()=>r.getCurrentResult()),(()=>r.getCurrentResult())),u=s.useCallback(((e,t)=>{r.mutate(e,t).catch(o.lQ)}),[r]);if(i.error&&(0,o.GU)(r.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:u,mutateAsync:i.mutate}}},44:(e,t,a)=>{a.d(t,{A:()=>n});const s=window.feedlaneData?.rest_url||"/wp-json/",r=window.feedlaneData?.nonce||"",n=new class{constructor(){this.baseURL=s,this.nonce=r}async request(e,t={}){const a=`${this.baseURL}feedlane/v1${e}`,s={headers:{"Content-Type":"application/json","X-WP-Nonce":this.nonce}},r={...s,...t,headers:{...s.headers,...t.headers}};try{const e=await fetch(a,r);if(!e.ok){const t=await e.json().catch((()=>({})));throw new Error(t.message||`HTTP error! status: ${e.status}`)}return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async get(e,t={}){const a=new URLSearchParams(t).toString(),s=a?`${e}?${a}`:e;return this.request(s,{method:"GET"})}async post(e,t={}){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t={}){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}async postFormData(e,t){return this.request(e,{method:"POST",headers:{"X-WP-Nonce":this.nonce},body:t})}}},69:(e,t,a)=>{a.d(t,{AH:()=>m,I4:()=>g,i7:()=>v,mj:()=>b});let s={data:""},r=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||s,n=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,i=/\/\*[^]*?\*\/|  +/g,o=/\n+/g,l=(e,t)=>{let a="",s="",r="";for(let n in e){let i=e[n];"@"==n[0]?"i"==n[1]?a=n+" "+i+";":s+="f"==n[1]?l(i,n):n+"{"+l(i,"k"==n[1]?"":t)+"}":"object"==typeof i?s+=l(i,t?t.replace(/([^,])+/g,(e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):n):null!=i&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=l.p?l.p(n,i):n+":"+i+";")}return a+(t&&r?t+"{"+r+"}":r)+s},c={},u=e=>{if("object"==typeof e){let t="";for(let a in e)t+=a+u(e[a]);return t}return e},d=(e,t,a,s,r)=>{let d=u(e),h=c[d]||(c[d]=(e=>{let t=0,a=11;for(;t<e.length;)a=101*a+e.charCodeAt(t++)>>>0;return"go"+a})(d));if(!c[h]){let t=d!==e?e:(e=>{let t,a,s=[{}];for(;t=n.exec(e.replace(i,""));)t[4]?s.shift():t[3]?(a=t[3].replace(o," ").trim(),s.unshift(s[0][a]=s[0][a]||{})):s[0][t[1]]=t[2].replace(o," ").trim();return s[0]})(e);c[h]=l(r?{["@keyframes "+h]:t}:t,a?"":"."+h)}let m=a&&c.g?c.g:null;return a&&(c.g=c[h]),((e,t,a,s)=>{s?t.data=t.data.replace(s,e):-1===t.data.indexOf(e)&&(t.data=a?e+t.data:t.data+e)})(c[h],t,s,m),h},h=(e,t,a)=>e.reduce(((e,s,r)=>{let n=t[r];if(n&&n.call){let e=n(a),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":l(e,""):!1===e?"":e}return e+s+(null==n?"":n)}),"");function m(e){let t=this||{},a=e.call?e(t.p):e;return d(a.unshift?a.raw?h(a,[].slice.call(arguments,1),t.p):a.reduce(((e,a)=>Object.assign(e,a&&a.call?a(t.p):a)),{}):a,r(t.target),t.g,t.o,t.k)}m.bind({g:1});let f,p,y,v=m.bind({k:1});function b(e,t,a,s){l.p=t,f=e,p=a,y=s}function g(e,t){let a=this||{};return function(){let s=arguments;function r(n,i){let o=Object.assign({},n),l=o.className||r.className;a.p=Object.assign({theme:p&&p()},o),a.o=/ *go\d+/.test(l),o.className=m.apply(a,s)+(l?" "+l:""),t&&(o.ref=i);let c=e;return e[0]&&(c=o.as||e,delete o.as),y&&c[0]&&y(o),f(c,o)}return t?t(r):r}}},70:(e,t,a)=>{e.exports=a(462)},79:(e,t,a)=>{a.d(t,{m:()=>n});var s=a(613),r=a(383),n=new class extends s.Q{#i;#o;#l;constructor(){super(),this.#l=e=>{if(!r.S$&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#o||this.setEventListener(this.#l)}onUnsubscribe(){this.hasListeners()||(this.#o?.(),this.#o=void 0)}setEventListener(e){this.#l=e,this.#o?.(),this.#o=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#i!==e&&(this.#i=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#i?this.#i:"hidden"!==globalThis.document?.visibilityState}}},140:(e,t,a)=>{a.d(t,{wIk:()=>h,fK4:()=>m,wAb:()=>f,K7R:()=>p,X6_:()=>y,mEP:()=>v,GGD:()=>b,kGk:()=>g,B88:()=>E,yGN:()=>_});var s=a(609),r=a.n(s),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=r().createContext&&r().createContext(n),o=function(){return o=Object.assign||function(e){for(var t,a=1,s=arguments.length;a<s;a++)for(var r in t=arguments[a])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},o.apply(this,arguments)},l=function(e,t){var a={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(a[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(s=Object.getOwnPropertySymbols(e);r<s.length;r++)t.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(e,s[r])&&(a[s[r]]=e[s[r]])}return a};function c(e){return e&&e.map((function(e,t){return r().createElement(e.tag,o({key:t},e.attr),c(e.child))}))}function u(e){return function(t){return r().createElement(d,o({attr:o({},e.attr)},t),c(e.child))}}function d(e){var t=function(t){var a,s=e.attr,n=e.size,i=e.title,c=l(e,["attr","size","title"]),u=n||t.size||"1em";return t.className&&(a=t.className),e.className&&(a=(a?a+" ":"")+e.className),r().createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,c,{className:a,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),i&&r().createElement("title",null,i),e.children)};return void 0!==i?r().createElement(i.Consumer,null,(function(e){return t(e)})):t(n)}function h(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"3",y:"4",width:"18",height:"18",rx:"2",ry:"2"}},{tag:"line",attr:{x1:"16",y1:"2",x2:"16",y2:"6"}},{tag:"line",attr:{x1:"8",y1:"2",x2:"8",y2:"6"}},{tag:"line",attr:{x1:"3",y1:"10",x2:"21",y2:"10"}}]})(e)}function m(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"6 9 12 15 18 9"}}]})(e)}function f(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"18 15 12 9 6 15"}}]})(e)}function p(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polygon",attr:{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"}}]})(e)}function y(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"}}]})(e)}function v(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"}}]})(e)}function b(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"12",y1:"5",x2:"12",y2:"19"}},{tag:"line",attr:{x1:"5",y1:"12",x2:"19",y2:"12"}}]})(e)}function g(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"22",y1:"2",x2:"11",y2:"13"}},{tag:"polygon",attr:{points:"22 2 15 22 11 13 2 9 22 2"}}]})(e)}function E(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}},{tag:"polyline",attr:{points:"17 8 12 3 7 8"}},{tag:"line",attr:{x1:"12",y1:"3",x2:"12",y2:"15"}}]})(e)}function _(e){return u({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"18",y1:"6",x2:"6",y2:"18"}},{tag:"line",attr:{x1:"6",y1:"6",x2:"18",y2:"18"}}]})(e)}},258:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var s=a(609),r=a(967),n=a(470),i=a(14),o=a(140),l=a(326),c=a(44);const u=({postId:e})=>{const[t,a]=(0,s.useState)(!1),[u,d]=(0,s.useState)(!1),[h,m]=(0,s.useState)({feedback_text:"",user_name:"",user_email:""}),f=(0,r.jE)(),p=window.feedlaneData?.is_user_logged_in||!1,y=window.feedlaneData?.settings?.enable_guest_submissions||!1,{data:v,isLoading:b}=(0,n.I)({queryKey:["feedback",e],queryFn:()=>(async(e,t={})=>{const a={page:1,per_page:10,...t};return c.A.get(`/feedback/${e}`,a)})(e),enabled:u,staleTime:12e4}),g=(0,i.n)({mutationFn:t=>(async(e,t)=>c.A.post("/feedback",{post_id:e,...t}))(e,t),onSuccess:()=>{l.Ay.success("Feedback submitted successfully!"),m({feedback_text:"",user_name:"",user_email:""}),a(!1),f.invalidateQueries(["feedback",e])},onError:e=>{const t=e.response?.data?.message||"Failed to submit feedback";l.Ay.error(t)}}),E=e=>{const{name:t,value:a}=e.target;m((e=>({...e,[t]:a})))};return p||y?(0,s.createElement)("div",{className:"feedlane-feedback"},(0,s.createElement)("div",{className:"feedlane-feedback__toggle"},(0,s.createElement)("button",{onClick:()=>a(!t),className:"feedlane-feedback__toggle-button"},(0,s.createElement)(o.X6_,{size:16}),(0,s.createElement)("span",null,"Leave Feedback"),t?(0,s.createElement)(o.wAb,{size:16}):(0,s.createElement)(o.fK4,{size:16})),v&&v.pagination.total>0&&(0,s.createElement)("button",{onClick:()=>d(!u),className:"feedlane-feedback__view-button"},"View ",v.pagination.total," feedback",u?(0,s.createElement)(o.wAb,{size:14}):(0,s.createElement)(o.fK4,{size:14}))),t&&(0,s.createElement)("form",{onSubmit:e=>{if(e.preventDefault(),h.feedback_text.trim()){if(!p&&y){if(!h.user_name.trim()||!h.user_email.trim())return void l.Ay.error("Name and email are required");if(t=h.user_email,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return void l.Ay.error("Please enter a valid email address")}g.mutate(h)}else l.Ay.error("Please enter your feedback");var t},className:"feedlane-feedback__form"},!p&&y&&(0,s.createElement)("div",{className:"feedlane-feedback__guest-fields"},(0,s.createElement)("div",{className:"feedlane-form-row"},(0,s.createElement)("input",{type:"text",name:"user_name",value:h.user_name,onChange:E,placeholder:"Your name",className:"feedlane-input feedlane-input--small",required:!0}),(0,s.createElement)("input",{type:"email",name:"user_email",value:h.user_email,onChange:E,placeholder:"Your email",className:"feedlane-input feedlane-input--small",required:!0}))),(0,s.createElement)("div",{className:"feedlane-feedback__text-field"},(0,s.createElement)("textarea",{name:"feedback_text",value:h.feedback_text,onChange:E,placeholder:"Share your thoughts about this update...",className:"feedlane-textarea",rows:"3",required:!0})),(0,s.createElement)("div",{className:"feedlane-feedback__actions"},(0,s.createElement)("button",{type:"button",onClick:()=>a(!1),className:"feedlane-button feedlane-button--secondary feedlane-button--small"},"Cancel"),(0,s.createElement)("button",{type:"submit",disabled:g.isPending,className:"feedlane-button feedlane-button--primary feedlane-button--small"},g.isPending?(0,s.createElement)(s.Fragment,null,(0,s.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}),"Sending..."):(0,s.createElement)(s.Fragment,null,(0,s.createElement)(o.kGk,{size:14}),"Send Feedback")))),u&&(0,s.createElement)("div",{className:"feedlane-feedback__list"},b?(0,s.createElement)("div",{className:"feedlane-feedback__loading"},(0,s.createElement)("div",{className:"feedlane-spinner"}),(0,s.createElement)("span",null,"Loading feedback...")):v&&v.feedback.length>0?(0,s.createElement)("div",{className:"feedlane-feedback__items"},v.feedback.map((e=>(0,s.createElement)("div",{key:e.id,className:"feedlane-feedback__item"},(0,s.createElement)("div",{className:"feedlane-feedback__item-header"},(0,s.createElement)("span",{className:"feedlane-feedback__author"},e.author.name),(0,s.createElement)("span",{className:"feedlane-feedback__time"},e.time_ago)),(0,s.createElement)("div",{className:"feedlane-feedback__item-content"},e.feedback_text))))):(0,s.createElement)("div",{className:"feedlane-feedback__empty"},(0,s.createElement)("p",null,"No feedback yet. Be the first to share your thoughts!")))):null}},259:(e,t,a)=>{a.d(t,{$:()=>o,s:()=>i});var s=a(474),r=a(515),n=a(483),i=class extends r.k{#c;#u;#d;constructor(e){super(),this.mutationId=e.mutationId,this.#u=e.mutationCache,this.#c=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#c.includes(e)||(this.#c.push(e),this.clearGcTimeout(),this.#u.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#c=this.#c.filter((t=>t!==e)),this.scheduleGc(),this.#u.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#c.length||("pending"===this.state.status?this.scheduleGc():this.#u.remove(this))}continue(){return this.#d?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#h({type:"continue"})};this.#d=(0,n.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#u.canRun(this)});const a="pending"===this.state.status,s=!this.#d.canStart();try{if(a)t();else{this.#h({type:"pending",variables:e,isPaused:s}),await(this.#u.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#h({type:"pending",context:t,variables:e,isPaused:s})}const r=await this.#d.start();return await(this.#u.config.onSuccess?.(r,e,this.state.context,this)),await(this.options.onSuccess?.(r,e,this.state.context)),await(this.#u.config.onSettled?.(r,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(r,null,e,this.state.context)),this.#h({type:"success",data:r}),r}catch(t){try{throw await(this.#u.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#u.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#h({type:"error",error:t})}}finally{this.#u.runNext(this)}}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),s.jG.batch((()=>{this.#c.forEach((t=>{t.onMutationUpdate(e)})),this.#u.notify({mutation:this,type:"updated",action:e})}))}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},326:(e,t,a)=>{a.d(t,{Ay:()=>L,l$:()=>M});var s=a(609),r=a(69),n=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,i=(()=>{let e=0;return()=>(++e).toString()})(),o=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),l=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case 2:let{toast:a}=t;return l(e,{type:e.toasts.find((e=>e.id===a.id))?1:0,toast:a});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map((e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))};case 5:return{...e,pausedAt:t.time};case 6:let r=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+r})))}}},c=[],u={toasts:[],pausedAt:void 0},d=e=>{u=l(u,e),c.forEach((e=>{e(u)}))},h={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},m=e=>(t,a)=>{let s=((e,t="blank",a)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...a,id:(null==a?void 0:a.id)||i()}))(t,e,a);return d({type:2,toast:s}),s.id},f=(e,t)=>m("blank")(e,t);f.error=m("error"),f.success=m("success"),f.loading=m("loading"),f.custom=m("custom"),f.dismiss=e=>{d({type:3,toastId:e})},f.remove=e=>d({type:4,toastId:e}),f.promise=(e,t,a)=>{let s=f.loading(t.loading,{...a,...null==a?void 0:a.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let r=t.success?n(t.success,e):void 0;return r?f.success(r,{id:s,...a,...null==a?void 0:a.success}):f.dismiss(s),e})).catch((e=>{let r=t.error?n(t.error,e):void 0;r?f.error(r,{id:s,...a,...null==a?void 0:a.error}):f.dismiss(s)})),e};var p=(e,t)=>{d({type:1,toast:{id:e,height:t}})},y=()=>{d({type:5,time:Date.now()})},v=new Map,b=r.i7`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,g=r.i7`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,E=r.i7`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,_=(0,r.I4)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${b} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${g} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${E} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,w=r.i7`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,C=(0,r.I4)("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${w} 1s linear infinite;
`,N=r.i7`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,O=r.i7`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,k=(0,r.I4)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${N} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${O} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,x=(0,r.I4)("div")`
  position: absolute;
`,S=(0,r.I4)("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,R=r.i7`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,P=(0,r.I4)("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${R} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,q=({toast:e})=>{let{icon:t,type:a,iconTheme:r}=e;return void 0!==t?"string"==typeof t?s.createElement(P,null,t):t:"blank"===a?null:s.createElement(S,null,s.createElement(C,{...r}),"loading"!==a&&s.createElement(x,null,"error"===a?s.createElement(_,{...r}):s.createElement(k,{...r})))},I=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,Q=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,F=(0,r.I4)("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,D=(0,r.I4)("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,j=s.memo((({toast:e,position:t,style:a,children:i})=>{let l=e.height?((e,t)=>{let a=e.includes("top")?1:-1,[s,n]=o()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[I(a),Q(a)];return{animation:t?`${(0,r.i7)(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${(0,r.i7)(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||t||"top-center",e.visible):{opacity:0},c=s.createElement(q,{toast:e}),u=s.createElement(D,{...e.ariaProps},n(e.message,e));return s.createElement(F,{className:e.className,style:{...l,...a,...e.style}},"function"==typeof i?i({icon:c,message:u}):s.createElement(s.Fragment,null,c,u))}));(0,r.mj)(s.createElement);var T=({id:e,className:t,style:a,onHeightUpdate:r,children:n})=>{let i=s.useCallback((t=>{if(t){let a=()=>{let a=t.getBoundingClientRect().height;r(e,a)};a(),new MutationObserver(a).observe(t,{subtree:!0,childList:!0,characterData:!0})}}),[e,r]);return s.createElement("div",{ref:i,className:t,style:a},n)},A=r.AH`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,M=({reverseOrder:e,position:t="top-center",toastOptions:a,gutter:r,children:i,containerStyle:l,containerClassName:m})=>{let{toasts:b,handlers:g}=(e=>{let{toasts:t,pausedAt:a}=((e={})=>{let[t,a]=(0,s.useState)(u),r=(0,s.useRef)(u);(0,s.useEffect)((()=>(r.current!==u&&a(u),c.push(a),()=>{let e=c.indexOf(a);e>-1&&c.splice(e,1)})),[]);let n=t.toasts.map((t=>{var a,s,r;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(a=e[t.type])?void 0:a.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||h[t.type],style:{...e.style,...null==(r=e[t.type])?void 0:r.style,...t.style}}}));return{...t,toasts:n}})(e);(0,s.useEffect)((()=>{if(a)return;let e=Date.now(),s=t.map((t=>{if(t.duration===1/0)return;let a=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(a<0))return setTimeout((()=>f.dismiss(t.id)),a);t.visible&&f.dismiss(t.id)}));return()=>{s.forEach((e=>e&&clearTimeout(e)))}}),[t,a]);let r=(0,s.useCallback)((()=>{a&&d({type:6,time:Date.now()})}),[a]),n=(0,s.useCallback)(((e,a)=>{let{reverseOrder:s=!1,gutter:r=8,defaultPosition:n}=a||{},i=t.filter((t=>(t.position||n)===(e.position||n)&&t.height)),o=i.findIndex((t=>t.id===e.id)),l=i.filter(((e,t)=>t<o&&e.visible)).length;return i.filter((e=>e.visible)).slice(...s?[l+1]:[0,l]).reduce(((e,t)=>e+(t.height||0)+r),0)}),[t]);return(0,s.useEffect)((()=>{t.forEach((e=>{if(e.dismissed)((e,t=1e3)=>{if(v.has(e))return;let a=setTimeout((()=>{v.delete(e),d({type:4,toastId:e})}),t);v.set(e,a)})(e.id,e.removeDelay);else{let t=v.get(e.id);t&&(clearTimeout(t),v.delete(e.id))}}))}),[t]),{toasts:t,handlers:{updateHeight:p,startPause:y,endPause:r,calculateOffset:n}}})(a);return s.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...l},className:m,onMouseEnter:g.startPause,onMouseLeave:g.endPause},b.map((a=>{let l=a.position||t,c=((e,t)=>{let a=e.includes("top"),s=a?{top:0}:{bottom:0},r=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:o()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(a?1:-1)}px)`,...s,...r}})(l,g.calculateOffset(a,{reverseOrder:e,gutter:r,defaultPosition:t}));return s.createElement(T,{id:a.id,key:a.id,onHeightUpdate:g.updateHeight,className:a.visible?A:"",style:c},"custom"===a.type?n(a.message,a):i?i(a):s.createElement(j,{toast:a,position:l}))})))},L=f},332:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var s=a(609),r=a(14),n=a(140),i=a(326),o=a(559);const l=({onSuccess:e,onCancel:t})=>{const[a,l]=(0,s.useState)({title:"",details:"",category:"feature-request",email:"",first_name:"",last_name:"",image:null}),[c,u]=(0,s.useState)(null),d=window.feedlaneData?.is_user_logged_in||!1,h=(0,r.n)({mutationFn:e=>(0,o.Gn)(e),onSuccess:()=>{i.Ay.success("Idea submitted successfully! It will be reviewed before being published."),e&&e()},onError:e=>{const t=e.response?.data?.message||"Failed to submit idea";i.Ay.error(t)}}),m=e=>{const{name:t,value:a}=e.target;l((e=>({...e,[t]:a})))};return(0,s.createElement)("div",{className:"feedlane-idea-form"},(0,s.createElement)("div",{className:"feedlane-idea-form__header"},(0,s.createElement)("h4",null,"Submit Your Idea"),(0,s.createElement)("button",{onClick:t,className:"feedlane-idea-form__close"},(0,s.createElement)(n.yGN,{size:20}))),(0,s.createElement)("form",{onSubmit:e=>{if(e.preventDefault(),!a.title.trim()||!a.details.trim())return void i.Ay.error("Title and details are required");if(!d){if(!a.first_name.trim()||!a.last_name.trim()||!a.email.trim())return void i.Ay.error("Name and email are required");if(t=a.email,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return void i.Ay.error("Please enter a valid email address")}var t;const s=new FormData;Object.keys(a).forEach((e=>{null!==a[e]&&s.append(e,a[e])})),h.mutate(s)},className:"feedlane-idea-form__form"},!d&&(0,s.createElement)("div",{className:"feedlane-idea-form__guest-fields"},(0,s.createElement)("div",{className:"feedlane-form-row"},(0,s.createElement)("input",{type:"text",name:"first_name",value:a.first_name,onChange:m,placeholder:"First name",className:"feedlane-input",required:!0}),(0,s.createElement)("input",{type:"text",name:"last_name",value:a.last_name,onChange:m,placeholder:"Last name",className:"feedlane-input",required:!0})),(0,s.createElement)("input",{type:"email",name:"email",value:a.email,onChange:m,placeholder:"Email address",className:"feedlane-input",required:!0})),(0,s.createElement)("div",{className:"feedlane-form-field"},(0,s.createElement)("label",{htmlFor:"idea-title",className:"feedlane-label"},"Title *"),(0,s.createElement)("input",{id:"idea-title",type:"text",name:"title",value:a.title,onChange:m,placeholder:"Brief description of your idea",className:"feedlane-input",required:!0})),(0,s.createElement)("div",{className:"feedlane-form-field"},(0,s.createElement)("label",{htmlFor:"idea-category",className:"feedlane-label"},"Category *"),(0,s.createElement)("select",{id:"idea-category",name:"category",value:a.category,onChange:m,className:"feedlane-select",required:!0},[{value:"improvement",label:"Improvement"},{value:"feature-request",label:"Feature Request"},{value:"bug",label:"Bug Report"},{value:"feedback",label:"General Feedback"}].map((e=>(0,s.createElement)("option",{key:e.value,value:e.value},e.label))))),(0,s.createElement)("div",{className:"feedlane-form-field"},(0,s.createElement)("label",{htmlFor:"idea-details",className:"feedlane-label"},"Details *"),(0,s.createElement)("textarea",{id:"idea-details",name:"details",value:a.details,onChange:m,placeholder:"Provide more details about your idea...",className:"feedlane-textarea",rows:"4",required:!0})),(0,s.createElement)("div",{className:"feedlane-form-field"},(0,s.createElement)("label",{className:"feedlane-label"},"Image (optional)"),c?(0,s.createElement)("div",{className:"feedlane-image-preview"},(0,s.createElement)("img",{src:c,alt:"Preview"}),(0,s.createElement)("button",{type:"button",onClick:()=>{l((e=>({...e,image:null}))),u(null)},className:"feedlane-image-preview__remove"},(0,s.createElement)(n.yGN,{size:16}))):(0,s.createElement)("div",{className:"feedlane-file-upload"},(0,s.createElement)("input",{type:"file",id:"idea-image",accept:"image/*",onChange:e=>{const t=e.target.files[0];if(t){if(!t.type.startsWith("image/"))return void i.Ay.error("Please select an image file");if(t.size>5242880)return void i.Ay.error("Image size must be less than 5MB");l((e=>({...e,image:t})));const e=new FileReader;e.onload=e=>{u(e.target.result)},e.readAsDataURL(t)}},className:"feedlane-file-input"}),(0,s.createElement)("label",{htmlFor:"idea-image",className:"feedlane-file-label"},(0,s.createElement)(n.B88,{size:20}),(0,s.createElement)("span",null,"Upload Image"),(0,s.createElement)("small",null,"Max 5MB, JPG/PNG")))),(0,s.createElement)("div",{className:"feedlane-idea-form__actions"},(0,s.createElement)("button",{type:"button",onClick:t,className:"feedlane-button feedlane-button--secondary"},"Cancel"),(0,s.createElement)("button",{type:"submit",disabled:h.isPending,className:"feedlane-button feedlane-button--primary"},h.isPending?(0,s.createElement)(s.Fragment,null,(0,s.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}),"Submitting..."):"Submit Idea"))))}},383:(e,t,a)=>{a.d(t,{Cp:()=>f,EN:()=>m,Eh:()=>c,F$:()=>h,GU:()=>k,MK:()=>u,S$:()=>s,ZM:()=>O,ZZ:()=>C,Zw:()=>n,d2:()=>l,f8:()=>y,gn:()=>i,hT:()=>N,j3:()=>o,lQ:()=>r,nJ:()=>d,pl:()=>_,y9:()=>w,yy:()=>E});var s="undefined"==typeof window||"Deno"in globalThis;function r(){}function n(e,t){return"function"==typeof e?e(t):e}function i(e){return"number"==typeof e&&e>=0&&e!==1/0}function o(e,t){return Math.max(e+(t||0)-Date.now(),0)}function l(e,t){return"function"==typeof e?e(t):e}function c(e,t){return"function"==typeof e?e(t):e}function u(e,t){const{type:a="all",exact:s,fetchStatus:r,predicate:n,queryKey:i,stale:o}=e;if(i)if(s){if(t.queryHash!==h(i,t.options))return!1}else if(!f(t.queryKey,i))return!1;if("all"!==a){const e=t.isActive();if("active"===a&&!e)return!1;if("inactive"===a&&e)return!1}return!("boolean"==typeof o&&t.isStale()!==o||r&&r!==t.state.fetchStatus||n&&!n(t))}function d(e,t){const{exact:a,status:s,predicate:r,mutationKey:n}=e;if(n){if(!t.options.mutationKey)return!1;if(a){if(m(t.options.mutationKey)!==m(n))return!1}else if(!f(t.options.mutationKey,n))return!1}return!(s&&t.state.status!==s||r&&!r(t))}function h(e,t){return(t?.queryKeyHashFn||m)(e)}function m(e){return JSON.stringify(e,((e,t)=>b(t)?Object.keys(t).sort().reduce(((e,a)=>(e[a]=t[a],e)),{}):t))}function f(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&Object.keys(t).every((a=>f(e[a],t[a])))}function p(e,t){if(e===t)return e;const a=v(e)&&v(t);if(a||b(e)&&b(t)){const s=a?e:Object.keys(e),r=s.length,n=a?t:Object.keys(t),i=n.length,o=a?[]:{};let l=0;for(let r=0;r<i;r++){const i=a?r:n[r];(!a&&s.includes(i)||a)&&void 0===e[i]&&void 0===t[i]?(o[i]=void 0,l++):(o[i]=p(e[i],t[i]),o[i]===e[i]&&void 0!==e[i]&&l++)}return r===i&&l===r?e:o}return t}function y(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const a in e)if(e[a]!==t[a])return!1;return!0}function v(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function b(e){if(!g(e))return!1;const t=e.constructor;if(void 0===t)return!0;const a=t.prototype;return!!g(a)&&!!a.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function g(e){return"[object Object]"===Object.prototype.toString.call(e)}function E(e){return new Promise((t=>{setTimeout(t,e)}))}function _(e,t,a){return"function"==typeof a.structuralSharing?a.structuralSharing(e,t):!1!==a.structuralSharing?p(e,t):t}function w(e,t,a=0){const s=[...e,t];return a&&s.length>a?s.slice(1):s}function C(e,t,a=0){const s=[t,...e];return a&&s.length>a?s.slice(0,-1):s}var N=Symbol();function O(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==N?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}function k(e,t){return"function"==typeof e?e(...t):!!e}},443:(e,t,a)=>{function s(){let e,t;const a=new Promise(((a,s)=>{e=a,t=s}));function s(e){Object.assign(a,e),delete a.resolve,delete a.reject}return a.status="pending",a.catch((()=>{})),a.resolve=t=>{s({status:"fulfilled",value:t}),e(t)},a.reject=e=>{s({status:"rejected",reason:e}),t(e)},a}a.d(t,{T:()=>s})},462:(e,t,a)=>{var s=a(609),r=Symbol.for("react.element"),n=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),i=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,a){var s,l={},c=null,u=null;for(s in void 0!==a&&(c=""+a),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)n.call(t,s)&&!o.hasOwnProperty(s)&&(l[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===l[s]&&(l[s]=t[s]);return{$$typeof:r,type:e,key:c,ref:u,props:l,_owner:i.current}}},470:(e,t,a)=>{a.d(t,{I:()=>x});var s=a(79),r=a(474),n=a(702),i=a(613),o=a(443),l=a(383),c=class extends i.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#m=null,this.#f=(0,o.T)(),this.options.experimental_prefetchInRender||this.#f.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#p=void 0;#y=void 0;#t=void 0;#v;#b;#f;#m;#g;#E;#_;#w;#C;#N;#O=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#p.addObserver(this),u(this.#p,this.options)?this.#k():this.updateResult(),this.#x())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return d(this.#p,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return d(this.#p,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#S(),this.#R(),this.#p.removeObserver(this)}setOptions(e){const t=this.options,a=this.#p;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,l.Eh)(this.options.enabled,this.#p))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#P(),this.#p.setOptions(this.options),t._defaulted&&!(0,l.f8)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#p,observer:this});const s=this.hasListeners();s&&h(this.#p,a,this.options,t)&&this.#k(),this.updateResult(),!s||this.#p===a&&(0,l.Eh)(this.options.enabled,this.#p)===(0,l.Eh)(t.enabled,this.#p)&&(0,l.d2)(this.options.staleTime,this.#p)===(0,l.d2)(t.staleTime,this.#p)||this.#q();const r=this.#I();!s||this.#p===a&&(0,l.Eh)(this.options.enabled,this.#p)===(0,l.Eh)(t.enabled,this.#p)&&r===this.#N||this.#Q(r)}getOptimisticResult(e){const t=this.#e.getQueryCache().build(this.#e,e),a=this.createResult(t,e);return s=this,r=a,!(0,l.f8)(s.getCurrentResult(),r)&&(this.#t=a,this.#b=this.options,this.#v=this.#p.state),a;var s,r}getCurrentResult(){return this.#t}trackResult(e,t){return new Proxy(e,{get:(e,a)=>(this.trackProp(a),t?.(a),Reflect.get(e,a))})}trackProp(e){this.#O.add(e)}getCurrentQuery(){return this.#p}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#e.defaultQueryOptions(e),a=this.#e.getQueryCache().build(this.#e,t);return a.fetch().then((()=>this.createResult(a,t)))}fetch(e){return this.#k({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#t)))}#k(e){this.#P();let t=this.#p.fetch(this.options,e);return e?.throwOnError||(t=t.catch(l.lQ)),t}#q(){this.#S();const e=(0,l.d2)(this.options.staleTime,this.#p);if(l.S$||this.#t.isStale||!(0,l.gn)(e))return;const t=(0,l.j3)(this.#t.dataUpdatedAt,e)+1;this.#w=setTimeout((()=>{this.#t.isStale||this.updateResult()}),t)}#I(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#p):this.options.refetchInterval)??!1}#Q(e){this.#R(),this.#N=e,!l.S$&&!1!==(0,l.Eh)(this.options.enabled,this.#p)&&(0,l.gn)(this.#N)&&0!==this.#N&&(this.#C=setInterval((()=>{(this.options.refetchIntervalInBackground||s.m.isFocused())&&this.#k()}),this.#N))}#x(){this.#q(),this.#Q(this.#I())}#S(){this.#w&&(clearTimeout(this.#w),this.#w=void 0)}#R(){this.#C&&(clearInterval(this.#C),this.#C=void 0)}createResult(e,t){const a=this.#p,s=this.options,r=this.#t,i=this.#v,c=this.#b,d=e!==a?e.state:this.#y,{state:f}=e;let p,y={...f},v=!1;if(t._optimisticResults){const r=this.hasListeners(),i=!r&&u(e,t),o=r&&h(e,a,t,s);(i||o)&&(y={...y,...(0,n.k)(f.data,e.options)}),"isRestoring"===t._optimisticResults&&(y.fetchStatus="idle")}let{error:b,errorUpdatedAt:g,status:E}=y;p=y.data;let _=!1;if(void 0!==t.placeholderData&&void 0===p&&"pending"===E){let e;r?.isPlaceholderData&&t.placeholderData===c?.placeholderData?(e=r.data,_=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#_?.state.data,this.#_):t.placeholderData,void 0!==e&&(E="success",p=(0,l.pl)(r?.data,e,t),v=!0)}if(t.select&&void 0!==p&&!_)if(r&&p===i?.data&&t.select===this.#g)p=this.#E;else try{this.#g=t.select,p=t.select(p),p=(0,l.pl)(r?.data,p,t),this.#E=p,this.#m=null}catch(e){this.#m=e}this.#m&&(b=this.#m,p=this.#E,g=Date.now(),E="error");const w="fetching"===y.fetchStatus,C="pending"===E,N="error"===E,O=C&&w,k=void 0!==p,x={status:E,fetchStatus:y.fetchStatus,isPending:C,isSuccess:"success"===E,isError:N,isInitialLoading:O,isLoading:O,data:p,dataUpdatedAt:y.dataUpdatedAt,error:b,errorUpdatedAt:g,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>d.dataUpdateCount||y.errorUpdateCount>d.errorUpdateCount,isFetching:w,isRefetching:w&&!C,isLoadingError:N&&!k,isPaused:"paused"===y.fetchStatus,isPlaceholderData:v,isRefetchError:N&&k,isStale:m(e,t),refetch:this.refetch,promise:this.#f};if(this.options.experimental_prefetchInRender){const t=e=>{"error"===x.status?e.reject(x.error):void 0!==x.data&&e.resolve(x.data)},s=()=>{const e=this.#f=x.promise=(0,o.T)();t(e)},r=this.#f;switch(r.status){case"pending":e.queryHash===a.queryHash&&t(r);break;case"fulfilled":"error"!==x.status&&x.data===r.value||s();break;case"rejected":"error"===x.status&&x.error===r.reason||s()}}return x}updateResult(){const e=this.#t,t=this.createResult(this.#p,this.options);this.#v=this.#p.state,this.#b=this.options,void 0!==this.#v.data&&(this.#_=this.#p),(0,l.f8)(t,e)||(this.#t=t,this.#n({listeners:(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,a="function"==typeof t?t():t;if("all"===a||!a&&!this.#O.size)return!0;const s=new Set(a??this.#O);return this.options.throwOnError&&s.add("error"),Object.keys(this.#t).some((t=>{const a=t;return this.#t[a]!==e[a]&&s.has(a)}))})()}))}#P(){const e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#p)return;const t=this.#p;this.#p=e,this.#y=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#x()}#n(e){r.jG.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#t)})),this.#e.getQueryCache().notify({query:this.#p,type:"observerResultsUpdated"})}))}};function u(e,t){return function(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&d(e,t,t.refetchOnMount)}function d(e,t,a){if(!1!==(0,l.Eh)(t.enabled,e)){const s="function"==typeof a?a(e):a;return"always"===s||!1!==s&&m(e,t)}return!1}function h(e,t,a,s){return(e!==t||!1===(0,l.Eh)(s.enabled,e))&&(!a.suspense||"error"!==e.state.status)&&m(e,a)}function m(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&e.isStaleByTime((0,l.d2)(t.staleTime,e))}var f=a(609),p=a(967);a(70);var y=f.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),v=()=>f.useContext(y),b=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},g=e=>{f.useEffect((()=>{e.clearReset()}),[e])},E=({result:e,errorResetBoundary:t,throwOnError:a,query:s,suspense:r})=>e.isError&&!t.isReset()&&!e.isFetching&&s&&(r&&void 0===e.data||(0,l.GU)(a,[e.error,s])),_=f.createContext(!1),w=()=>f.useContext(_),C=(_.Provider,e=>{const t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))}),N=(e,t)=>e.isLoading&&e.isFetching&&!t,O=(e,t)=>e?.suspense&&t.isPending,k=(e,t,a)=>t.fetchOptimistic(e).catch((()=>{a.clearReset()}));function x(e,t){return function(e,t,a){const s=(0,p.jE)(a),n=w(),i=v(),o=s.defaultQueryOptions(e);s.getDefaultOptions().queries?._experimental_beforeQuery?.(o),o._optimisticResults=n?"isRestoring":"optimistic",C(o),b(o,i),g(i);const c=!s.getQueryCache().get(o.queryHash),[u]=f.useState((()=>new t(s,o))),d=u.getOptimisticResult(o),h=!n&&!1!==e.subscribed;if(f.useSyncExternalStore(f.useCallback((e=>{const t=h?u.subscribe(r.jG.batchCalls(e)):l.lQ;return u.updateResult(),t}),[u,h]),(()=>u.getCurrentResult()),(()=>u.getCurrentResult())),f.useEffect((()=>{u.setOptions(o)}),[o,u]),O(o,d))throw k(o,u,i);if(E({result:d,errorResetBoundary:i,throwOnError:o.throwOnError,query:s.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw d.error;if(s.getDefaultOptions().queries?._experimental_afterQuery?.(o,d),o.experimental_prefetchInRender&&!l.S$&&N(d,n)){const e=c?k(o,u,i):s.getQueryCache().get(o.queryHash)?.promise;e?.catch(l.lQ).finally((()=>{u.updateResult()}))}return o.notifyOnChangeProps?d:u.trackResult(d)}(e,c,t)}},474:(e,t,a)=>{a.d(t,{jG:()=>r});var s=e=>setTimeout(e,0),r=function(){let e=[],t=0,a=e=>{e()},r=e=>{e()},n=s;const i=s=>{t?e.push(s):n((()=>{a(s)}))};return{batch:s=>{let i;t++;try{i=s()}finally{t--,t||(()=>{const t=e;e=[],t.length&&n((()=>{r((()=>{t.forEach((e=>{a(e)}))}))}))})()}return i},batchCalls:e=>(...t)=>{i((()=>{e(...t)}))},schedule:i,setNotifyFunction:e=>{a=e},setBatchNotifyFunction:e=>{r=e},setScheduler:e=>{n=e}}}()},483:(e,t,a)=>{a.d(t,{II:()=>d,v_:()=>l,wm:()=>u});var s=a(79),r=a(860),n=a(443),i=a(383);function o(e){return Math.min(1e3*2**e,3e4)}function l(e){return"online"!==(e??"online")||r.t.isOnline()}var c=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function u(e){return e instanceof c}function d(e){let t,a=!1,u=0,d=!1;const h=(0,n.T)(),m=()=>s.m.isFocused()&&("always"===e.networkMode||r.t.isOnline())&&e.canRun(),f=()=>l(e.networkMode)&&e.canRun(),p=a=>{d||(d=!0,e.onSuccess?.(a),t?.(),h.resolve(a))},y=a=>{d||(d=!0,e.onError?.(a),t?.(),h.reject(a))},v=()=>new Promise((a=>{t=e=>{(d||m())&&a(e)},e.onPause?.()})).then((()=>{t=void 0,d||e.onContinue?.()})),b=()=>{if(d)return;let t;const s=0===u?e.initialPromise:void 0;try{t=s??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(p).catch((t=>{if(d)return;const s=e.retry??(i.S$?0:3),r=e.retryDelay??o,n="function"==typeof r?r(u,t):r,l=!0===s||"number"==typeof s&&u<s||"function"==typeof s&&s(u,t);!a&&l?(u++,e.onFail?.(u,t),(0,i.yy)(n).then((()=>m()?void 0:v())).then((()=>{a?y(t):b()}))):y(t)}))};return{promise:h,cancel:t=>{d||(y(new c(t)),e.abort?.())},continue:()=>(t?.(),h),cancelRetry:()=>{a=!0},continueRetry:()=>{a=!1},canStart:f,start:()=>(f()?b():v().then(b),h)}}},510:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var s=a(609),r=a(967),n=a(14),i=a(140),o=a(326),l=a(559);const c=({ideaId:e,hasVoted:t=!1,voteCount:a=0})=>{const c=(0,r.jE)(),u=(0,n.n)({mutationFn:()=>(0,l.Q2)(e),onSuccess:t=>{c.setQueryData(["ideas"],(a=>a?{...a,ideas:a.ideas.map((a=>a.id===e?{...a,vote_count:t.vote_count,has_voted:!0}:a))}:a)),c.setQueryData(["idea",e],(e=>e?{...e,vote_count:t.vote_count,has_voted:!0}:e)),o.Ay.success("Vote recorded!")},onError:e=>{const t=e.response?.data?.message||"Failed to vote";o.Ay.error(t)}});return(0,s.createElement)("button",{onClick:()=>{t?o.Ay.info("You have already voted on this idea"):u.mutate()},disabled:u.isPending||t,className:"feedlane-vote-button "+(t?"feedlane-vote-button--voted":""),title:t?"You have voted":"Vote for this idea"},u.isPending?(0,s.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}):(0,s.createElement)(i.wAb,{size:16}),(0,s.createElement)("span",{className:"feedlane-vote-button__count"},a))}},515:(e,t,a)=>{a.d(t,{k:()=>r});var s=a(383),r=class{#F;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,s.gn)(this.gcTime)&&(this.#F=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(s.S$?1/0:3e5))}clearGcTimeout(){this.#F&&(clearTimeout(this.#F),this.#F=void 0)}}},559:(e,t,a)=>{a.d(t,{Gn:()=>n,Q2:()=>i,Zu:()=>r});var s=a(44);const r=async(e={})=>{const t={page:1,per_page:10,orderby:"votes",...e};return s.A.get("/ideas",t)},n=async e=>e instanceof FormData?s.A.postFormData("/ideas",e):s.A.post("/ideas",e),i=async e=>s.A.post(`/ideas/${e}/vote`)},576:(e,t,a)=>{var s=a(795);t.H=s.createRoot,s.hydrateRoot},609:e=>{e.exports=window.React},613:(e,t,a)=>{a.d(t,{Q:()=>s});var s=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},702:(e,t,a)=>{a.d(t,{X:()=>o,k:()=>l});var s=a(383),r=a(474),n=a(483),i=a(515),o=class extends i.k{#D;#j;#T;#e;#d;#A;#M;constructor(e){super(),this.#M=!1,this.#A=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#e=e.client,this.#T=this.#e.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#D=function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,a=void 0!==t,s=a?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:a?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:a?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#D,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#d?.promise}setOptions(e){this.options={...this.#A,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#T.remove(this)}setData(e,t){const a=(0,s.pl)(this.state.data,e,this.options);return this.#h({data:a,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),a}setState(e,t){this.#h({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#d?.promise;return this.#d?.cancel(e),t?t.then(s.lQ).catch(s.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#D)}isActive(){return this.observers.some((e=>!1!==(0,s.Eh)(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===s.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,s.j3)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#d?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#d?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#T.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#d&&(this.#M?this.#d.cancel({revert:!0}):this.#d.cancelRetry()),this.scheduleGc()),this.#T.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#h({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#d)return this.#d.continueRetry(),this.#d.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const a=new AbortController,r=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#M=!0,a.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#e,state:this.state,fetchFn:()=>{const e=(0,s.ZM)(this.options,t),a={client:this.#e,queryKey:this.queryKey,meta:this.meta};return r(a),this.#M=!1,this.options.persister?this.options.persister(e,a,this):e(a)}};r(i),this.options.behavior?.onFetch(i,this),this.#j=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===i.fetchOptions?.meta||this.#h({type:"fetch",meta:i.fetchOptions?.meta});const o=e=>{(0,n.wm)(e)&&e.silent||this.#h({type:"error",error:e}),(0,n.wm)(e)||(this.#T.config.onError?.(e,this),this.#T.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#d=(0,n.II)({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:a.abort.bind(a),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void o(e)}this.#T.config.onSuccess?.(e,this),this.#T.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else o(new Error(`${this.queryHash} data is undefined`))},onError:o,onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:()=>{this.#h({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#d.start()}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...l(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const a=e.error;return(0,n.wm)(a)&&a.revert&&this.#j?{...this.#j,fetchStatus:"idle"}:{...t,error:a,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:a,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),r.jG.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#T.notify({query:this,type:"updated",action:e})}))}};function l(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,n.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},795:e=>{e.exports=window.ReactDOM},828:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var s=a(609),r=a(967),n=a(470),i=a(14),o=a(326),l=a(44);const c=({postId:e})=>{const t=(0,r.jE)(),{data:a,isLoading:c,error:u}=(0,n.I)({queryKey:["reactions",e],queryFn:()=>(async e=>l.A.get(`/reactions/${e}`))(e),staleTime:12e4}),d=(0,i.n)({mutationFn:({postId:e,reactionType:t})=>(async(e,t)=>l.A.post("/reactions",{post_id:e,reaction_type:t}))(e,t),onSuccess:a=>{t.setQueryData(["reactions",e],a),o.Ay.success("Reaction added!")},onError:e=>{const t=e.response?.data?.message||"Failed to add reaction";o.Ay.error(t)}}),h=(0,i.n)({mutationFn:({postId:e,reactionType:t})=>(async(e,t)=>l.A.delete(`/reactions/${e}/${t}`))(e,t),onSuccess:a=>{t.setQueryData(["reactions",e],a),o.Ay.success("Reaction removed!")},onError:e=>{const t=e.response?.data?.message||"Failed to remove reaction";o.Ay.error(t)}});if(c)return(0,s.createElement)("div",{className:"feedlane-reactions feedlane-reactions--loading"},(0,s.createElement)("div",{className:"feedlane-reactions__skeleton"},[1,2,3].map((e=>(0,s.createElement)("div",{key:e,className:"feedlane-reaction-button feedlane-reaction-button--skeleton"},(0,s.createElement)("div",{className:"feedlane-reaction-button__emoji"}),(0,s.createElement)("div",{className:"feedlane-reaction-button__count"}))))));if(u||!a)return null;const{reaction_counts:m={},user_reaction:f,available_reactions:p={}}=a;return(0,s.createElement)("div",{className:"feedlane-reactions"},(0,s.createElement)("div",{className:"feedlane-reactions__buttons"},Object.entries(p).map((([t,r])=>{const n=m[t]||0,i=f===t,o=d.isPending||h.isPending;return(0,s.createElement)("button",{key:t,onClick:()=>(t=>{a&&(a.user_reaction===t?h.mutate({postId:e,reactionType:t}):d.mutate({postId:e,reactionType:t}))})(t),disabled:o,className:"feedlane-reaction-button "+(i?"feedlane-reaction-button--active":""),title:`React with ${t}`},(0,s.createElement)("span",{className:"feedlane-reaction-button__emoji"},r),n>0&&(0,s.createElement)("span",{className:"feedlane-reaction-button__count"},n))}))),Object.values(m).some((e=>e>0))&&(0,s.createElement)("div",{className:"feedlane-reactions__summary"},Object.entries(m).filter((([e,t])=>t>0)).map((([e,t])=>(0,s.createElement)("span",{key:e,className:"feedlane-reactions__summary-item"},p[e]," ",t)))))}},860:(e,t,a)=>{a.d(t,{t:()=>n});var s=a(613),r=a(383),n=new class extends s.Q{#L=!0;#o;#l;constructor(){super(),this.#l=e=>{if(!r.S$&&window.addEventListener){const t=()=>e(!0),a=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",a,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",a)}}}}onSubscribe(){this.#o||this.setEventListener(this.#l)}onUnsubscribe(){this.hasListeners()||(this.#o?.(),this.#o=void 0)}setEventListener(e){this.#l=e,this.#o?.(),this.#o=e(this.setOnline.bind(this))}setOnline(e){this.#L!==e&&(this.#L=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#L}}},967:(e,t,a)=>{a.d(t,{Ht:()=>o,jE:()=>i});var s=a(609),r=a(70),n=s.createContext(void 0),i=e=>{const t=s.useContext(n);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},o=({client:e,children:t})=>(s.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,r.jsx)(n.Provider,{value:e,children:t}))}},t={};function a(s){var r=t[s];if(void 0!==r)return r.exports;var n=t[s]={exports:{}};return e[s](n,n.exports,a),n.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var s in t)a.o(t,s)&&!a.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s=a(609),r=a(576),n=a(383),i=a(702),o=a(474),l=a(613),c=class extends l.Q{constructor(e={}){super(),this.config=e,this.#U=new Map}#U;build(e,t,a){const s=t.queryKey,r=t.queryHash??(0,n.F$)(s,t);let o=this.get(r);return o||(o=new i.X({client:e,queryKey:s,queryHash:r,options:e.defaultQueryOptions(t),state:a,defaultOptions:e.getQueryDefaults(s)}),this.add(o)),o}add(e){this.#U.has(e.queryHash)||(this.#U.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#U.get(e.queryHash);t&&(e.destroy(),t===e&&this.#U.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){o.jG.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#U.get(e)}getAll(){return[...this.#U.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,n.MK)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,n.MK)(e,t))):t}notify(e){o.jG.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){o.jG.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){o.jG.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}},u=a(259),d=class extends l.Q{constructor(e={}){super(),this.config=e,this.#$=new Set,this.#G=new Map,this.#K=0}#$;#G;#K;build(e,t,a){const s=new u.s({mutationCache:this,mutationId:++this.#K,options:e.defaultMutationOptions(t),state:a});return this.add(s),s}add(e){this.#$.add(e);const t=h(e);if("string"==typeof t){const a=this.#G.get(t);a?a.push(e):this.#G.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#$.delete(e)){const t=h(e);if("string"==typeof t){const a=this.#G.get(t);if(a)if(a.length>1){const t=a.indexOf(e);-1!==t&&a.splice(t,1)}else a[0]===e&&this.#G.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=h(e);if("string"==typeof t){const a=this.#G.get(t),s=a?.find((e=>"pending"===e.state.status));return!s||s===e}return!0}runNext(e){const t=h(e);if("string"==typeof t){const a=this.#G.get(t)?.find((t=>t!==e&&t.state.isPaused));return a?.continue()??Promise.resolve()}return Promise.resolve()}clear(){o.jG.batch((()=>{this.#$.forEach((e=>{this.notify({type:"removed",mutation:e})})),this.#$.clear(),this.#G.clear()}))}getAll(){return Array.from(this.#$)}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,n.nJ)(t,e)))}findAll(e={}){return this.getAll().filter((t=>(0,n.nJ)(e,t)))}notify(e){o.jG.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return o.jG.batch((()=>Promise.all(e.map((e=>e.continue().catch(n.lQ))))))}};function h(e){return e.options.scope?.id}var m=a(79),f=a(860);function p(e){return{onFetch:(t,a)=>{const s=t.options,r=t.fetchOptions?.meta?.fetchMore?.direction,i=t.state.data?.pages||[],o=t.state.data?.pageParams||[];let l={pages:[],pageParams:[]},c=0;const u=async()=>{let a=!1;const u=(0,n.ZM)(t.options,t.fetchOptions),d=async(e,s,r)=>{if(a)return Promise.reject();if(null==s&&e.pages.length)return Promise.resolve(e);const i={client:t.client,queryKey:t.queryKey,pageParam:s,direction:r?"backward":"forward",meta:t.options.meta};var o;o=i,Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(t.signal.aborted?a=!0:t.signal.addEventListener("abort",(()=>{a=!0})),t.signal)});const l=await u(i),{maxPages:c}=t.options,d=r?n.ZZ:n.y9;return{pages:d(e.pages,l,c),pageParams:d(e.pageParams,s,c)}};if(r&&i.length){const e="backward"===r,t={pages:i,pageParams:o},a=(e?v:y)(s,t);l=await d(t,a,e)}else{const t=e??i.length;do{const e=0===c?o[0]??s.initialPageParam:y(s,l);if(c>0&&null==e)break;l=await d(l,e),c++}while(c<t)}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},a):t.fetchFn=u}}}function y(e,{pages:t,pageParams:a}){const s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,a[s],a):void 0}function v(e,{pages:t,pageParams:a}){return t.length>0?e.getPreviousPageParam?.(t[0],t,a[0],a):void 0}var b=a(967),g=a(326),E=a(140),_=a(470),w=a(828),C=a(258);a(44);const N=async e=>{try{const t=await fetch(`${window.feedlaneData.rest_url}wp/v2/media/${e}`,{headers:{"X-WP-Nonce":window.feedlaneData.nonce}});if(t.ok)return(await t.json()).source_url}catch(e){console.error("Failed to fetch media:",e)}return null},O=({post:e})=>{return(0,s.createElement)("article",{className:"feedlane-post"},(0,s.createElement)("header",{className:"feedlane-post__header"},(0,s.createElement)("h4",{className:"feedlane-post__title"},e.title),(0,s.createElement)("div",{className:"feedlane-post__meta"},(0,s.createElement)("span",{className:"feedlane-post__date"},(0,s.createElement)(E.wIk,{size:14}),(t=e.date,new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}))))),e.featured_image&&(0,s.createElement)("div",{className:"feedlane-post__image"},(0,s.createElement)("img",{src:e.featured_image,alt:e.title,loading:"lazy"})),(0,s.createElement)("div",{className:"feedlane-post__content"},(0,s.createElement)("div",{className:"feedlane-post__excerpt",dangerouslySetInnerHTML:{__html:((e,t=200)=>e.length<=t?e:e.substring(0,t)+"...")(e.excerpt||e.content)}}),(e.content.length>200||e.excerpt)&&(0,s.createElement)("button",{className:"feedlane-post__read-more"},"Read more")),(0,s.createElement)("footer",{className:"feedlane-post__footer"},(0,s.createElement)("div",{className:"feedlane-post__reactions"},(0,s.createElement)(w.default,{postId:e.id})),(0,s.createElement)("div",{className:"feedlane-post__feedback"},(0,s.createElement)(C.default,{postId:e.id}))));var t},k=()=>{const{data:e,isLoading:t,error:a,refetch:r}=(0,_.I)({queryKey:["newsfeed-posts"],queryFn:()=>(async(e={})=>{const t={page:1,per_page:10,...e},a=await fetch(`${window.feedlaneData.rest_url}wp/v2/feedlane_posts?${new URLSearchParams(t)}`,{headers:{"X-WP-Nonce":window.feedlaneData.nonce}});if(!a.ok)throw new Error("Failed to fetch newsfeed posts");return(await a.json()).map((e=>({id:e.id,title:e.title.rendered,content:e.content.rendered,excerpt:e.excerpt.rendered,date:e.date,featured_image:e.featured_media?N(e.featured_media):null,author:e.author,link:e.link})))})({per_page:10}),staleTime:3e5});return t?(0,s.createElement)("div",{className:"feedlane-loading"},(0,s.createElement)("div",{className:"feedlane-loading__spinner"}),(0,s.createElement)("p",null,"Loading newsfeed...")):a?(0,s.createElement)("div",{className:"feedlane-error"},(0,s.createElement)("p",null,"Failed to load newsfeed posts."),(0,s.createElement)("button",{onClick:()=>r(),className:"feedlane-button feedlane-button--secondary"},"Try Again")):e&&0!==e.length?(0,s.createElement)("div",{className:"feedlane-newsfeed"},(0,s.createElement)("div",{className:"feedlane-newsfeed__header"},(0,s.createElement)("h3",null,"Latest Updates"),(0,s.createElement)("p",null,"Stay informed about our latest news and announcements")),(0,s.createElement)("div",{className:"feedlane-newsfeed__posts"},e.map((e=>(0,s.createElement)(O,{key:e.id,post:e}))))):(0,s.createElement)("div",{className:"feedlane-empty"},(0,s.createElement)("div",{className:"feedlane-empty__icon"},"📢"),(0,s.createElement)("h3",null,"No News Yet"),(0,s.createElement)("p",null,"Check back later for updates and announcements."))};var x=a(332),S=a(510),R=a(559);const P=({idea:e})=>{return(0,s.createElement)("article",{className:"feedlane-idea-card"},(0,s.createElement)("header",{className:"feedlane-idea-card__header"},(0,s.createElement)("h4",{className:"feedlane-idea-card__title"},e.title),(0,s.createElement)("div",{className:"feedlane-idea-card__meta"},(0,s.createElement)("span",{className:"feedlane-idea-card__date"},(t=e.created_at,new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}))),e.categories.length>0&&(0,s.createElement)("span",{className:"feedlane-idea-card__category"},e.categories[0].name),e.status&&(0,s.createElement)("span",{className:`feedlane-idea-card__status feedlane-status--${e.status.slug}`},e.status.name))),(0,s.createElement)("div",{className:"feedlane-idea-card__content"},(0,s.createElement)("p",{className:"feedlane-idea-card__excerpt"},e.excerpt),e.image&&(0,s.createElement)("div",{className:"feedlane-idea-card__image"},(0,s.createElement)("img",{src:e.image,alt:e.title,loading:"lazy"}))),(0,s.createElement)("footer",{className:"feedlane-idea-card__footer"},(0,s.createElement)("div",{className:"feedlane-idea-card__voting"},(0,s.createElement)(S.default,{ideaId:e.id}),(0,s.createElement)("span",{className:"feedlane-idea-card__vote-count"},e.vote_count," ",1===e.vote_count?"vote":"votes")),(0,s.createElement)("button",{className:"feedlane-idea-card__view-details"},"View Details")));var t},q=()=>{const[e,t]=(0,s.useState)(!1),[a,r]=(0,s.useState)({category:"",orderby:"votes"}),{data:n,isLoading:i,error:o,refetch:l}=(0,_.I)({queryKey:["ideas",a],queryFn:()=>(0,R.Zu)({...a,per_page:20}),staleTime:18e4});if(i)return(0,s.createElement)("div",{className:"feedlane-loading"},(0,s.createElement)("div",{className:"feedlane-loading__spinner"}),(0,s.createElement)("p",null,"Loading ideas..."));if(o)return(0,s.createElement)("div",{className:"feedlane-error"},(0,s.createElement)("p",null,"Failed to load ideas."),(0,s.createElement)("button",{onClick:()=>l(),className:"feedlane-button feedlane-button--secondary"},"Try Again"));const c=n?.ideas||[];return(0,s.createElement)("div",{className:"feedlane-ideas"},(0,s.createElement)("div",{className:"feedlane-ideas__header"},(0,s.createElement)("h3",null,"Ideas & Suggestions"),(0,s.createElement)("p",null,"Share your ideas and vote on others"),(0,s.createElement)("button",{onClick:()=>t(!e),className:"feedlane-button feedlane-button--primary"},(0,s.createElement)(E.GGD,{size:16}),"Submit Idea")),e&&(0,s.createElement)("div",{className:"feedlane-ideas__form"},(0,s.createElement)(x.default,{onSuccess:()=>{t(!1),l()},onCancel:()=>t(!1)})),(0,s.createElement)("div",{className:"feedlane-ideas__filters"},(0,s.createElement)("div",{className:"feedlane-filter-group"},(0,s.createElement)(E.K7R,{size:16}),(0,s.createElement)("select",{value:a.orderby,onChange:e=>r((t=>({...t,orderby:e.target.value}))),className:"feedlane-select"},(0,s.createElement)("option",{value:"votes"},"Most Voted"),(0,s.createElement)("option",{value:"date"},"Newest"),(0,s.createElement)("option",{value:"title"},"Alphabetical")))),(0,s.createElement)("div",{className:"feedlane-ideas__list"},0===c.length?(0,s.createElement)("div",{className:"feedlane-empty"},(0,s.createElement)("div",{className:"feedlane-empty__icon"},"💡"),(0,s.createElement)("h3",null,"No Ideas Yet"),(0,s.createElement)("p",null,"Be the first to share your brilliant idea!")):c.map((e=>(0,s.createElement)(P,{key:e.id,idea:e})))))},I=({status:e,isExpanded:t,onToggle:a})=>{const{data:r,isLoading:n,error:i}=(0,_.I)({queryKey:["roadmap-ideas",e.slug],queryFn:()=>(0,R.Zu)({status:e.slug,per_page:50,orderby:"votes"}),staleTime:3e5}),o=r?.ideas||[],l=r?.pagination?.total||0;return(0,s.createElement)("div",{className:`feedlane-roadmap-section feedlane-roadmap-section--${e.color}`},(0,s.createElement)("button",{onClick:a,className:"feedlane-roadmap-section__header"},(0,s.createElement)("div",{className:"feedlane-roadmap-section__title"},(0,s.createElement)("span",{className:"feedlane-roadmap-section__icon"},e.icon),(0,s.createElement)("span",{className:"feedlane-roadmap-section__name"},e.name),(0,s.createElement)("span",{className:"feedlane-roadmap-section__count"},"(",l,")")),(0,s.createElement)("div",{className:"feedlane-roadmap-section__toggle"},t?(0,s.createElement)(E.wAb,{size:20}):(0,s.createElement)(E.fK4,{size:20}))),t&&(0,s.createElement)("div",{className:"feedlane-roadmap-section__content"},n?(0,s.createElement)("div",{className:"feedlane-roadmap-section__loading"},(0,s.createElement)("div",{className:"feedlane-spinner"}),(0,s.createElement)("span",null,"Loading ",e.name.toLowerCase(),"...")):i?(0,s.createElement)("div",{className:"feedlane-roadmap-section__error"},(0,s.createElement)("p",null,"Failed to load items")):0===o.length?(0,s.createElement)("div",{className:"feedlane-roadmap-section__empty"},(0,s.createElement)("p",null,"No items in ",e.name.toLowerCase())):(0,s.createElement)("div",{className:"feedlane-roadmap-section__items"},o.map((e=>(0,s.createElement)(Q,{key:e.id,idea:e}))))))},Q=({idea:e})=>{return(0,s.createElement)("article",{className:"feedlane-roadmap-item"},(0,s.createElement)("div",{className:"feedlane-roadmap-item__content"},(0,s.createElement)("h4",{className:"feedlane-roadmap-item__title"},e.title),(0,s.createElement)("p",{className:"feedlane-roadmap-item__excerpt"},e.excerpt),(0,s.createElement)("div",{className:"feedlane-roadmap-item__meta"},(0,s.createElement)("span",{className:"feedlane-roadmap-item__date"},(t=e.created_at,new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}))),e.categories.length>0&&(0,s.createElement)("span",{className:"feedlane-roadmap-item__category"},e.categories[0].name))),(0,s.createElement)("div",{className:"feedlane-roadmap-item__actions"},(0,s.createElement)(S.default,{ideaId:e.id,hasVoted:e.has_voted,voteCount:e.vote_count})));var t},F=()=>{const[e,t]=(0,s.useState)({"under-review":!0,planned:!0,"in-progress":!0,completed:!1});return(0,s.createElement)("div",{className:"feedlane-roadmap"},(0,s.createElement)("div",{className:"feedlane-roadmap__header"},(0,s.createElement)("h3",null,"Product Roadmap"),(0,s.createElement)("p",null,"Track the progress of ideas and features")),(0,s.createElement)("div",{className:"feedlane-roadmap__sections"},[{slug:"under-review",name:"Under Review",icon:"🔍",color:"yellow"},{slug:"planned",name:"Planned",icon:"📋",color:"blue"},{slug:"in-progress",name:"In Progress",icon:"⚡",color:"orange"},{slug:"completed",name:"Completed",icon:"✅",color:"green"}].map((a=>(0,s.createElement)(I,{key:a.slug,status:a,isExpanded:e[a.slug],onToggle:()=>(e=>{t((t=>({...t,[e]:!t[e]})))})(a.slug)})))))},D=()=>{const[e,t]=(0,s.useState)(!1),[a,r]=(0,s.useState)("newsfeed"),[n,i]=(0,s.useState)({});(0,s.useEffect)((()=>{if(window.feedlaneData&&window.feedlaneData.settings){i(window.feedlaneData.settings);const{enable_newsfeed:e,enable_ideas:t,enable_roadmap:a}=window.feedlaneData.settings;e?r("newsfeed"):t?r("ideas"):a&&r("roadmap")}}),[]);const o=()=>{t(!1)},l=(()=>{const e=[];return n.enable_newsfeed&&e.push({id:"newsfeed",label:"Newsfeed",icon:"📢",component:k}),n.enable_ideas&&e.push({id:"ideas",label:"Ideas",icon:"💡",component:q}),n.enable_roadmap&&e.push({id:"roadmap",label:"Roadmap",icon:"🚧",component:F}),e})();if(0===l.length)return null;const c=n.sidebar_position||"left",u=n.primary_color||"#0ea5e9",d=l.find((e=>e.id===a))?.component||k;return(0,s.createElement)(s.Fragment,null,(0,s.createElement)("button",{onClick:()=>{t(!e)},className:`feedlane-floating-button feedlane-floating-button--${c}`,style:{backgroundColor:u},"aria-label":"Open Feedlane"},(0,s.createElement)(E.mEP,{size:24})),e&&(0,s.createElement)("div",{className:"feedlane-overlay",onClick:o}),(0,s.createElement)("div",{className:`feedlane-sidebar feedlane-sidebar--${c} ${e?"feedlane-sidebar--open":""}`,style:{"--feedlane-primary-color":u}},(0,s.createElement)("div",{className:"feedlane-sidebar__header"},(0,s.createElement)("h2",{className:"feedlane-sidebar__title"},"Feedlane"),(0,s.createElement)("button",{onClick:o,className:"feedlane-sidebar__close","aria-label":"Close sidebar"},(0,s.createElement)(E.yGN,{size:20}))),l.length>1&&(0,s.createElement)("div",{className:"feedlane-sidebar__tabs"},l.map((e=>(0,s.createElement)("button",{key:e.id,onClick:()=>r(e.id),className:"feedlane-sidebar__tab "+(a===e.id?"feedlane-sidebar__tab--active":"")},(0,s.createElement)("span",{className:"feedlane-sidebar__tab-icon"},e.icon),(0,s.createElement)("span",{className:"feedlane-sidebar__tab-label"},e.label))))),(0,s.createElement)("div",{className:"feedlane-sidebar__content"},(0,s.createElement)(d,null))))},j=new class{#H;#u;#A;#z;#B;#W;#Z;#Y;constructor(e={}){this.#H=e.queryCache||new c,this.#u=e.mutationCache||new d,this.#A=e.defaultOptions||{},this.#z=new Map,this.#B=new Map,this.#W=0}mount(){this.#W++,1===this.#W&&(this.#Z=m.m.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#H.onFocus())})),this.#Y=f.t.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#H.onOnline())})))}unmount(){this.#W--,0===this.#W&&(this.#Z?.(),this.#Z=void 0,this.#Y?.(),this.#Y=void 0)}isFetching(e){return this.#H.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#u.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#H.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),a=this.#H.build(this,t),s=a.state.data;return void 0===s?this.fetchQuery(e):(e.revalidateIfStale&&a.isStaleByTime((0,n.d2)(t.staleTime,a))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return this.#H.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,a){const s=this.defaultQueryOptions({queryKey:e}),r=this.#H.get(s.queryHash),i=r?.state.data,o=(0,n.Zw)(t,i);if(void 0!==o)return this.#H.build(this,s).setData(o,{...a,manual:!0})}setQueriesData(e,t,a){return o.jG.batch((()=>this.#H.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,a)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#H.get(t.queryHash)?.state}removeQueries(e){const t=this.#H;o.jG.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const a=this.#H;return o.jG.batch((()=>(a.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries({type:"active",...e},t))))}cancelQueries(e,t={}){const a={revert:!0,...t},s=o.jG.batch((()=>this.#H.findAll(e).map((e=>e.cancel(a)))));return Promise.all(s).then(n.lQ).catch(n.lQ)}invalidateQueries(e,t={}){return o.jG.batch((()=>(this.#H.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))))}refetchQueries(e,t={}){const a={...t,cancelRefetch:t.cancelRefetch??!0},s=o.jG.batch((()=>this.#H.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,a);return a.throwOnError||(t=t.catch(n.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(s).then(n.lQ)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const a=this.#H.build(this,t);return a.isStaleByTime((0,n.d2)(t.staleTime,a))?a.fetch(t):Promise.resolve(a.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n.lQ).catch(n.lQ)}fetchInfiniteQuery(e){return e.behavior=p(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n.lQ).catch(n.lQ)}ensureInfiniteQueryData(e){return e.behavior=p(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return f.t.isOnline()?this.#u.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#H}getMutationCache(){return this.#u}getDefaultOptions(){return this.#A}setDefaultOptions(e){this.#A=e}setQueryDefaults(e,t){this.#z.set((0,n.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#z.values()],a={};return t.forEach((t=>{(0,n.Cp)(e,t.queryKey)&&Object.assign(a,t.defaultOptions)})),a}setMutationDefaults(e,t){this.#B.set((0,n.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#B.values()],a={};return t.forEach((t=>{(0,n.Cp)(e,t.mutationKey)&&Object.assign(a,t.defaultOptions)})),a}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#A.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,n.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===n.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#A.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#H.clear(),this.#u.clear()}}({defaultOptions:{queries:{retry:2,staleTime:3e5}}}),T=()=>(0,s.createElement)(b.Ht,{client:j},(0,s.createElement)(D,null),(0,s.createElement)(g.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}));document.addEventListener("DOMContentLoaded",(()=>{const e=document.querySelectorAll(".feedlane-sidebar-container");if(e.length>0)e.forEach((e=>{(0,r.H)(e).render((0,s.createElement)(T,null))}));else{const e=document.createElement("div");e.id="feedlane-floating-sidebar",e.className="feedlane-floating-sidebar",document.body.appendChild(e),(0,r.H)(e).render((0,s.createElement)(T,null))}document.querySelectorAll(".feedlane-reactions").forEach((e=>{const t=e.dataset.postId;t&&Promise.resolve().then(a.bind(a,828)).then((({default:a})=>{(0,r.H)(e).render((0,s.createElement)(b.Ht,{client:j},(0,s.createElement)(a,{postId:parseInt(t)})))}))})),document.querySelectorAll(".feedlane-feedback-form").forEach((e=>{const t=e.dataset.postId;t&&Promise.resolve().then(a.bind(a,258)).then((({default:a})=>{(0,r.H)(e).render((0,s.createElement)(b.Ht,{client:j},(0,s.createElement)(a,{postId:parseInt(t)})))}))})),document.querySelectorAll(".feedlane-idea-form").forEach((e=>{Promise.resolve().then(a.bind(a,332)).then((({default:t})=>{(0,r.H)(e).render((0,s.createElement)(b.Ht,{client:j},(0,s.createElement)(t,null)))}))})),document.querySelectorAll(".feedlane-idea-actions, .feedlane-item-actions").forEach((e=>{const t=e.dataset.ideaId;t&&Promise.resolve().then(a.bind(a,510)).then((({default:a})=>{(0,r.H)(e).render((0,s.createElement)(b.Ht,{client:j},(0,s.createElement)(a,{ideaId:parseInt(t)})))}))}))}))})();