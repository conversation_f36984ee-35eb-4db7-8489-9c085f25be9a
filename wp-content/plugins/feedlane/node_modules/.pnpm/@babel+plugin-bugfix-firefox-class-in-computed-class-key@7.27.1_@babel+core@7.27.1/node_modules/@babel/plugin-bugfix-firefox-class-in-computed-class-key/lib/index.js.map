{"version": 3, "names": ["_traverse", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_default", "exports", "default", "declare", "types", "t", "assertVersion", "containsClassExpressionVisitor", "ClassExpression", "path", "state", "found", "stop", "Function", "skip", "containsYieldOrAwaitVisitor", "visitors", "environmentVisitor", "YieldExpression", "yield", "await", "AwaitExpression", "containsClassExpression", "isClassExpression", "node", "isFunction", "traverse", "wrap", "context", "isYieldExpression", "isAwaitExpression", "replacement", "fn", "functionExpression", "blockStatement", "returnStatement", "yieldExpression", "callExpression", "memberExpression", "identifier", "thisExpression", "arrowFunctionExpression", "awaitExpression", "replaceWith", "name", "visitor", "Class", "hasPrivateElement", "body", "some", "isPrivate", "elem", "get", "computed"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { types as t, NodePath, Visitor } from \"@babel/core\";\nimport { visitors } from \"@babel/traverse\";\nimport { declare } from \"@babel/helper-plugin-utils\";\n\nexport default declare(({ types: t, assertVersion }) => {\n  assertVersion(REQUIRED_VERSION(7));\n\n  const containsClassExpressionVisitor: Visitor<{ found: boolean }> = {\n    ClassExpression(path, state) {\n      state.found = true;\n      path.stop();\n    },\n    Function(path) {\n      path.skip();\n    },\n  };\n\n  const containsYieldOrAwaitVisitor = visitors.environmentVisitor<{\n    yield: boolean;\n    await: boolean;\n  }>({\n    YieldExpression(path, state) {\n      state.yield = true;\n      if (state.await) path.stop();\n    },\n    AwaitExpression(path, state) {\n      state.await = true;\n      if (state.yield) path.stop();\n    },\n  });\n\n  function containsClassExpression(path: NodePath<t.Node>) {\n    if (t.isClassExpression(path.node)) return true;\n    if (t.isFunction(path.node)) return false;\n    const state = { found: false };\n    path.traverse(containsClassExpressionVisitor, state);\n    return state.found;\n  }\n\n  function wrap(path: NodePath<t.Expression>) {\n    const context = {\n      yield: t.isYieldExpression(path.node),\n      await: t.isAwaitExpression(path.node),\n    };\n    path.traverse(containsYieldOrAwaitVisitor, context);\n\n    let replacement;\n\n    if (context.yield) {\n      const fn = t.functionExpression(\n        null,\n        [],\n        t.blockStatement([t.returnStatement(path.node)]),\n        /* generator */ true,\n        /* async */ context.await,\n      );\n\n      replacement = t.yieldExpression(\n        t.callExpression(t.memberExpression(fn, t.identifier(\"call\")), [\n          t.thisExpression(),\n          // NOTE: In some context arguments is invalid (it might not be defined\n          // in the top-level scope, or it's a syntax error in static class blocks).\n          // However, `yield` is also invalid in those contexts, so we can safely\n          // inject a reference to arguments.\n          t.identifier(\"arguments\"),\n        ]),\n        true,\n      );\n    } else {\n      const fn = t.arrowFunctionExpression([], path.node, context.await);\n\n      replacement = t.callExpression(fn, []);\n      if (context.await) replacement = t.awaitExpression(replacement);\n    }\n\n    path.replaceWith(replacement);\n  }\n\n  return {\n    name: \"bugfix-firefox-class-in-computed-class-key\",\n\n    visitor: {\n      Class(path) {\n        const hasPrivateElement = path.node.body.body.some(node =>\n          t.isPrivate(node),\n        );\n        if (!hasPrivateElement) return;\n\n        for (const elem of path.get(\"body.body\")) {\n          if (\n            \"computed\" in elem.node &&\n            elem.node.computed &&\n            containsClassExpression(elem.get(\"key\"))\n          ) {\n            wrap(\n              // @ts-expect-error .key also includes t.PrivateName\n              elem.get(\"key\") satisfies NodePath<t.Expression>,\n            );\n          }\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAAqD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEtC,IAAAC,0BAAO,EAAC,CAAC;EAAEC,KAAK,EAAEC,CAAC;EAAEC;AAAc,CAAC,KAAK;EACtDA,aAAa,CAAkB,CAAE,CAAC;EAElC,MAAMC,8BAA2D,GAAG;IAClEC,eAAeA,CAACC,IAAI,EAAEC,KAAK,EAAE;MAC3BA,KAAK,CAACC,KAAK,GAAG,IAAI;MAClBF,IAAI,CAACG,IAAI,CAAC,CAAC;IACb,CAAC;IACDC,QAAQA,CAACJ,IAAI,EAAE;MACbA,IAAI,CAACK,IAAI,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMC,2BAA2B,GAAGC,kBAAQ,CAACC,kBAAkB,CAG5D;IACDC,eAAeA,CAACT,IAAI,EAAEC,KAAK,EAAE;MAC3BA,KAAK,CAACS,KAAK,GAAG,IAAI;MAClB,IAAIT,KAAK,CAACU,KAAK,EAAEX,IAAI,CAACG,IAAI,CAAC,CAAC;IAC9B,CAAC;IACDS,eAAeA,CAACZ,IAAI,EAAEC,KAAK,EAAE;MAC3BA,KAAK,CAACU,KAAK,GAAG,IAAI;MAClB,IAAIV,KAAK,CAACS,KAAK,EAAEV,IAAI,CAACG,IAAI,CAAC,CAAC;IAC9B;EACF,CAAC,CAAC;EAEF,SAASU,uBAAuBA,CAACb,IAAsB,EAAE;IACvD,IAAIJ,CAAC,CAACkB,iBAAiB,CAACd,IAAI,CAACe,IAAI,CAAC,EAAE,OAAO,IAAI;IAC/C,IAAInB,CAAC,CAACoB,UAAU,CAAChB,IAAI,CAACe,IAAI,CAAC,EAAE,OAAO,KAAK;IACzC,MAAMd,KAAK,GAAG;MAAEC,KAAK,EAAE;IAAM,CAAC;IAC9BF,IAAI,CAACiB,QAAQ,CAACnB,8BAA8B,EAAEG,KAAK,CAAC;IACpD,OAAOA,KAAK,CAACC,KAAK;EACpB;EAEA,SAASgB,IAAIA,CAAClB,IAA4B,EAAE;IAC1C,MAAMmB,OAAO,GAAG;MACdT,KAAK,EAAEd,CAAC,CAACwB,iBAAiB,CAACpB,IAAI,CAACe,IAAI,CAAC;MACrCJ,KAAK,EAAEf,CAAC,CAACyB,iBAAiB,CAACrB,IAAI,CAACe,IAAI;IACtC,CAAC;IACDf,IAAI,CAACiB,QAAQ,CAACX,2BAA2B,EAAEa,OAAO,CAAC;IAEnD,IAAIG,WAAW;IAEf,IAAIH,OAAO,CAACT,KAAK,EAAE;MACjB,MAAMa,EAAE,GAAG3B,CAAC,CAAC4B,kBAAkB,CAC7B,IAAI,EACJ,EAAE,EACF5B,CAAC,CAAC6B,cAAc,CAAC,CAAC7B,CAAC,CAAC8B,eAAe,CAAC1B,IAAI,CAACe,IAAI,CAAC,CAAC,CAAC,EAChC,IAAI,EACRI,OAAO,CAACR,KACtB,CAAC;MAEDW,WAAW,GAAG1B,CAAC,CAAC+B,eAAe,CAC7B/B,CAAC,CAACgC,cAAc,CAAChC,CAAC,CAACiC,gBAAgB,CAACN,EAAE,EAAE3B,CAAC,CAACkC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAC7DlC,CAAC,CAACmC,cAAc,CAAC,CAAC,EAKlBnC,CAAC,CAACkC,UAAU,CAAC,WAAW,CAAC,CAC1B,CAAC,EACF,IACF,CAAC;IACH,CAAC,MAAM;MACL,MAAMP,EAAE,GAAG3B,CAAC,CAACoC,uBAAuB,CAAC,EAAE,EAAEhC,IAAI,CAACe,IAAI,EAAEI,OAAO,CAACR,KAAK,CAAC;MAElEW,WAAW,GAAG1B,CAAC,CAACgC,cAAc,CAACL,EAAE,EAAE,EAAE,CAAC;MACtC,IAAIJ,OAAO,CAACR,KAAK,EAAEW,WAAW,GAAG1B,CAAC,CAACqC,eAAe,CAACX,WAAW,CAAC;IACjE;IAEAtB,IAAI,CAACkC,WAAW,CAACZ,WAAW,CAAC;EAC/B;EAEA,OAAO;IACLa,IAAI,EAAE,4CAA4C;IAElDC,OAAO,EAAE;MACPC,KAAKA,CAACrC,IAAI,EAAE;QACV,MAAMsC,iBAAiB,GAAGtC,IAAI,CAACe,IAAI,CAACwB,IAAI,CAACA,IAAI,CAACC,IAAI,CAACzB,IAAI,IACrDnB,CAAC,CAAC6C,SAAS,CAAC1B,IAAI,CAClB,CAAC;QACD,IAAI,CAACuB,iBAAiB,EAAE;QAExB,KAAK,MAAMI,IAAI,IAAI1C,IAAI,CAAC2C,GAAG,CAAC,WAAW,CAAC,EAAE;UACxC,IACE,UAAU,IAAID,IAAI,CAAC3B,IAAI,IACvB2B,IAAI,CAAC3B,IAAI,CAAC6B,QAAQ,IAClB/B,uBAAuB,CAAC6B,IAAI,CAACC,GAAG,CAAC,KAAK,CAAC,CAAC,EACxC;YACAzB,IAAI,CAEFwB,IAAI,CAACC,GAAG,CAAC,KAAK,CAChB,CAAC;UACH;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}