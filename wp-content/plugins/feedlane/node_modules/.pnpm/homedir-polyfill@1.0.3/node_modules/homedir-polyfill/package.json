{"name": "homedir-polyfill", "description": "Node.js os.homedir polyfill for older versions of node.js.", "version": "1.0.3", "homepage": "https://github.com/doowb/homedir-polyfill", "author": "<PERSON> (https://github.com/doowb)", "repository": "doowb/homedir-polyfill", "bugs": {"url": "https://github.com/doowb/homedir-polyfill/issues"}, "license": "MIT", "files": ["index.js", "polyfill.js", "LICENSE"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.1.2"}, "keywords": ["home", "homedir", "homedirectory", "os", "os-homedir", "polyfill", "userhome"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["parse-passwd"]}, "reflinks": ["verb", "verb-generate-readme"]}, "dependencies": {"parse-passwd": "^1.0.0"}}