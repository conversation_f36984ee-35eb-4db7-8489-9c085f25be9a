# @babel/plugin-transform-async-to-generator

> Turn async functions into ES2015 generators

See our website [@babel/plugin-transform-async-to-generator](https://babeljs.io/docs/babel-plugin-transform-async-to-generator) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-async-to-generator
```

or using yarn:

```sh
yarn add @babel/plugin-transform-async-to-generator --dev
```
