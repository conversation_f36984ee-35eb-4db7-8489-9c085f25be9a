declare namespace htmlTagsVoid {
	export type htmlTagsVoid =
		| 'area'
		| 'base'
		| 'br'
		| 'col'
		| 'embed'
		| 'hr'
		| 'img'
		| 'input'
		| 'link'
		| 'menuitem'
		| 'meta'
		| 'param'
		| 'source'
		| 'track'
		| 'wbr'
}

/**
List of standard, self-closing HTML tags.

@example
```
import htmlTags = require('html-tags/void');

console.log(htmlTags);
//=> ['a', 'abbr', 'acronym' …]
```
*/
declare const htmlTagsVoid: readonly htmlTagsVoid.htmlTagsVoid[];

export = htmlTagsVoid;
