/**
 * Memize options object.
 *
 * @typedef MemizeOptions
 *
 * @property {number} [maxSize] Maximum size of the cache.
 */
/**
 * Internal cache entry.
 *
 * @typedef MemizeCacheNode
 *
 * @property {?MemizeCacheNode|undefined} [prev] Previous node.
 * @property {?MemizeCacheNode|undefined} [next] Next node.
 * @property {Array<*>}                   args   Function arguments for cache
 *                                               entry.
 * @property {*}                          val    Function result.
 */
/**
 * Properties of the enhanced function for controlling cache.
 *
 * @typedef MemizeMemoizedFunction
 *
 * @property {()=>void} clear Clear the cache.
 */
/**
 * Accepts a function to be memoized, and returns a new memoized function, with
 * optional options.
 *
 * @template {(...args: any[]) => any} F
 *
 * @param {F}             fn        Function to memoize.
 * @param {MemizeOptions} [options] Options object.
 *
 * @return {((...args: Parameters<F>) => ReturnType<F>) & MemizeMemoizedFunction} Memoized function.
 */
export default function memize<F extends (...args: any[]) => any>(fn: F, options?: MemizeOptions | undefined): ((...args: Parameters<F>) => ReturnType<F>) & MemizeMemoizedFunction;
/**
 * Memize options object.
 */
export type MemizeOptions = {
    /**
     * Maximum size of the cache.
     */
    maxSize?: number | undefined;
};
/**
 * Internal cache entry.
 */
export type MemizeCacheNode = {
    /**
     * Previous node.
     */
    prev?: (MemizeCacheNode | undefined) | null;
    /**
     * Next node.
     */
    next?: (MemizeCacheNode | undefined) | null;
    /**
     * Function arguments for cache
     * entry.
     */
    args: Array<any>;
    /**
     * Function result.
     */
    val: any;
};
/**
 * Properties of the enhanced function for controlling cache.
 */
export type MemizeMemoizedFunction = {
    /**
     * Clear the cache.
     */
    clear: () => void;
};
