# @babel/plugin-syntax-import-attributes

> Allow parsing of the module attributes in the import statement

See our website [@babel/plugin-syntax-import-attributes](https://babeljs.io/docs/babel-plugin-syntax-import-attributes) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-syntax-import-attributes
```

or using yarn:

```sh
yarn add @babel/plugin-syntax-import-attributes --dev
```
