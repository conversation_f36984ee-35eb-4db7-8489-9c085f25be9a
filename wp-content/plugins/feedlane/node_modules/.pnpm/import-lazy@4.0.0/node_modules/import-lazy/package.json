{"name": "import-lazy", "version": "4.0.0", "description": "Import a module lazily", "license": "MIT", "repository": "sindresorhus/import-lazy", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["import", "require", "load", "module", "modules", "lazy", "lazily", "defer", "deferred", "proxy", "proxies"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}