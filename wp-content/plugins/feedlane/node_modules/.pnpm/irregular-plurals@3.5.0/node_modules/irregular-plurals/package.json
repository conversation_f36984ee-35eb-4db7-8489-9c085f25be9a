{"name": "irregular-plurals", "version": "3.5.0", "description": "Map of nouns to their irregular plural form", "license": "MIT", "repository": "sindresorhus/irregular-plurals", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"//test": "xo && ava && tsd", "test": "ava"}, "files": ["index.js", "index.d.ts", "irregular-plurals.json"], "keywords": ["word", "words", "list", "map", "hash", "json", "irregular", "plural", "plurals", "noun", "nouns"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "xo": {"rules": {"import/extensions": "off"}}, "tsd": {"compilerOptions": {"resolveJsonModule": true}}}