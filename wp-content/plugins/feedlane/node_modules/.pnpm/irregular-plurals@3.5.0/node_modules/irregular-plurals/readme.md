# irregular-plurals

> Map of nouns to their irregular plural form

> An irregular plural in this library is defined as a noun that cannot be made plural by applying these rules:
> - If the noun ends in an "s", "x", "z", "ch" or "sh", add "es"
> - If the noun ends in a "y" and is preceded by a consonant, drop the "y" and add "ies"
> - If the noun ends in a "y" and is preceded by a vowel, add "s"

The list is just a [JSON file](irregular-plurals.json) and can be used anywhere.

## Install

```
$ npm install irregular-plurals
```

## Usage

```js
const irregularPlurals = require('irregular-plurals');

console.log(irregularPlurals.get('cactus'));
//=> 'cacti'

console.log(irregularPlurals);
/*
Map {
	[addendum, 'addenda'],
	[alga, 'algae'],
	…
}
*/
```

## Related

- [plur](https://github.com/sindresorhus/plur) - Pluralize a word
