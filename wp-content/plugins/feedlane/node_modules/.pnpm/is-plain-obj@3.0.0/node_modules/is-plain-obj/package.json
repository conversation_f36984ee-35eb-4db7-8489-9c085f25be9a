{"name": "is-plain-obj", "version": "3.0.0", "description": "Check if a value is a plain object", "license": "MIT", "repository": "sindresorhus/is-plain-obj", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["object", "is", "check", "test", "type", "plain", "vanilla", "pure", "simple"], "devDependencies": {"ava": "^2.4.0", "tsd": "^0.13.1", "xo": "^0.33.1"}}