{"name": "is-windows", "description": "Returns true if the platform is windwows.", "version": "0.2.0", "homepage": "https://github.com/jonschlinkert/is-windows", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/is-windows", "bugs": {"url": "https://github.com/jonschlinkert/is-windows/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "*"}, "keywords": ["check", "is", "is-windows", "nix", "platform", "process", "unix", "win", "win32", "windows"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-absolute", "is-glob", "is-relative", "isobject", "window-size"]}, "lint": {"reflinks": true}, "reflinks": ["verb"]}}