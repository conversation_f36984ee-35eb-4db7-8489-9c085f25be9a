{"name": "is-plain-obj", "version": "1.1.0", "description": "Check if a value is a plain object", "license": "MIT", "repository": "sindresorhus/is-plain-obj", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["obj", "object", "is", "check", "test", "type", "plain", "vanilla", "pure", "simple"], "devDependencies": {"ava": "0.0.4"}}