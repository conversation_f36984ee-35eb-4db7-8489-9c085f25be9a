{"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor, or Object.create(null).", "version": "5.0.0", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON><PERSON> (http://onokumus.com)", "<PERSON> (https://svachon.com)", "(https://github.com/wtgtybhertgeghgtwtg)", "<PERSON><PERSON><PERSON> (https://github.com/TrySound)"], "repository": "jonschlinkert/is-plain-object", "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "main": "dist/is-plain-object.js", "module": "dist/is-plain-object.mjs", "types": "is-plain-object.d.ts", "files": ["is-plain-object.d.ts", "dist"], "exports": {".": {"import": "./dist/is-plain-object.mjs", "require": "./dist/is-plain-object.js"}, "./package.json": "./package.json"}, "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test_browser": "mocha-headless-chrome --args=disable-web-security -f test/browser.html", "test_node": "mocha -r esm", "test": "npm run test_node && npm run build && npm run test_browser", "prepare": "rollup -c"}, "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.22.1"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}}