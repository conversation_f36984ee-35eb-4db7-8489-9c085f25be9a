{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,GAAG,CAAC;AAE/C,IAAM,UAAU,GAAuB;IACrC,CAAC,EAAE,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,aAAa,EAAE,aAAa,CAAC;CAC/B,CAAC;AAEF,IAAM,iBAAiB,GAA+B;IACpD,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC;CAC3B,CAAC;AAEF,QAAQ,CAAC,YAAY,EAAE;4BACT,KAAK,EAAE,MAAM;QACvB,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;;IAHL,KAA8B,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAA7B,IAAA,qBAAe,EAAd,KAAK,QAAA,EAAE,MAAM,QAAA;gBAAb,KAAK,EAAE,MAAM;KAIxB;AACH,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,mBAAmB,EAAE;4BAChB,KAAK,EAAE,MAAM,EAAE,MAAM;QAC/B,EAAE,CAAI,MAAM,UAAK,KAAK,YAAO,MAAQ,EAAE;YACrC,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;;IAHL,KAAsC,UAAiB,EAAjB,uCAAiB,EAAjB,+BAAiB,EAAjB,IAAiB;QAA5C,IAAA,4BAAuB,EAAtB,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,MAAM,QAAA;gBAArB,KAAK,EAAE,MAAM,EAAE,MAAM;KAIhC;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { lowerCase, localeLowerCase } from \".\";\n\nconst TEST_CASES: [string, string][] = [\n  [\"\", \"\"],\n  [\"test\", \"test\"],\n  [\"TEST\", \"test\"],\n  [\"test string\", \"test string\"],\n  [\"TEST STRING\", \"test string\"],\n];\n\nconst LOCALE_TEST_CASES: [string, string, string][] = [\n  [\"STRING\", \"strıng\", \"tr\"],\n];\n\ndescribe(\"lower case\", () => {\n  for (const [input, result] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(lowerCase(input)).toEqual(result);\n    });\n  }\n});\n\ndescribe(\"locale lower case\", () => {\n  for (const [input, result, locale] of LOCALE_TEST_CASES) {\n    it(`${locale}: ${input} -> ${result}`, () => {\n      expect(localeLowerCase(input, locale)).toEqual(result);\n    });\n  }\n});\n"]}