{"name": "jest-dev-server", "description": "Starts a server before your Jest tests and tears it down after.", "version": "6.2.0", "main": "index.js", "repository": "https://github.com/smooth-code/jest-puppeteer/tree/master/packages/jest-dev-server", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["jest", "jest-environment", "server"], "scripts": {"prebuild": "rimraf lib/", "build": "babel --config-file ../../babel.config.js -d lib --ignore \"**/*.test.js\" src", "dev": "yarn build --watch", "prepublishOnly": "yarn build"}, "dependencies": {"chalk": "^4.1.2", "cwd": "^0.10.0", "find-process": "^1.4.7", "prompts": "^2.4.2", "spawnd": "^6.2.0", "tree-kill": "^1.2.2", "wait-on": "^6.0.1"}, "gitHead": "108d6f69cb65ae2929c7269aac1c2e4c5077b142"}