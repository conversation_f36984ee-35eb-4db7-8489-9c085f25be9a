{"version": 3, "names": ["ESLINT_VERSION", "require", "it", "children", "Array", "isArray", "traverse", "node", "visitorKeys", "visitor", "type", "keys", "key", "child", "enter", "exit", "convertNodesVisitor", "innerComments", "trailingComments", "leadingComments", "extra", "loc", "identifierName", "typeAnnotation", "bound", "id", "name", "isType", "i", "quasis", "length", "q", "range", "tail", "start", "column", "end", "convertNodes", "ast", "convertProgramNode", "body", "program", "Object", "assign", "sourceType", "errors", "comments", "lastComment", "tokens", "lastToken", "line", "module", "exports", "convertAST"], "sources": ["../../src/convert/convertAST.cts"], "sourcesContent": ["import type * as t from \"@babel/types\";\nimport ESLINT_VERSION = require(\"../utils/eslint-version.cts\");\nimport type { ParseResult } from \"../types.d.cts\";\n\nfunction* it<T>(children: T | T[]) {\n  if (Array.isArray(children)) yield* children;\n  else yield children;\n}\n\nfunction traverse(\n  node: t.Node,\n  visitorKeys: Record<string, string[]>,\n  visitor: typeof convertNodesVisitor,\n) {\n  const { type } = node;\n  if (!type) return;\n  const keys = visitorKeys[type];\n  if (!keys) return;\n\n  for (const key of keys) {\n    for (const child of it(\n      node[key as keyof t.Node] as unknown as t.Node | t.Node[],\n    )) {\n      if (child && typeof child === \"object\") {\n        visitor.enter(child);\n        traverse(child, visitorKeys, visitor);\n        visitor.exit(child);\n      }\n    }\n  }\n}\n\nconst convertNodesVisitor = {\n  enter(node: t.Node) {\n    if (node.innerComments) {\n      delete node.innerComments;\n    }\n\n    if (node.trailingComments) {\n      delete node.trailingComments;\n    }\n\n    if (node.leadingComments) {\n      delete node.leadingComments;\n    }\n  },\n  exit(node: t.Node) {\n    // Used internally by @babel/parser.\n    if (node.extra) {\n      delete node.extra;\n    }\n\n    if (process.env.IS_PUBLISH) {\n      if (node.loc.identifierName) {\n        delete node.loc.identifierName;\n      }\n    } else {\n      // To minimize the jest-diff noise comparing Babel AST and third-party AST,\n      // here we generate a deep copy of loc without identifierName and index\n      if (node.loc) {\n        node.loc = {\n          end: {\n            column: node.loc.end.column,\n            line: node.loc.end.line,\n          },\n          start: {\n            column: node.loc.start.column,\n            line: node.loc.start.line,\n          },\n        } as any;\n      }\n    }\n\n    if (node.type === \"TypeParameter\") {\n      // @ts-expect-error eslint\n      node.type = \"Identifier\";\n      // @ts-expect-error eslint\n      node.typeAnnotation = node.bound;\n      delete node.bound;\n    }\n\n    // flow: prevent \"no-undef\"\n    // for \"Component\" in: \"let x: React.Component\"\n    if (node.type === \"QualifiedTypeIdentifier\") {\n      delete node.id;\n    }\n    // for \"b\" in: \"var a: { b: Foo }\"\n    if (node.type === \"ObjectTypeProperty\") {\n      delete node.key;\n    }\n    // for \"indexer\" in: \"var a: {[indexer: string]: number}\"\n    if (node.type === \"ObjectTypeIndexer\") {\n      delete node.id;\n    }\n    // for \"param\" in: \"var a: { func(param: Foo): Bar };\"\n    if (node.type === \"FunctionTypeParam\") {\n      delete node.name;\n    }\n\n    // modules\n    if (node.type === \"ImportDeclaration\") {\n      // @ts-expect-error legacy?\n      delete node.isType;\n    }\n\n    // template string range fixes\n    if (\n      node.type === \"TemplateLiteral\" ||\n      node.type === \"TSTemplateLiteralType\"\n    ) {\n      for (let i = 0; i < node.quasis.length; i++) {\n        const q = node.quasis[i];\n        q.range[0] -= 1;\n        if (q.tail) {\n          q.range[1] += 1;\n        } else {\n          q.range[1] += 2;\n        }\n        q.loc.start.column -= 1;\n        if (q.tail) {\n          q.loc.end.column += 1;\n        } else {\n          q.loc.end.column += 2;\n        }\n\n        if (ESLINT_VERSION >= 8) {\n          q.start -= 1;\n          if (q.tail) {\n            q.end += 1;\n          } else {\n            q.end += 2;\n          }\n        }\n      }\n    }\n  },\n};\n\nfunction convertNodes(ast: ParseResult, visitorKeys: Record<string, string[]>) {\n  traverse(ast as unknown as t.Program, visitorKeys, convertNodesVisitor);\n}\n\nfunction convertProgramNode(ast: ParseResult) {\n  const body = ast.program.body;\n  Object.assign(ast, {\n    type: \"Program\",\n    sourceType: ast.program.sourceType,\n    body,\n  });\n  delete ast.program;\n  delete ast.errors;\n\n  if (ast.comments.length) {\n    const lastComment = ast.comments[ast.comments.length - 1];\n\n    if (ast.tokens.length) {\n      const lastToken = ast.tokens[ast.tokens.length - 1];\n\n      if (lastComment.end > lastToken.end) {\n        // If there is a comment after the last token, the program ends at the\n        // last token and not the comment\n        ast.range[1] = lastToken.end;\n        ast.loc.end.line = lastToken.loc.end.line;\n        ast.loc.end.column = lastToken.loc.end.column;\n\n        if (ESLINT_VERSION >= 8) {\n          ast.end = lastToken.end;\n        }\n      }\n    }\n  } else {\n    if (!ast.tokens.length) {\n      ast.loc.start.line = 1;\n      ast.loc.end.line = 1;\n    }\n  }\n\n  if (body?.length) {\n    ast.loc.start.line = body[0].loc.start.line;\n    ast.range[0] = body[0].start;\n\n    if (ESLINT_VERSION >= 8) {\n      ast.start = body[0].start;\n    }\n  }\n}\n\nexport = function convertAST(\n  ast: ParseResult,\n  visitorKeys: Record<string, string[]>,\n) {\n  convertNodes(ast, visitorKeys);\n  convertProgramNode(ast);\n};\n"], "mappings": ";;MACOA,cAAc,GAAAC,OAAA,CAAW,6BAA6B;AAG7D,UAAUC,EAAEA,CAAIC,QAAiB,EAAE;EACjC,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE,OAAOA,QAAQ,CAAC,KACxC,MAAMA,QAAQ;AACrB;AAEA,SAASG,QAAQA,CACfC,IAAY,EACZC,WAAqC,EACrCC,OAAmC,EACnC;EACA,MAAM;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrB,IAAI,CAACG,IAAI,EAAE;EACX,MAAMC,IAAI,GAAGH,WAAW,CAACE,IAAI,CAAC;EAC9B,IAAI,CAACC,IAAI,EAAE;EAEX,KAAK,MAAMC,GAAG,IAAID,IAAI,EAAE;IACtB,KAAK,MAAME,KAAK,IAAIX,EAAE,CACpBK,IAAI,CAACK,GAAG,CACV,CAAC,EAAE;MACD,IAAIC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACtCJ,OAAO,CAACK,KAAK,CAACD,KAAK,CAAC;QACpBP,QAAQ,CAACO,KAAK,EAAEL,WAAW,EAAEC,OAAO,CAAC;QACrCA,OAAO,CAACM,IAAI,CAACF,KAAK,CAAC;MACrB;IACF;EACF;AACF;AAEA,MAAMG,mBAAmB,GAAG;EAC1BF,KAAKA,CAACP,IAAY,EAAE;IAClB,IAAIA,IAAI,CAACU,aAAa,EAAE;MACtB,OAAOV,IAAI,CAACU,aAAa;IAC3B;IAEA,IAAIV,IAAI,CAACW,gBAAgB,EAAE;MACzB,OAAOX,IAAI,CAACW,gBAAgB;IAC9B;IAEA,IAAIX,IAAI,CAACY,eAAe,EAAE;MACxB,OAAOZ,IAAI,CAACY,eAAe;IAC7B;EACF,CAAC;EACDJ,IAAIA,CAACR,IAAY,EAAE;IAEjB,IAAIA,IAAI,CAACa,KAAK,EAAE;MACd,OAAOb,IAAI,CAACa,KAAK;IACnB;IAE4B;MAC1B,IAAIb,IAAI,CAACc,GAAG,CAACC,cAAc,EAAE;QAC3B,OAAOf,IAAI,CAACc,GAAG,CAACC,cAAc;MAChC;IACF;IAiBA,IAAIf,IAAI,CAACG,IAAI,KAAK,eAAe,EAAE;MAEjCH,IAAI,CAACG,IAAI,GAAG,YAAY;MAExBH,IAAI,CAACgB,cAAc,GAAGhB,IAAI,CAACiB,KAAK;MAChC,OAAOjB,IAAI,CAACiB,KAAK;IACnB;IAIA,IAAIjB,IAAI,CAACG,IAAI,KAAK,yBAAyB,EAAE;MAC3C,OAAOH,IAAI,CAACkB,EAAE;IAChB;IAEA,IAAIlB,IAAI,CAACG,IAAI,KAAK,oBAAoB,EAAE;MACtC,OAAOH,IAAI,CAACK,GAAG;IACjB;IAEA,IAAIL,IAAI,CAACG,IAAI,KAAK,mBAAmB,EAAE;MACrC,OAAOH,IAAI,CAACkB,EAAE;IAChB;IAEA,IAAIlB,IAAI,CAACG,IAAI,KAAK,mBAAmB,EAAE;MACrC,OAAOH,IAAI,CAACmB,IAAI;IAClB;IAGA,IAAInB,IAAI,CAACG,IAAI,KAAK,mBAAmB,EAAE;MAErC,OAAOH,IAAI,CAACoB,MAAM;IACpB;IAGA,IACEpB,IAAI,CAACG,IAAI,KAAK,iBAAiB,IAC/BH,IAAI,CAACG,IAAI,KAAK,uBAAuB,EACrC;MACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,IAAI,CAACsB,MAAM,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC3C,MAAMG,CAAC,GAAGxB,IAAI,CAACsB,MAAM,CAACD,CAAC,CAAC;QACxBG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QACf,IAAID,CAAC,CAACE,IAAI,EAAE;UACVF,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QACjB,CAAC,MAAM;UACLD,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QACjB;QACAD,CAAC,CAACV,GAAG,CAACa,KAAK,CAACC,MAAM,IAAI,CAAC;QACvB,IAAIJ,CAAC,CAACE,IAAI,EAAE;UACVF,CAAC,CAACV,GAAG,CAACe,GAAG,CAACD,MAAM,IAAI,CAAC;QACvB,CAAC,MAAM;UACLJ,CAAC,CAACV,GAAG,CAACe,GAAG,CAACD,MAAM,IAAI,CAAC;QACvB;QAEA,IAAInC,cAAc,IAAI,CAAC,EAAE;UACvB+B,CAAC,CAACG,KAAK,IAAI,CAAC;UACZ,IAAIH,CAAC,CAACE,IAAI,EAAE;YACVF,CAAC,CAACK,GAAG,IAAI,CAAC;UACZ,CAAC,MAAM;YACLL,CAAC,CAACK,GAAG,IAAI,CAAC;UACZ;QACF;MACF;IACF;EACF;AACF,CAAC;AAED,SAASC,YAAYA,CAACC,GAAgB,EAAE9B,WAAqC,EAAE;EAC7EF,QAAQ,CAACgC,GAAG,EAA0B9B,WAAW,EAAEQ,mBAAmB,CAAC;AACzE;AAEA,SAASuB,kBAAkBA,CAACD,GAAgB,EAAE;EAC5C,MAAME,IAAI,GAAGF,GAAG,CAACG,OAAO,CAACD,IAAI;EAC7BE,MAAM,CAACC,MAAM,CAACL,GAAG,EAAE;IACjB5B,IAAI,EAAE,SAAS;IACfkC,UAAU,EAAEN,GAAG,CAACG,OAAO,CAACG,UAAU;IAClCJ;EACF,CAAC,CAAC;EACF,OAAOF,GAAG,CAACG,OAAO;EAClB,OAAOH,GAAG,CAACO,MAAM;EAEjB,IAAIP,GAAG,CAACQ,QAAQ,CAAChB,MAAM,EAAE;IACvB,MAAMiB,WAAW,GAAGT,GAAG,CAACQ,QAAQ,CAACR,GAAG,CAACQ,QAAQ,CAAChB,MAAM,GAAG,CAAC,CAAC;IAEzD,IAAIQ,GAAG,CAACU,MAAM,CAAClB,MAAM,EAAE;MACrB,MAAMmB,SAAS,GAAGX,GAAG,CAACU,MAAM,CAACV,GAAG,CAACU,MAAM,CAAClB,MAAM,GAAG,CAAC,CAAC;MAEnD,IAAIiB,WAAW,CAACX,GAAG,GAAGa,SAAS,CAACb,GAAG,EAAE;QAGnCE,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,GAAGiB,SAAS,CAACb,GAAG;QAC5BE,GAAG,CAACjB,GAAG,CAACe,GAAG,CAACc,IAAI,GAAGD,SAAS,CAAC5B,GAAG,CAACe,GAAG,CAACc,IAAI;QACzCZ,GAAG,CAACjB,GAAG,CAACe,GAAG,CAACD,MAAM,GAAGc,SAAS,CAAC5B,GAAG,CAACe,GAAG,CAACD,MAAM;QAE7C,IAAInC,cAAc,IAAI,CAAC,EAAE;UACvBsC,GAAG,CAACF,GAAG,GAAGa,SAAS,CAACb,GAAG;QACzB;MACF;IACF;EACF,CAAC,MAAM;IACL,IAAI,CAACE,GAAG,CAACU,MAAM,CAAClB,MAAM,EAAE;MACtBQ,GAAG,CAACjB,GAAG,CAACa,KAAK,CAACgB,IAAI,GAAG,CAAC;MACtBZ,GAAG,CAACjB,GAAG,CAACe,GAAG,CAACc,IAAI,GAAG,CAAC;IACtB;EACF;EAEA,IAAIV,IAAI,YAAJA,IAAI,CAAEV,MAAM,EAAE;IAChBQ,GAAG,CAACjB,GAAG,CAACa,KAAK,CAACgB,IAAI,GAAGV,IAAI,CAAC,CAAC,CAAC,CAACnB,GAAG,CAACa,KAAK,CAACgB,IAAI;IAC3CZ,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,GAAGQ,IAAI,CAAC,CAAC,CAAC,CAACN,KAAK;IAE5B,IAAIlC,cAAc,IAAI,CAAC,EAAE;MACvBsC,GAAG,CAACJ,KAAK,GAAGM,IAAI,CAAC,CAAC,CAAC,CAACN,KAAK;IAC3B;EACF;AACF;AAACiB,MAAA,CAAAC,OAAA,GAEQ,SAASC,UAAUA,CAC1Bf,GAAgB,EAChB9B,WAAqC,EACrC;EACA6B,YAAY,CAACC,GAAG,EAAE9B,WAAW,CAAC;EAC9B+B,kBAAkB,CAACD,GAAG,CAAC;AACzB,CAAC", "ignoreList": []}