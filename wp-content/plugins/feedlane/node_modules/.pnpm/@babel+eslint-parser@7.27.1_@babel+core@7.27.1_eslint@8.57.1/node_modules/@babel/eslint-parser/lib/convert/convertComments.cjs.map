{"version": 3, "names": ["convertComments", "comments", "comment", "type", "range", "start", "end"], "sources": ["../../src/convert/convertComments.cts"], "sourcesContent": ["import type { Comment } from \"@babel/types\";\n\nexport = function convertComments(comments: Comment[]) {\n  for (const comment of comments) {\n    // @ts-expect-error eslint\n    comment.type = comment.type === \"CommentBlock\" ? \"Block\" : \"Line\";\n\n    // sometimes comments don't get ranges computed,\n    // even with options.ranges === true\n\n    // @ts-expect-error eslint\n    comment.range ||= [comment.start, comment.end];\n  }\n};\n"], "mappings": ";;iBAES,SAASA,eAAeA,CAACC,QAAmB,EAAE;EACrD,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;IAE9BC,OAAO,CAACC,IAAI,GAAGD,OAAO,CAACC,IAAI,KAAK,cAAc,GAAG,OAAO,GAAG,MAAM;IAMjED,OAAO,CAACE,KAAK,KAAbF,OAAO,CAACE,KAAK,GAAK,CAACF,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACI,GAAG,CAAC;EAChD;AACF,CAAC", "ignoreList": []}