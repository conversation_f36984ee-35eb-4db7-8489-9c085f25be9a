{"name": "jsonc-parser", "version": "3.0.0", "description": "Scanner and parser for JSON with comments.", "main": "./lib/umd/main.js", "typings": "./lib/umd/main", "module": "./lib/esm/main.js", "author": "Microsoft Corporation", "repository": {"type": "git", "url": "https://github.com/microsoft/node-jsonc-parser"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/node-jsonc-parser/issues"}, "devDependencies": {"mocha": "^8.2.1", "typescript": "^4.0.5", "@types/node": "^10.12.12", "@types/mocha": "^5.2.7", "@typescript-eslint/eslint-plugin": "^4.7.0", "@typescript-eslint/parser": "^4.7.0", "eslint": "^7.13.0", "rimraf": "^3.0.2"}, "scripts": {"prepublishOnly": "npm run clean && npm run compile-esm && npm run test && npm run remove-sourcemap-refs", "postpublish": "node ./build/post-publish.js", "compile": "tsc -p ./src && npm run lint", "compile-esm": "tsc -p ./src/tsconfig.esm.json", "remove-sourcemap-refs": "node ./build/remove-sourcemap-refs.js", "clean": "<PERSON><PERSON><PERSON> lib", "watch": "tsc -w -p ./src", "test": "npm run compile && mocha ./lib/umd/test", "lint": "eslint src/**/*.ts", "preversion": "npm test", "postversion": "git push && git push --tags"}}