const { registerBlockType } = wp.blocks;
const { InspectorControls } = wp.blockEditor;
const { PanelBody, SelectControl, TextControl, ColorPicker } = wp.components;
const { __ } = wp.i18n;

registerBlockType('feedlane/sidebar', {
    title: __('Feedlane Sidebar', 'feedlane'),
    description: __('Add a feedback sidebar with newsfeed, ideas, and roadmap tabs.', 'feedlane'),
    category: 'widgets',
    icon: 'feedback',
    attributes: {
        position: {
            type: 'string',
            default: 'left',
        },
        height: {
            type: 'string',
            default: '600px',
        },
        primaryColor: {
            type: 'string',
            default: '#0ea5e9',
        },
    },
    
    edit: function(props) {
        const { attributes, setAttributes } = props;
        const { position, height, primaryColor } = attributes;
        
        return [
            wp.element.createElement(InspectorControls, { key: 'inspector' },
                wp.element.createElement(PanelBody, {
                    title: __('Settings', 'feedlane'),
                    initialOpen: true,
                },
                    wp.element.createElement(SelectControl, {
                        label: __('Position', 'feedlane'),
                        value: position,
                        options: [
                            { label: __('Left', 'feedlane'), value: 'left' },
                            { label: __('Right', 'feedlane'), value: 'right' },
                        ],
                        onChange: function(value) {
                            setAttributes({ position: value });
                        },
                    }),
                    wp.element.createElement(TextControl, {
                        label: __('Height', 'feedlane'),
                        value: height,
                        onChange: function(value) {
                            setAttributes({ height: value });
                        },
                        help: __('Set the height (e.g., 600px, 100vh)', 'feedlane'),
                    }),
                    wp.element.createElement('div', {
                        style: { marginBottom: '16px' }
                    },
                        wp.element.createElement('label', {
                            style: { 
                                display: 'block', 
                                marginBottom: '8px',
                                fontWeight: '600'
                            }
                        }, __('Primary Color', 'feedlane')),
                        wp.element.createElement(ColorPicker, {
                            color: primaryColor,
                            onChangeComplete: function(color) {
                                setAttributes({ primaryColor: color.hex });
                            },
                        })
                    )
                )
            ),
            
            wp.element.createElement('div', {
                key: 'preview',
                className: 'feedlane-block-preview',
                style: {
                    border: '2px dashed #ddd',
                    padding: '40px 20px',
                    textAlign: 'center',
                    background: '#f9f9f9',
                    borderRadius: '8px',
                    '--feedlane-primary-color': primaryColor
                }
            },
                wp.element.createElement('div', {
                    style: {
                        fontSize: '48px',
                        color: '#666',
                        marginBottom: '16px'
                    }
                }, '💬'),
                wp.element.createElement('h4', {
                    style: {
                        margin: '0 0 12px 0',
                        color: '#333'
                    }
                }, __('Feedlane Sidebar', 'feedlane')),
                wp.element.createElement('p', {
                    style: {
                        margin: '4px 0',
                        color: '#666',
                        fontSize: '14px'
                    }
                }, __('Position:', 'feedlane') + ' ' + position),
                wp.element.createElement('p', {
                    style: {
                        margin: '4px 0',
                        color: '#666',
                        fontSize: '14px'
                    }
                }, __('Height:', 'feedlane') + ' ' + height),
                wp.element.createElement('div', {
                    style: {
                        width: '20px',
                        height: '20px',
                        backgroundColor: primaryColor,
                        borderRadius: '50%',
                        margin: '8px auto 0',
                        border: '2px solid #fff',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                    }
                })
            )
        ];
    },
    
    save: function() {
        // Return null since this is a dynamic block
        return null;
    },
});
