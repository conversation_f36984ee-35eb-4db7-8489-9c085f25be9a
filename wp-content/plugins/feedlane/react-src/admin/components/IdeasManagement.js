import React, { useState } from 'react';
import toast from 'react-hot-toast';

const IdeasManagement = () => {
    // Mock data for now
    const [ideas, setIdeas] = useState([
        {
            id: 1,
            title: 'Dark Mode Support',
            category: 'Feature Request',
            status: 'pending',
            votes: 89,
            submitter: '<PERSON>',
            date: '2024-01-15',
            excerpt: 'Add dark mode theme option for better user experience...'
        },
        {
            id: 2,
            title: 'Mobile App',
            category: 'Feature Request',
            status: 'under-review',
            votes: 67,
            submitter: '<PERSON>',
            date: '2024-01-14',
            excerpt: 'Create a mobile application for iOS and Android...'
        },
        {
            id: 3,
            title: 'Better Search',
            category: 'Improvement',
            status: 'planned',
            votes: 45,
            submitter: '<PERSON>',
            date: '2024-01-13',
            excerpt: 'Improve search functionality with filters and sorting...'
        }
    ]);

    const [filter, setFilter] = useState('all');

    const handleStatusChange = (ideaId, newStatus) => {
        setIdeas(prev => prev.map(idea => 
            idea.id === ideaId ? { ...idea, status: newStatus } : idea
        ));
        toast.success('Status updated successfully!');
    };

    const handleApprove = (ideaId) => {
        handleStatusChange(ideaId, 'under-review');
    };

    const handleReject = (ideaId) => {
        setIdeas(prev => prev.filter(idea => idea.id !== ideaId));
        toast.success('Idea rejected and removed');
    };

    const filteredIdeas = filter === 'all' ? ideas : ideas.filter(idea => idea.status === filter);

    const getStatusBadge = (status) => {
        const statusClasses = {
            'pending': 'feedlane-badge--warning',
            'under-review': 'feedlane-badge--info',
            'planned': 'feedlane-badge--success',
            'in-progress': 'feedlane-badge--info',
            'completed': 'feedlane-badge--success'
        };

        return (
            <span className={`feedlane-badge ${statusClasses[status] || 'feedlane-badge--gray'}`}>
                {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </span>
        );
    };

    return (
        <div className="feedlane-admin">
            <div className="feedlane-admin__header">
                <h1>Ideas Management</h1>
                <p>Review and manage submitted ideas</p>
            </div>

            <div className="feedlane-admin__content">
                {/* Filters */}
                <div className="mb-6">
                    <div className="flex gap-2">
                        <button
                            onClick={() => setFilter('all')}
                            className={`feedlane-btn feedlane-btn--small ${filter === 'all' ? 'feedlane-btn--primary' : 'feedlane-btn--secondary'}`}
                        >
                            All ({ideas.length})
                        </button>
                        <button
                            onClick={() => setFilter('pending')}
                            className={`feedlane-btn feedlane-btn--small ${filter === 'pending' ? 'feedlane-btn--primary' : 'feedlane-btn--secondary'}`}
                        >
                            Pending ({ideas.filter(i => i.status === 'pending').length})
                        </button>
                        <button
                            onClick={() => setFilter('under-review')}
                            className={`feedlane-btn feedlane-btn--small ${filter === 'under-review' ? 'feedlane-btn--primary' : 'feedlane-btn--secondary'}`}
                        >
                            Under Review ({ideas.filter(i => i.status === 'under-review').length})
                        </button>
                        <button
                            onClick={() => setFilter('planned')}
                            className={`feedlane-btn feedlane-btn--small ${filter === 'planned' ? 'feedlane-btn--primary' : 'feedlane-btn--secondary'}`}
                        >
                            Planned ({ideas.filter(i => i.status === 'planned').length})
                        </button>
                    </div>
                </div>

                {/* Ideas Table */}
                <div className="overflow-x-auto">
                    <table className="feedlane-table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Votes</th>
                                <th>Submitter</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredIdeas.map(idea => (
                                <tr key={idea.id}>
                                    <td>
                                        <div>
                                            <div className="font-medium text-gray-900">{idea.title}</div>
                                            <div className="text-sm text-gray-500">{idea.excerpt}</div>
                                        </div>
                                    </td>
                                    <td>{idea.category}</td>
                                    <td>{getStatusBadge(idea.status)}</td>
                                    <td>{idea.votes}</td>
                                    <td>{idea.submitter}</td>
                                    <td>{idea.date}</td>
                                    <td>
                                        <div className="flex gap-2">
                                            {idea.status === 'pending' && (
                                                <>
                                                    <button
                                                        onClick={() => handleApprove(idea.id)}
                                                        className="feedlane-btn feedlane-btn--primary feedlane-btn--small"
                                                    >
                                                        Approve
                                                    </button>
                                                    <button
                                                        onClick={() => handleReject(idea.id)}
                                                        className="feedlane-btn feedlane-btn--danger feedlane-btn--small"
                                                    >
                                                        Reject
                                                    </button>
                                                </>
                                            )}
                                            {idea.status !== 'pending' && (
                                                <select
                                                    value={idea.status}
                                                    onChange={(e) => handleStatusChange(idea.id, e.target.value)}
                                                    className="text-sm border border-gray-300 rounded px-2 py-1"
                                                >
                                                    <option value="under-review">Under Review</option>
                                                    <option value="planned">Planned</option>
                                                    <option value="in-progress">In Progress</option>
                                                    <option value="completed">Completed</option>
                                                </select>
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {filteredIdeas.length === 0 && (
                    <div className="feedlane-empty">
                        <div className="feedlane-empty__icon">💡</div>
                        <h3>No Ideas Found</h3>
                        <p>No ideas match the current filter.</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default IdeasManagement;
