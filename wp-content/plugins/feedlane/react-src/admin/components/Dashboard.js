import React from 'react';
import { useQuery } from '@tanstack/react-query';

const Dashboard = () => {
    // Mock data for now - replace with actual API calls
    const stats = {
        totalPosts: 12,
        totalIdeas: 45,
        pendingIdeas: 8,
        totalFeedback: 156,
        totalVotes: 234
    };

    return (
        <div className="feedlane-admin">
            <div className="feedlane-admin__header">
                <h1>Feedlane Dashboard</h1>
                <p>Overview of your feedback system</p>
            </div>

            <div className="feedlane-admin__content">
                <div className="feedlane-admin__grid">
                    <div className="feedlane-admin__card">
                        <div className="stat-number">{stats.totalPosts}</div>
                        <div className="stat-label">Newsfeed Posts</div>
                        <p>Published announcements and updates</p>
                    </div>

                    <div className="feedlane-admin__card">
                        <div className="stat-number">{stats.totalIdeas}</div>
                        <div className="stat-label">Total Ideas</div>
                        <p>Ideas submitted by users</p>
                    </div>

                    <div className="feedlane-admin__card">
                        <div className="stat-number">{stats.pendingIdeas}</div>
                        <div className="stat-label">Pending Ideas</div>
                        <p>Ideas awaiting approval</p>
                    </div>

                    <div className="feedlane-admin__card">
                        <div className="stat-number">{stats.totalFeedback}</div>
                        <div className="stat-label">Feedback Comments</div>
                        <p>User feedback on posts</p>
                    </div>

                    <div className="feedlane-admin__card">
                        <div className="stat-number">{stats.totalVotes}</div>
                        <div className="stat-label">Total Votes</div>
                        <p>Votes cast on ideas</p>
                    </div>

                    <div className="feedlane-admin__card">
                        <h3>Quick Actions</h3>
                        <div className="space-y-2">
                            <a href="post-new.php?post_type=feedlane_posts" className="feedlane-btn feedlane-btn--primary feedlane-btn--small block text-center">
                                Add Newsfeed Post
                            </a>
                            <a href="admin.php?page=feedlane-ideas" className="feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center">
                                Manage Ideas
                            </a>
                            <a href="admin.php?page=feedlane-settings" className="feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center">
                                Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Dashboard;
