import React, { useState } from 'react';
import toast from 'react-hot-toast';

const Settings = () => {
    const [settings, setSettings] = useState({
        enable_newsfeed: true,
        enable_ideas: true,
        enable_roadmap: true,
        enable_guest_submissions: true,
        sidebar_position: 'left',
        primary_color: '#0ea5e9',
        firebase_config: '',
        firebase_webhook_secret: ''
    });

    const [saving, setSaving] = useState(false);

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setSettings(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);

        try {
            // Mock save - replace with actual API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            toast.success('Settings saved successfully!');
        } catch (error) {
            toast.error('Failed to save settings');
        } finally {
            setSaving(false);
        }
    };

    return (
        <div className="feedlane-admin">
            <div className="feedlane-admin__header">
                <h1>Feedlane Settings</h1>
                <p>Configure your feedback system</p>
            </div>

            <div className="feedlane-admin__content">
                <form onSubmit={handleSubmit} className="feedlane-form">
                    {/* General Settings */}
                    <div className="feedlane-form__section">
                        <h3>General Settings</h3>
                        <p>Configure which tabs are enabled and basic appearance</p>

                        <div className="space-y-4">
                            <div className="feedlane-form__field">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="enable_newsfeed"
                                        checked={settings.enable_newsfeed}
                                        onChange={handleInputChange}
                                        className="mr-2"
                                    />
                                    Enable Newsfeed Tab
                                </label>
                                <div className="description">Show the newsfeed tab in the sidebar</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="enable_ideas"
                                        checked={settings.enable_ideas}
                                        onChange={handleInputChange}
                                        className="mr-2"
                                    />
                                    Enable Ideas Tab
                                </label>
                                <div className="description">Show the ideas submission tab in the sidebar</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="enable_roadmap"
                                        checked={settings.enable_roadmap}
                                        onChange={handleInputChange}
                                        className="mr-2"
                                    />
                                    Enable Roadmap Tab
                                </label>
                                <div className="description">Show the roadmap tab in the sidebar</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="enable_guest_submissions"
                                        checked={settings.enable_guest_submissions}
                                        onChange={handleInputChange}
                                        className="mr-2"
                                    />
                                    Enable Guest Submissions
                                </label>
                                <div className="description">Allow non-logged-in users to submit feedback and ideas</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label htmlFor="sidebar_position">Sidebar Position</label>
                                <select
                                    id="sidebar_position"
                                    name="sidebar_position"
                                    value={settings.sidebar_position}
                                    onChange={handleInputChange}
                                >
                                    <option value="left">Left</option>
                                    <option value="right">Right</option>
                                </select>
                                <div className="description">Choose which side of the screen the sidebar appears on</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label htmlFor="primary_color">Primary Color</label>
                                <input
                                    type="color"
                                    id="primary_color"
                                    name="primary_color"
                                    value={settings.primary_color}
                                    onChange={handleInputChange}
                                />
                                <div className="description">Choose the primary color for the sidebar and buttons</div>
                            </div>
                        </div>
                    </div>

                    {/* Firebase Settings */}
                    <div className="feedlane-form__section">
                        <h3>Firebase Configuration</h3>
                        <p>Configure Firebase for real-time comments functionality</p>

                        <div className="space-y-4">
                            <div className="feedlane-form__field">
                                <label htmlFor="firebase_config">Firebase Configuration JSON</label>
                                <textarea
                                    id="firebase_config"
                                    name="firebase_config"
                                    value={settings.firebase_config}
                                    onChange={handleInputChange}
                                    rows="6"
                                    placeholder='{"apiKey": "...", "authDomain": "...", "projectId": "..."}'
                                />
                                <div className="description">Paste your Firebase configuration JSON here for real-time comments</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label htmlFor="firebase_webhook_secret">Webhook Secret</label>
                                <input
                                    type="password"
                                    id="firebase_webhook_secret"
                                    name="firebase_webhook_secret"
                                    value={settings.firebase_webhook_secret}
                                    onChange={handleInputChange}
                                />
                                <div className="description">Secret key for Firebase webhook authentication</div>
                            </div>
                        </div>
                    </div>

                    <div className="feedlane-form__actions">
                        <button
                            type="submit"
                            disabled={saving}
                            className="feedlane-btn feedlane-btn--primary"
                        >
                            {saving ? 'Saving...' : 'Save Settings'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default Settings;
