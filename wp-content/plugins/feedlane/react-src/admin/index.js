import React from 'react';
import { createRoot } from 'react-dom/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import Dashboard from './components/Dashboard';
import Settings from './components/Settings';
import Analytics from './components/Analytics';
import IdeasManagement from './components/IdeasManagement';
import './scss/admin.scss';

// Create a client
const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            retry: 2,
            staleTime: 5 * 60 * 1000, // 5 minutes
        },
    },
});

// Main App component
const App = ({ page }) => {
    const renderPage = () => {
        switch (page) {
            case 'dashboard':
                return <Dashboard />;
            case 'settings':
                return <Settings />;
            case 'analytics':
                return <Analytics />;
            case 'ideas':
                return <IdeasManagement />;
            default:
                return <Dashboard />;
        }
    };

    return (
        <QueryClientProvider client={queryClient}>
            {renderPage()}
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        iconTheme: {
                            primary: '#4ade80',
                            secondary: '#fff',
                        },
                    },
                    error: {
                        duration: 5000,
                        iconTheme: {
                            primary: '#ef4444',
                            secondary: '#fff',
                        },
                    },
                }}
            />
        </QueryClientProvider>
    );
};

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    // Dashboard
    const dashboardContainer = document.getElementById('feedlane-admin-dashboard');
    if (dashboardContainer) {
        const root = createRoot(dashboardContainer);
        root.render(<App page="dashboard" />);
    }
    
    // Settings
    const settingsContainer = document.getElementById('feedlane-admin-settings');
    if (settingsContainer) {
        const root = createRoot(settingsContainer);
        root.render(<App page="settings" />);
    }
    
    // Analytics
    const analyticsContainer = document.getElementById('feedlane-admin-analytics');
    if (analyticsContainer) {
        const root = createRoot(analyticsContainer);
        root.render(<App page="analytics" />);
    }
    
    // Ideas Management
    const ideasContainer = document.getElementById('feedlane-admin-ideas');
    if (ideasContainer) {
        const root = createRoot(ideasContainer);
        root.render(<App page="ideas" />);
    }
});
