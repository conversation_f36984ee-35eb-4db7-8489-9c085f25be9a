import apiClient from './client';

/**
 * Fetch ideas
 */
export const fetchIdeas = async (params = {}) => {
    const defaultParams = {
        page: 1,
        per_page: 10,
        orderby: 'votes',
    };
    
    const queryParams = { ...defaultParams, ...params };
    
    return apiClient.get('/ideas', queryParams);
};

/**
 * Fetch single idea
 */
export const fetchIdea = async (ideaId) => {
    return apiClient.get(`/ideas/${ideaId}`);
};

/**
 * Submit new idea
 */
export const submitIdea = async (ideaData) => {
    // If ideaData is FormData (for file upload), use postFormData
    if (ideaData instanceof FormData) {
        return apiClient.postFormData('/ideas', ideaData);
    }
    
    return apiClient.post('/ideas', ideaData);
};

/**
 * Vote on idea
 */
export const voteIdea = async (ideaId) => {
    return apiClient.post(`/ideas/${ideaId}/vote`);
};
