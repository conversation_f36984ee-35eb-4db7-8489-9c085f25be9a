import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';
import VotingButton from './VotingButton';
import { fetchIdeas } from '../api/ideas';

const RoadmapTab = () => {
    const [expandedSections, setExpandedSections] = useState({
        'under-review': true,
        'planned': true,
        'in-progress': true,
        'completed': false
    });
    
    const roadmapStatuses = [
        { slug: 'under-review', name: 'Under Review', icon: '🔍', color: 'yellow' },
        { slug: 'planned', name: 'Planned', icon: '📋', color: 'blue' },
        { slug: 'in-progress', name: 'In Progress', icon: '⚡', color: 'orange' },
        { slug: 'completed', name: 'Completed', icon: '✅', color: 'green' }
    ];
    
    const toggleSection = (status) => {
        setExpandedSections(prev => ({
            ...prev,
            [status]: !prev[status]
        }));
    };
    
    return (
        <div className="feedlane-roadmap">
            <div className="feedlane-roadmap__header">
                <h3>Product Roadmap</h3>
                <p>Track the progress of ideas and features</p>
            </div>
            
            <div className="feedlane-roadmap__sections">
                {roadmapStatuses.map(status => (
                    <RoadmapSection
                        key={status.slug}
                        status={status}
                        isExpanded={expandedSections[status.slug]}
                        onToggle={() => toggleSection(status.slug)}
                    />
                ))}
            </div>
        </div>
    );
};

const RoadmapSection = ({ status, isExpanded, onToggle }) => {
    const {
        data: ideasData,
        isLoading,
        error
    } = useQuery({
        queryKey: ['roadmap-ideas', status.slug],
        queryFn: () => fetchIdeas({ 
            status: status.slug, 
            per_page: 50,
            orderby: 'votes'
        }),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
    
    const ideas = ideasData?.ideas || [];
    const totalCount = ideasData?.pagination?.total || 0;
    
    return (
        <div className={`feedlane-roadmap-section feedlane-roadmap-section--${status.color}`}>
            {/* Section Header */}
            <button
                onClick={onToggle}
                className="feedlane-roadmap-section__header"
            >
                <div className="feedlane-roadmap-section__title">
                    <span className="feedlane-roadmap-section__icon">
                        {status.icon}
                    </span>
                    <span className="feedlane-roadmap-section__name">
                        {status.name}
                    </span>
                    <span className="feedlane-roadmap-section__count">
                        ({totalCount})
                    </span>
                </div>
                <div className="feedlane-roadmap-section__toggle">
                    {isExpanded ? <FiChevronUp size={20} /> : <FiChevronDown size={20} />}
                </div>
            </button>
            
            {/* Section Content */}
            {isExpanded && (
                <div className="feedlane-roadmap-section__content">
                    {isLoading ? (
                        <div className="feedlane-roadmap-section__loading">
                            <div className="feedlane-spinner"></div>
                            <span>Loading {status.name.toLowerCase()}...</span>
                        </div>
                    ) : error ? (
                        <div className="feedlane-roadmap-section__error">
                            <p>Failed to load items</p>
                        </div>
                    ) : ideas.length === 0 ? (
                        <div className="feedlane-roadmap-section__empty">
                            <p>No items in {status.name.toLowerCase()}</p>
                        </div>
                    ) : (
                        <div className="feedlane-roadmap-section__items">
                            {ideas.map(idea => (
                                <RoadmapItem key={idea.id} idea={idea} />
                            ))}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

const RoadmapItem = ({ idea }) => {
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };
    
    return (
        <article className="feedlane-roadmap-item">
            <div className="feedlane-roadmap-item__content">
                <h4 className="feedlane-roadmap-item__title">
                    {idea.title}
                </h4>
                
                <p className="feedlane-roadmap-item__excerpt">
                    {idea.excerpt}
                </p>
                
                <div className="feedlane-roadmap-item__meta">
                    <span className="feedlane-roadmap-item__date">
                        {formatDate(idea.created_at)}
                    </span>
                    
                    {idea.categories.length > 0 && (
                        <span className="feedlane-roadmap-item__category">
                            {idea.categories[0].name}
                        </span>
                    )}
                </div>
            </div>
            
            <div className="feedlane-roadmap-item__actions">
                <VotingButton 
                    ideaId={idea.id}
                    hasVoted={idea.has_voted}
                    voteCount={idea.vote_count}
                />
            </div>
        </article>
    );
};

export default RoadmapTab;
