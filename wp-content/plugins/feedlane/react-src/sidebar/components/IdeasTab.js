import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { FiPlus, FiFilter } from 'react-icons/fi';
import IdeaForm from './IdeaForm';
import VotingButton from './VotingButton';
import { fetchIdeas } from '../api/ideas';

const IdeasTab = () => {
    const [showForm, setShowForm] = useState(false);
    const [filters, setFilters] = useState({
        category: '',
        orderby: 'votes'
    });
    
    const {
        data: ideasData,
        isLoading,
        error,
        refetch
    } = useQuery({
        queryKey: ['ideas', filters],
        queryFn: () => fetchIdeas({ ...filters, per_page: 20 }),
        staleTime: 3 * 60 * 1000, // 3 minutes
    });
    
    const handleFormSuccess = () => {
        setShowForm(false);
        refetch();
    };
    
    if (isLoading) {
        return (
            <div className="feedlane-loading">
                <div className="feedlane-loading__spinner"></div>
                <p>Loading ideas...</p>
            </div>
        );
    }
    
    if (error) {
        return (
            <div className="feedlane-error">
                <p>Failed to load ideas.</p>
                <button 
                    onClick={() => refetch()}
                    className="feedlane-button feedlane-button--secondary"
                >
                    Try Again
                </button>
            </div>
        );
    }
    
    const ideas = ideasData?.ideas || [];
    
    return (
        <div className="feedlane-ideas">
            {/* Header */}
            <div className="feedlane-ideas__header">
                <h3>Ideas & Suggestions</h3>
                <p>Share your ideas and vote on others</p>
                
                <button
                    onClick={() => setShowForm(!showForm)}
                    className="feedlane-button feedlane-button--primary"
                >
                    <FiPlus size={16} />
                    Submit Idea
                </button>
            </div>
            
            {/* Idea Form */}
            {showForm && (
                <div className="feedlane-ideas__form">
                    <IdeaForm 
                        onSuccess={handleFormSuccess}
                        onCancel={() => setShowForm(false)}
                    />
                </div>
            )}
            
            {/* Filters */}
            <div className="feedlane-ideas__filters">
                <div className="feedlane-filter-group">
                    <FiFilter size={16} />
                    <select
                        value={filters.orderby}
                        onChange={(e) => setFilters(prev => ({ ...prev, orderby: e.target.value }))}
                        className="feedlane-select"
                    >
                        <option value="votes">Most Voted</option>
                        <option value="date">Newest</option>
                        <option value="title">Alphabetical</option>
                    </select>
                </div>
            </div>
            
            {/* Ideas List */}
            <div className="feedlane-ideas__list">
                {ideas.length === 0 ? (
                    <div className="feedlane-empty">
                        <div className="feedlane-empty__icon">💡</div>
                        <h3>No Ideas Yet</h3>
                        <p>Be the first to share your brilliant idea!</p>
                    </div>
                ) : (
                    ideas.map(idea => (
                        <IdeaCard key={idea.id} idea={idea} />
                    ))
                )}
            </div>
        </div>
    );
};

const IdeaCard = ({ idea }) => {
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };
    
    return (
        <article className="feedlane-idea-card">
            {/* Header */}
            <header className="feedlane-idea-card__header">
                <h4 className="feedlane-idea-card__title">{idea.title}</h4>
                <div className="feedlane-idea-card__meta">
                    <span className="feedlane-idea-card__date">
                        {formatDate(idea.created_at)}
                    </span>
                    {idea.categories.length > 0 && (
                        <span className="feedlane-idea-card__category">
                            {idea.categories[0].name}
                        </span>
                    )}
                    {idea.status && (
                        <span className={`feedlane-idea-card__status feedlane-status--${idea.status.slug}`}>
                            {idea.status.name}
                        </span>
                    )}
                </div>
            </header>
            
            {/* Content */}
            <div className="feedlane-idea-card__content">
                <p className="feedlane-idea-card__excerpt">
                    {idea.excerpt}
                </p>
                
                {idea.image && (
                    <div className="feedlane-idea-card__image">
                        <img src={idea.image} alt={idea.title} loading="lazy" />
                    </div>
                )}
            </div>
            
            {/* Footer */}
            <footer className="feedlane-idea-card__footer">
                <div className="feedlane-idea-card__voting">
                    <VotingButton ideaId={idea.id} />
                    <span className="feedlane-idea-card__vote-count">
                        {idea.vote_count} {idea.vote_count === 1 ? 'vote' : 'votes'}
                    </span>
                </div>
                
                <button className="feedlane-idea-card__view-details">
                    View Details
                </button>
            </footer>
        </article>
    );
};

export default IdeasTab;
