import React from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FiChevronUp } from 'react-icons/fi';
import toast from 'react-hot-toast';
import { voteIdea } from '../api/ideas';

const VotingButton = ({ ideaId, hasVoted = false, voteCount = 0 }) => {
    const queryClient = useQueryClient();
    
    const voteMutation = useMutation({
        mutationFn: () => voteIdea(ideaId),
        onSuccess: (data) => {
            // Update the ideas query cache
            queryClient.setQueryData(['ideas'], (oldData) => {
                if (!oldData) return oldData;
                
                return {
                    ...oldData,
                    ideas: oldData.ideas.map(idea => 
                        idea.id === ideaId 
                            ? { 
                                ...idea, 
                                vote_count: data.vote_count,
                                has_voted: true 
                              }
                            : idea
                    )
                };
            });
            
            // Also update individual idea cache if it exists
            queryClient.setQueryData(['idea', ideaId], (oldData) => {
                if (!oldData) return oldData;
                
                return {
                    ...oldData,
                    vote_count: data.vote_count,
                    has_voted: true
                };
            });
            
            toast.success('Vote recorded!');
        },
        onError: (error) => {
            const message = error.response?.data?.message || 'Failed to vote';
            toast.error(message);
        },
    });
    
    const handleVote = () => {
        if (hasVoted) {
            toast.info('You have already voted on this idea');
            return;
        }
        
        voteMutation.mutate();
    };
    
    return (
        <button
            onClick={handleVote}
            disabled={voteMutation.isPending || hasVoted}
            className={`feedlane-vote-button ${hasVoted ? 'feedlane-vote-button--voted' : ''}`}
            title={hasVoted ? 'You have voted' : 'Vote for this idea'}
        >
            {voteMutation.isPending ? (
                <div className="feedlane-spinner feedlane-spinner--small"></div>
            ) : (
                <FiChevronUp size={16} />
            )}
            <span className="feedlane-vote-button__count">
                {voteCount}
            </span>
        </button>
    );
};

export default VotingButton;
