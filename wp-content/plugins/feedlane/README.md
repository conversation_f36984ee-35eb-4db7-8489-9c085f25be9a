# Feedlane - WordPress Feedback Plugin

A React-powered WordPress plugin that adds a floating feedback sidebar with Newsfeed, Ideas, and Roadmap tabs—similar to GetBeamer. It supports guest submissions, real-time comments via Firebase, and admin moderation.

## Features

### 🧩 Frontend Sidebar Widget
- Floating button on the left or right edge of the screen
- Sliding panel with 3 React tabs: Newsfeed, Ideas, Roadmap
- TailwindCSS-styled and responsive design
- Embed via shortcode, Gutenberg block, or Elementor widget

### 📢 Newsfeed Tab
- Display `feedlane_posts` with title and content
- Emoji-style reaction icons (👍 😐 😡)
- Feedback form with guest user support
- Post views and interaction analytics
- Cookie/localStorage-based reaction protection

### 💡 Ideas Tab
- Idea submission form with file upload support
- Categories: Improvement, Feature Request, Bug, Changelog, Feedback
- Admin approval workflow (pending → published)
- Upvoting system with duplicate prevention
- Real-time comments via Firebase integration

### 🚧 Roadmap Tab
- 4 collapsible sections: Under Review, Planned, In Progress, Completed
- Ideas filtered by roadmap status
- Upvoting on roadmap items
- Admin-managed status updates

### 🛠️ Admin Panel
- React-based settings page
- Firebase configuration
- Analytics dashboard
- Ideas approval interface
- Roadmap status management

## Installation

1. **Upload the plugin files** to `/wp-content/plugins/feedlane/`
2. **Install dependencies**:
   ```bash
   cd wp-content/plugins/feedlane
   composer install
   npm install
   ```
3. **Build assets**:
   ```bash
   npm run build
   ```
4. **Activate the plugin** through the WordPress admin panel
5. **Configure settings** at `Admin → Feedlane → Settings`

## Usage

### Floating Sidebar (Automatic)
The floating sidebar appears automatically on all pages once the plugin is activated. Configure position and colors in the admin settings.

### Shortcodes

#### Full Sidebar
```php
[feedlane_sidebar position="left" height="600px" primary_color="#0ea5e9"]
```

#### Newsfeed Only
```php
[feedlane_newsfeed limit="5" show_reactions="true" show_feedback_form="true"]
```

#### Ideas Only
```php
[feedlane_ideas limit="10" category="feature-request" show_form="true" orderby="votes"]
```

#### Roadmap Only
```php
[feedlane_roadmap]
```

### Gutenberg Block
1. Add a new block in the editor
2. Search for "Feedlane Sidebar"
3. Configure position, height, and colors in the block settings

### Elementor Widget
1. Drag the "Feedlane Sidebar" widget to your page
2. Configure settings in the widget panel
3. Customize colors and positioning

## Configuration

### General Settings
- **Enable Newsfeed Tab**: Show/hide the newsfeed tab
- **Enable Ideas Tab**: Show/hide the ideas submission tab
- **Enable Roadmap Tab**: Show/hide the roadmap tab
- **Enable Guest Submissions**: Allow non-logged-in users to submit
- **Sidebar Position**: Left or right side of the screen
- **Primary Color**: Main color for buttons and accents

### Firebase Configuration (Optional)
For real-time comments on ideas:

1. Create a Firebase project at https://console.firebase.google.com/
2. Get your Firebase configuration JSON
3. Paste it in `Admin → Feedlane → Settings → Firebase Configuration`
4. Set up a webhook secret for Firebase Cloud Functions

## Custom Post Types

### feedlane_posts
- **Purpose**: Newsfeed items/announcements
- **Fields**: Title, Content, Featured Image, Excerpt
- **Admin**: `Admin → Feedlane → Newsfeed`

### feedlane_ideas
- **Purpose**: User-submitted ideas and suggestions
- **Fields**: Title, Content, Category, Submitter Info, Featured Image
- **Status**: Pending (requires approval) → Published
- **Admin**: `Admin → Feedlane → Ideas Management`

## Taxonomies

### idea_category
- **Values**: Improvement, Feature Request, Bug, Changelog, Feedback
- **Usage**: Categorize submitted ideas

### roadmap_status
- **Values**: Under Review, Planned, In Progress, Completed
- **Usage**: Track idea progress through development stages

## Database Tables

### feedlane_reactions
- Stores emoji reactions on posts
- Prevents duplicate reactions per user/IP

### feedlane_feedback
- Stores feedback comments on newsfeed posts
- Supports both registered and guest users

### feedlane_votes
- Stores upvotes on ideas
- Prevents duplicate votes per user/IP

### feedlane_analytics
- Tracks user interactions for analytics
- Events: post views, reactions, votes, submissions

## REST API Endpoints

### Feedback
- `POST /wp-json/feedlane/v1/feedback` - Submit feedback
- `GET /wp-json/feedlane/v1/feedback/{post_id}` - Get feedback

### Ideas
- `POST /wp-json/feedlane/v1/ideas` - Submit idea
- `GET /wp-json/feedlane/v1/ideas` - Get ideas list
- `GET /wp-json/feedlane/v1/ideas/{id}` - Get single idea
- `POST /wp-json/feedlane/v1/ideas/{id}/vote` - Vote on idea

### Reactions
- `POST /wp-json/feedlane/v1/reactions` - Add reaction
- `GET /wp-json/feedlane/v1/reactions/{post_id}` - Get reactions
- `DELETE /wp-json/feedlane/v1/reactions/{post_id}/{type}` - Remove reaction

### Comments (Firebase)
- `GET /wp-json/feedlane/v1/firebase-config` - Get Firebase config
- `POST /wp-json/feedlane/v1/comments/validate` - Validate comment
- `POST /wp-json/feedlane/v1/comments/sync` - Sync from Firebase

## Security Features

- **Nonce verification** on all REST API endpoints
- **Input sanitization** and validation
- **Capability checks** for admin functions
- **Rate limiting** via cookie/localStorage for guest users
- **File upload validation** for idea images
- **XSS protection** with proper escaping

## Development

### Build Process
```bash
# Development build with watch
npm run start

# Production build
npm run build

# Lint JavaScript
npm run lint:js

# Lint CSS
npm run lint:css
```

### File Structure
```
feedlane/
├── feedlane.php              # Main plugin file
├── composer.json             # PHP dependencies
├── package.json              # Node.js dependencies
├── webpack.config.js         # Build configuration
├── tailwind.config.js        # TailwindCSS configuration
├── includes/                 # PHP classes
│   ├── Plugin.php           # Main plugin class
│   ├── Core/                # Core functionality
│   ├── REST/                # REST API endpoints
│   ├── Utils/               # Utility classes
│   ├── Database/            # Database schema
│   └── Widgets/             # Elementor/Gutenberg widgets
├── react-src/               # React source files
│   ├── sidebar/             # Main sidebar components
│   ├── admin/               # Admin panel components
│   └── blocks/              # Gutenberg blocks
├── assets/                  # Built assets (generated)
├── views/                   # PHP template files
└── languages/               # Translation files
```

## Requirements

- **WordPress**: 5.0+
- **PHP**: 7.4+
- **Node.js**: 16+ (for development)
- **Composer**: 2.0+ (for development)

## License

GPL-3.0+ - See LICENSE file for details

## Support

For support and feature requests, please visit the plugin's GitHub repository or contact the development team.
