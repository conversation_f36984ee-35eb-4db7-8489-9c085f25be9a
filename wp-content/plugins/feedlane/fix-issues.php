<?php
/**
 * Feedlane Plugin Issue Fixer
 * 
 * Run this script to fix common issues with the Feedlane plugin
 */

// Include WordPress
require_once '../../../wp-config.php';

echo "=== Feedlane Plugin Issue Fixer ===\n\n";

// Step 1: Check if plugin exists
$plugin_file = 'feedlane/feedlane.php';
$plugin_path = WP_PLUGIN_DIR . '/' . $plugin_file;

if (!file_exists($plugin_path)) {
    echo "❌ Plugin file not found: $plugin_path\n";
    exit;
}

echo "✅ Plugin file exists\n";

// Step 2: Check autoloader
$autoloader = WP_PLUGIN_DIR . '/feedlane/vendor/autoload.php';
if (!file_exists($autoloader)) {
    echo "❌ Autoloader not found. Running composer install...\n";
    $output = shell_exec('cd ' . WP_PLUGIN_DIR . '/feedlane && composer install 2>&1');
    echo $output . "\n";
} else {
    echo "✅ Autoloader exists\n";
}

// Step 3: Check if plugin is active
if (!is_plugin_active($plugin_file)) {
    echo "🔄 Activating plugin...\n";
    $result = activate_plugin($plugin_file);
    
    if (is_wp_error($result)) {
        echo "❌ Activation failed: " . $result->get_error_message() . "\n";
        
        // Try to get more details
        echo "\n=== Detailed Error Information ===\n";
        
        // Check for syntax errors
        $syntax_check = shell_exec("php -l $plugin_path 2>&1");
        echo "Syntax check: $syntax_check\n";
        
        // Check if classes can be loaded
        try {
            require_once $autoloader;
            if (class_exists('WPDeveloper\Feedlane\Plugin')) {
                echo "✅ Main plugin class can be loaded\n";
            } else {
                echo "❌ Main plugin class cannot be loaded\n";
            }
        } catch (Exception $e) {
            echo "❌ Error loading classes: " . $e->getMessage() . "\n";
        }
        
        exit;
    } else {
        echo "✅ Plugin activated successfully\n";
    }
} else {
    echo "✅ Plugin is already active\n";
}

// Step 4: Force refresh hooks
echo "🔄 Refreshing WordPress hooks...\n";

// Trigger init hook manually to register post types and taxonomies
do_action('init');

// Step 5: Check post types
echo "\n=== Post Types Check ===\n";
$post_types = ['feedlane_posts', 'feedlane_ideas'];
foreach ($post_types as $post_type) {
    if (post_type_exists($post_type)) {
        echo "✅ $post_type: REGISTERED\n";
    } else {
        echo "❌ $post_type: NOT FOUND\n";
        
        // Try to register manually
        echo "🔄 Attempting manual registration...\n";
        
        if ($post_type === 'feedlane_posts') {
            register_post_type('feedlane_posts', [
                'labels' => [
                    'name' => 'Newsfeed Posts',
                    'singular_name' => 'Newsfeed Post',
                ],
                'public' => false,
                'show_ui' => true,
                'show_in_menu' => 'feedlane',
                'supports' => ['title', 'editor', 'thumbnail'],
            ]);
        } elseif ($post_type === 'feedlane_ideas') {
            register_post_type('feedlane_ideas', [
                'labels' => [
                    'name' => 'Ideas',
                    'singular_name' => 'Idea',
                ],
                'public' => false,
                'show_ui' => true,
                'show_in_menu' => 'feedlane',
                'supports' => ['title', 'editor', 'thumbnail'],
            ]);
        }
        
        if (post_type_exists($post_type)) {
            echo "✅ $post_type: MANUALLY REGISTERED\n";
        } else {
            echo "❌ $post_type: MANUAL REGISTRATION FAILED\n";
        }
    }
}

// Step 6: Check taxonomies
echo "\n=== Taxonomies Check ===\n";
$taxonomies = ['idea_category', 'roadmap_status'];
foreach ($taxonomies as $taxonomy) {
    if (taxonomy_exists($taxonomy)) {
        echo "✅ $taxonomy: REGISTERED\n";
    } else {
        echo "❌ $taxonomy: NOT FOUND\n";
        
        // Try to register manually
        echo "🔄 Attempting manual registration...\n";
        
        if ($taxonomy === 'idea_category') {
            register_taxonomy('idea_category', ['feedlane_ideas'], [
                'labels' => [
                    'name' => 'Idea Categories',
                    'singular_name' => 'Idea Category',
                ],
                'hierarchical' => true,
                'show_ui' => true,
            ]);
        } elseif ($taxonomy === 'roadmap_status') {
            register_taxonomy('roadmap_status', ['feedlane_ideas'], [
                'labels' => [
                    'name' => 'Roadmap Status',
                    'singular_name' => 'Status',
                ],
                'hierarchical' => false,
                'show_ui' => true,
            ]);
        }
        
        if (taxonomy_exists($taxonomy)) {
            echo "✅ $taxonomy: MANUALLY REGISTERED\n";
        } else {
            echo "❌ $taxonomy: MANUAL REGISTRATION FAILED\n";
        }
    }
}

// Step 7: Check assets
echo "\n=== Assets Check ===\n";
$assets = [
    'js/feedlane-sidebar.min.js',
    'css/feedlane-sidebar.min.css',
    'js/feedlane-admin.min.js',
    'css/feedlane-admin.min.css',
];

$assets_dir = WP_PLUGIN_DIR . '/feedlane/assets/';
$missing_assets = [];

foreach ($assets as $asset) {
    $file_path = $assets_dir . $asset;
    if (file_exists($file_path)) {
        echo "✅ $asset: EXISTS (" . round(filesize($file_path)/1024, 1) . "KB)\n";
    } else {
        echo "❌ $asset: NOT FOUND\n";
        $missing_assets[] = $asset;
    }
}

if (!empty($missing_assets)) {
    echo "\n🔄 Building missing assets...\n";
    $build_output = shell_exec('cd ' . WP_PLUGIN_DIR . '/feedlane && npm run build 2>&1');
    echo $build_output . "\n";
    
    // Check again
    foreach ($missing_assets as $asset) {
        $file_path = $assets_dir . $asset;
        if (file_exists($file_path)) {
            echo "✅ $asset: NOW EXISTS\n";
        } else {
            echo "❌ $asset: STILL MISSING\n";
        }
    }
}

// Step 8: Create manual admin menu if needed
echo "\n=== Admin Menu Fix ===\n";

// Add a temporary admin menu
add_action('admin_menu', function() {
    add_menu_page(
        'Feedlane (Fixed)',
        'Feedlane (Fixed)',
        'manage_options',
        'feedlane-fixed',
        function() {
            echo '<div class="wrap">';
            echo '<h1>Feedlane Plugin (Manual Fix)</h1>';
            echo '<p>This is a manually created admin menu for Feedlane.</p>';
            echo '<p><a href="' . admin_url('admin.php?page=feedlane') . '" class="button button-primary">Try Original Feedlane Menu</a></p>';
            echo '<p><a href="' . admin_url('admin.php?page=feedlane-debug') . '" class="button">View Debug Info</a></p>';
            echo '</div>';
        },
        'dashicons-feedback',
        30
    );
});

echo "✅ Manual admin menu created\n";

// Step 9: Final status
echo "\n=== Final Status ===\n";
echo "Plugin File: " . ($plugin_file ? "✅ EXISTS" : "❌ MISSING") . "\n";
echo "Plugin Active: " . (is_plugin_active($plugin_file) ? "✅ YES" : "❌ NO") . "\n";
echo "Classes Loaded: " . (class_exists('WPDeveloper\Feedlane\Plugin') ? "✅ YES" : "❌ NO") . "\n";
echo "Post Types: " . (post_type_exists('feedlane_posts') && post_type_exists('feedlane_ideas') ? "✅ REGISTERED" : "❌ MISSING") . "\n";

echo "\n=== Next Steps ===\n";
echo "1. Go to WordPress Admin\n";
echo "2. Look for 'Feedlane' or 'Feedlane (Fixed)' in the admin menu\n";
echo "3. If still not working, check the debug plugin: Admin → Feedlane Debug\n";
echo "4. Check error logs in /wp-content/debug.log\n";

echo "\n=== Fix Complete ===\n";
?>
