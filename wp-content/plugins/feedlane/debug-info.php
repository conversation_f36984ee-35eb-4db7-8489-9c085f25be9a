<?php
/**
 * Debug info page - Add this as a temporary admin page to debug
 */

// Add this to functions.php temporarily or create a simple plugin
add_action('admin_menu', function() {
    add_menu_page(
        'Feedlane Debug',
        'Feedlane Debug',
        'manage_options',
        'feedlane-debug',
        'feedlane_debug_page',
        'dashicons-admin-tools',
        99
    );
});

function feedlane_debug_page() {
    ?>
    <div class="wrap">
        <h1>Feedlane Debug Information</h1>
        
        <h2>Plugin Status</h2>
        <table class="widefat">
            <tr>
                <td><strong>Plugin Active:</strong></td>
                <td><?php echo is_plugin_active('feedlane/feedlane.php') ? 'YES' : 'NO'; ?></td>
            </tr>
            <tr>
                <td><strong>Main Class Exists:</strong></td>
                <td><?php echo class_exists('WPDeveloper\Feedlane\Plugin') ? 'YES' : 'NO'; ?></td>
            </tr>
            <tr>
                <td><strong>Plugin Instance:</strong></td>
                <td><?php echo function_exists('feedlane') ? 'YES' : 'NO'; ?></td>
            </tr>
        </table>
        
        <h2>Post Types</h2>
        <table class="widefat">
            <?php
            $post_types = ['feedlane_posts', 'feedlane_ideas'];
            foreach ($post_types as $post_type) {
                echo '<tr>';
                echo '<td><strong>' . $post_type . ':</strong></td>';
                echo '<td>' . (post_type_exists($post_type) ? 'REGISTERED' : 'NOT FOUND') . '</td>';
                echo '</tr>';
            }
            ?>
        </table>
        
        <h2>Taxonomies</h2>
        <table class="widefat">
            <?php
            $taxonomies = ['idea_category', 'roadmap_status'];
            foreach ($taxonomies as $taxonomy) {
                echo '<tr>';
                echo '<td><strong>' . $taxonomy . ':</strong></td>';
                echo '<td>' . (taxonomy_exists($taxonomy) ? 'REGISTERED' : 'NOT FOUND') . '</td>';
                echo '</tr>';
            }
            ?>
        </table>
        
        <h2>Admin Menus</h2>
        <table class="widefat">
            <?php
            global $menu, $submenu;
            $feedlane_found = false;
            
            if (isset($menu)) {
                foreach ($menu as $menu_item) {
                    if (isset($menu_item[2]) && strpos($menu_item[2], 'feedlane') !== false) {
                        $feedlane_found = true;
                        echo '<tr>';
                        echo '<td><strong>Menu:</strong></td>';
                        echo '<td>' . $menu_item[0] . ' (' . $menu_item[2] . ')</td>';
                        echo '</tr>';
                    }
                }
            }
            
            if (!$feedlane_found) {
                echo '<tr><td colspan="2">No Feedlane menus found</td></tr>';
            }
            
            if (isset($submenu['feedlane'])) {
                foreach ($submenu['feedlane'] as $submenu_item) {
                    echo '<tr>';
                    echo '<td><strong>Submenu:</strong></td>';
                    echo '<td>' . $submenu_item[0] . ' (' . $submenu_item[2] . ')</td>';
                    echo '</tr>';
                }
            }
            ?>
        </table>
        
        <h2>Hooks Debug</h2>
        <table class="widefat">
            <tr>
                <td><strong>Current Hook:</strong></td>
                <td><?php echo current_action(); ?></td>
            </tr>
            <tr>
                <td><strong>Init Hook Fired:</strong></td>
                <td><?php echo did_action('init') ? 'YES (' . did_action('init') . ' times)' : 'NO'; ?></td>
            </tr>
            <tr>
                <td><strong>Admin Menu Hook Fired:</strong></td>
                <td><?php echo did_action('admin_menu') ? 'YES (' . did_action('admin_menu') . ' times)' : 'NO'; ?></td>
            </tr>
        </table>
        
        <h2>Error Log</h2>
        <?php
        $error_log = ini_get('error_log');
        if ($error_log && file_exists($error_log)) {
            $log_content = file_get_contents($error_log);
            $feedlane_errors = array_filter(explode("\n", $log_content), function($line) {
                return strpos(strtolower($line), 'feedlane') !== false;
            });
            
            if (!empty($feedlane_errors)) {
                echo '<pre style="background: #f1f1f1; padding: 10px; max-height: 300px; overflow-y: auto;">';
                echo esc_html(implode("\n", array_slice($feedlane_errors, -10))); // Last 10 errors
                echo '</pre>';
            } else {
                echo '<p>No Feedlane-related errors found in error log.</p>';
            }
        } else {
            echo '<p>Error log not accessible.</p>';
        }
        ?>
        
        <h2>Actions</h2>
        <p>
            <a href="<?php echo admin_url('plugins.php'); ?>" class="button">Go to Plugins</a>
            <a href="<?php echo admin_url('admin.php?page=feedlane'); ?>" class="button button-primary">Try Feedlane Dashboard</a>
        </p>
    </div>
    <?php
}
?>
