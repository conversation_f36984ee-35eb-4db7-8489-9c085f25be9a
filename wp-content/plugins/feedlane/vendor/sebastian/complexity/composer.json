{"name": "sebastian/complexity", "description": "Library for calculating the complexity of PHP code units", "type": "library", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues"}, "prefer-stable": true, "require": {"php": ">=7.3", "nikic/php-parser": "^4.18 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "config": {"platform": {"php": "7.3.0"}, "optimize-autoloader": true, "sort-packages": true}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}